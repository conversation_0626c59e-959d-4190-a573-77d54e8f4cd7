-- Criação do banco de dados do Sistema Bolão
-- Execute este script para criar todas as tabelas necessárias

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS usuarios (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    telefone VARCHAR(20),
    senha_hash VARCHAR(255) NOT NULL,
    tipo ENUM('admin', 'usuario', 'cambista') DEFAULT 'usuario',
    status ENUM('ativo', 'inativo', 'bloqueado') DEFAULT 'ativo',
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabela de campeonatos
CREATE TABLE IF NOT EXISTS campeonatos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    pais VARCHAR(100),
    temporada VARCHAR(50),
    status ENUM('ativo', 'encerrado', 'pausado') DEFAULT 'ativo',
    data_inicio DATE,
    data_fim DATE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de times
CREATE TABLE IF NOT EXISTS times (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nome VARCHAR(255) NOT NULL,
    nome_curto VARCHAR(50),
    cidade VARCHAR(100),
    estado VARCHAR(100),
    pais VARCHAR(100),
    logo_url VARCHAR(500),
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de jogos
CREATE TABLE IF NOT EXISTS jogos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    campeonato_id INTEGER NOT NULL,
    time_casa_id INTEGER NOT NULL,
    time_fora_id INTEGER NOT NULL,
    data_jogo TIMESTAMP NOT NULL,
    local_jogo VARCHAR(255),
    rodada INTEGER,
    resultado_casa INTEGER,
    resultado_fora INTEGER,
    status ENUM('agendado', 'ao_vivo', 'finalizado', 'cancelado') DEFAULT 'agendado',
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campeonato_id) REFERENCES campeonatos(id),
    FOREIGN KEY (time_casa_id) REFERENCES times(id),
    FOREIGN KEY (time_fora_id) REFERENCES times(id)
);

-- Tabela de bolões
CREATE TABLE IF NOT EXISTS boloes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    valor_aposta DECIMAL(10,2) NOT NULL,
    premio_total DECIMAL(10,2) NOT NULL,
    max_participantes INTEGER,
    min_acertos INTEGER DEFAULT 3,
    data_inicio TIMESTAMP NOT NULL,
    data_fim TIMESTAMP NOT NULL,
    status ENUM('ativo', 'encerrado', 'em_breve') DEFAULT 'em_breve',
    criado_por INTEGER NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (criado_por) REFERENCES usuarios(id)
);

-- Tabela de jogos do bolão (relaciona bolões com jogos)
CREATE TABLE IF NOT EXISTS bolao_jogos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bolao_id INTEGER NOT NULL,
    jogo_id INTEGER NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bolao_id) REFERENCES boloes(id) ON DELETE CASCADE,
    FOREIGN KEY (jogo_id) REFERENCES jogos(id),
    UNIQUE(bolao_id, jogo_id)
);

-- Tabela de apostas
CREATE TABLE IF NOT EXISTS apostas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    usuario_id INTEGER NOT NULL,
    bolao_id INTEGER NOT NULL,
    valor_total DECIMAL(10,2) NOT NULL,
    status ENUM('pendente', 'paga', 'cancelada') DEFAULT 'pendente',
    data_aposta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_pagamento TIMESTAMP,
    codigo_bilhete VARCHAR(50) UNIQUE,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (bolao_id) REFERENCES boloes(id)
);

-- Tabela de detalhes das apostas (palpites por jogo)
CREATE TABLE IF NOT EXISTS aposta_detalhes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    aposta_id INTEGER NOT NULL,
    jogo_id INTEGER NOT NULL,
    resultado_apostado ENUM('casa', 'empate', 'fora') NOT NULL,
    acertou BOOLEAN DEFAULT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (aposta_id) REFERENCES apostas(id) ON DELETE CASCADE,
    FOREIGN KEY (jogo_id) REFERENCES jogos(id),
    UNIQUE(aposta_id, jogo_id)
);

-- Tabela de pagamentos
CREATE TABLE IF NOT EXISTS pagamentos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    aposta_id INTEGER NOT NULL,
    usuario_id INTEGER NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    metodo_pagamento ENUM('pix', 'cartao', 'dinheiro') NOT NULL,
    status ENUM('pendente', 'aprovado', 'rejeitado', 'cancelado') DEFAULT 'pendente',
    chave_pix VARCHAR(255),
    codigo_transacao VARCHAR(255),
    data_pagamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_confirmacao TIMESTAMP,
    FOREIGN KEY (aposta_id) REFERENCES apostas(id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
);

-- Tabela de prêmios
CREATE TABLE IF NOT EXISTS premios (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bolao_id INTEGER NOT NULL,
    usuario_id INTEGER NOT NULL,
    aposta_id INTEGER NOT NULL,
    acertos INTEGER NOT NULL,
    valor_premio DECIMAL(10,2) NOT NULL,
    status ENUM('pendente', 'pago', 'cancelado') DEFAULT 'pendente',
    data_premio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_pagamento TIMESTAMP,
    FOREIGN KEY (bolao_id) REFERENCES boloes(id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (aposta_id) REFERENCES apostas(id)
);

-- Tabela de configurações do sistema
CREATE TABLE IF NOT EXISTS configuracoes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    chave VARCHAR(100) UNIQUE NOT NULL,
    valor TEXT,
    descricao TEXT,
    tipo ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabela de logs do sistema
CREATE TABLE IF NOT EXISTS logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    usuario_id INTEGER,
    acao VARCHAR(255) NOT NULL,
    tabela_afetada VARCHAR(100),
    registro_id INTEGER,
    dados_anteriores JSON,
    dados_novos JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    data_log TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id)
);

-- Inserir dados iniciais

-- Usuário administrador padrão
INSERT OR IGNORE INTO usuarios (nome, email, senha_hash, tipo) VALUES 
('Administrador', '<EMAIL>', '$2b$10$hash_da_senha_admin123', 'admin');

-- Configurações iniciais do sistema
INSERT OR IGNORE INTO configuracoes (chave, valor, descricao, tipo) VALUES 
('sistema_nome', 'Sistema Bolão', 'Nome do sistema', 'string'),
('pix_chave', '00000000000', 'Chave PIX para recebimentos', 'string'),
('taxa_sistema', '10', 'Taxa do sistema em porcentagem', 'number'),
('max_apostas_usuario', '50', 'Máximo de apostas por usuário por bolão', 'number'),
('tempo_limite_pagamento', '30', 'Tempo limite para pagamento em minutos', 'number'),
('email_suporte', '<EMAIL>', 'Email de suporte', 'string'),
('whatsapp_suporte', '11999999999', 'WhatsApp de suporte', 'string');

-- Campeonatos iniciais
INSERT OR IGNORE INTO campeonatos (nome, descricao, pais, temporada, data_inicio, data_fim) VALUES 
('Brasileirão Série A', 'Campeonato Brasileiro de Futebol - Série A', 'Brasil', '2024', '2024-04-13', '2024-12-08'),
('Copa do Brasil', 'Copa do Brasil de Futebol', 'Brasil', '2024', '2024-02-28', '2024-11-10'),
('Copa Libertadores', 'Copa Libertadores da América', 'América do Sul', '2024', '2024-02-06', '2024-11-30'),
('Copa Sul-Americana', 'Copa Sul-Americana', 'América do Sul', '2024', '2024-03-05', '2024-11-23');

-- Times principais do Brasileirão
INSERT OR IGNORE INTO times (nome, nome_curto, cidade, estado, pais) VALUES 
('Clube de Regatas do Flamengo', 'Flamengo', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Sociedade Esportiva Palmeiras', 'Palmeiras', 'São Paulo', 'SP', 'Brasil'),
('São Paulo Futebol Clube', 'São Paulo', 'São Paulo', 'SP', 'Brasil'),
('Sport Club Corinthians Paulista', 'Corinthians', 'São Paulo', 'SP', 'Brasil'),
('Clube Atlético Mineiro', 'Atlético-MG', 'Belo Horizonte', 'MG', 'Brasil'),
('Cruzeiro Esporte Clube', 'Cruzeiro', 'Belo Horizonte', 'MG', 'Brasil'),
('Grêmio Foot-Ball Porto Alegrense', 'Grêmio', 'Porto Alegre', 'RS', 'Brasil'),
('Sport Club Internacional', 'Internacional', 'Porto Alegre', 'RS', 'Brasil'),
('Botafogo de Futebol e Regatas', 'Botafogo', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Club de Regatas Vasco da Gama', 'Vasco', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Fluminense Football Club', 'Fluminense', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Santos Futebol Clube', 'Santos', 'Santos', 'SP', 'Brasil');

COMMIT;
