#!/usr/bin/env node

/**
 * Script para monitorar e limpar conexões MySQL
 * Execute: node scripts/monitor-mysql-connections.js
 */

import mysql from 'mysql2/promise'
import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

// Configuração do banco de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  charset: 'utf8mb4',
  multipleStatements: true
}

async function monitorConnections() {
  let connection = null
  
  try {
    console.log('🔍 Conectando ao MySQL para monitoramento...')
    connection = await mysql.createConnection(dbConfig)
    
    // Verificar conexões ativas
    const [processlist] = await connection.execute('SHOW PROCESSLIST')
    console.log(`📊 Conexões ativas: ${processlist.length}`)
    
    // Mostrar conexões por usuário
    const connectionsByUser = {}
    processlist.forEach(proc => {
      const user = proc.User || 'unknown'
      connectionsByUser[user] = (connectionsByUser[user] || 0) + 1
    })
    
    console.log('👥 Conexões por usuário:')
    Object.entries(connectionsByUser).forEach(([user, count]) => {
      console.log(`   ${user}: ${count} conexões`)
    })
    
    // Verificar variáveis de conexão
    const [variables] = await connection.execute(`
      SHOW VARIABLES WHERE Variable_name IN (
        'max_connections',
        'max_user_connections',
        'wait_timeout',
        'interactive_timeout'
      )
    `)
    
    console.log('\n⚙️ Configurações MySQL:')
    variables.forEach(variable => {
      console.log(`   ${variable.Variable_name}: ${variable.Value}`)
    })
    
    // Verificar status de conexões
    const [status] = await connection.execute(`
      SHOW STATUS WHERE Variable_name IN (
        'Connections',
        'Max_used_connections',
        'Threads_connected',
        'Threads_running',
        'Aborted_connects',
        'Aborted_clients'
      )
    `)
    
    console.log('\n📈 Status de conexões:')
    status.forEach(stat => {
      console.log(`   ${stat.Variable_name}: ${stat.Value}`)
    })
    
    // Limpar conexões inativas (sleeping) há mais de 5 minutos
    console.log('\n🧹 Limpando conexões inativas...')
    const [sleepingConnections] = await connection.execute(`
      SELECT ID, USER, HOST, DB, COMMAND, TIME, STATE, INFO
      FROM INFORMATION_SCHEMA.PROCESSLIST
      WHERE COMMAND = 'Sleep' AND TIME > 300
      AND USER != 'root'
    `)
    
    if (sleepingConnections.length > 0) {
      console.log(`🔄 Encontradas ${sleepingConnections.length} conexões inativas para limpar`)
      
      for (const proc of sleepingConnections) {
        try {
          await connection.execute(`KILL ${proc.ID}`)
          console.log(`   ✅ Conexão ${proc.ID} (${proc.USER}@${proc.HOST}) terminada`)
        } catch (killError) {
          console.log(`   ❌ Erro ao terminar conexão ${proc.ID}: ${killError.message}`)
        }
      }
    } else {
      console.log('   ✅ Nenhuma conexão inativa encontrada')
    }
    
    console.log('\n✅ Monitoramento concluído')
    
  } catch (error) {
    console.error('❌ Erro no monitoramento:', error.message)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

async function optimizeMySQL() {
  let connection = null
  
  try {
    console.log('\n🔧 Otimizando configurações MySQL...')
    connection = await mysql.createConnection(dbConfig)
    
    // Configurações recomendadas para evitar "Too many connections"
    const optimizations = [
      'SET GLOBAL max_connections = 200',
      'SET GLOBAL max_user_connections = 50',
      'SET GLOBAL wait_timeout = 300',
      'SET GLOBAL interactive_timeout = 300',
      'SET GLOBAL connect_timeout = 60'
    ]
    
    for (const sql of optimizations) {
      try {
        await connection.execute(sql)
        console.log(`   ✅ ${sql}`)
      } catch (error) {
        console.log(`   ⚠️ ${sql} - ${error.message}`)
      }
    }
    
    console.log('✅ Otimizações aplicadas')
    
  } catch (error) {
    console.error('❌ Erro na otimização:', error.message)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// Executar monitoramento
async function main() {
  console.log('🚀 Iniciando monitoramento MySQL...\n')
  
  await monitorConnections()
  await optimizeMySQL()
  
  console.log('\n🎉 Processo concluído!')
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error)
}

export { monitorConnections, optimizeMySQL }
