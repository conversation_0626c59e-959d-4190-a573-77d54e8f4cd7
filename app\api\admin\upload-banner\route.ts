import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir } from "fs/promises"
import { existsSync } from "fs"
import path from "path"

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get("image") as File

    if (!file) {
      return NextResponse.json(
        { success: false, error: "Nenhuma imagem foi enviada" },
        { status: 400 }
      )
    }

    // Validar tipo de arquivo
    if (!file.type.startsWith("image/")) {
      return NextResponse.json(
        { success: false, error: "Apenas arquivos de imagem são permitidos" },
        { status: 400 }
      )
    }

    // Validar tamanho (máximo 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: "A imagem deve ter no máximo 5MB" },
        { status: 400 }
      )
    }

    // Criar diretório se não existir
    const uploadDir = path.join(process.cwd(), "public", "uploads", "banners")
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // Gerar nome único para o arquivo
    const timestamp = Date.now()
    const extension = file.name.split(".").pop()
    const fileName = `banner_${timestamp}.${extension}`
    const filePath = path.join(uploadDir, fileName)

    // Converter arquivo para buffer e salvar
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // Retornar URL da imagem
    const imageUrl = `/uploads/banners/${fileName}`

    console.log("✅ Banner uploaded successfully:", imageUrl)

    return NextResponse.json({
      success: true,
      imageUrl: imageUrl,
      message: "Banner enviado com sucesso"
    })

  } catch (error) {
    console.error("❌ Erro ao fazer upload do banner:", error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
