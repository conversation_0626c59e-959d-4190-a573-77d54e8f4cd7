#!/bin/bash

# Script para iniciar o sistema em produção
echo "🚀 Iniciando Sistema Bolão em Produção..."

# Verificar se a porta 3000 está em uso
echo "🔍 Verificando porta 3000..."
PORT_PID=$(lsof -ti:3000)

if [ ! -z "$PORT_PID" ]; then
    echo "⚠️  Porta 3000 em uso pelo processo $PORT_PID"
    echo "🔄 Finalizando processo anterior..."
    kill -9 $PORT_PID
    sleep 2
fi

# Verificar se Node.js está instalado
if ! command -v node &> /dev/null; then
    echo "❌ Node.js não encontrado. Instalando..."
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verificar versão do Node.js
NODE_VERSION=$(node --version)
echo "✅ Node.js versão: $NODE_VERSION"

# Verificar se PM2 está instalado
if ! command -v pm2 &> /dev/null; then
    echo "📦 Instalando PM2..."
    npm install -g pm2
fi

# Dar permissões aos binários
echo "🔧 Configurando permissões..."
chmod -R u+x node_modules/.bin/

# Verificar se o build existe
if [ ! -d ".next" ]; then
    echo "🏗️  Build não encontrado. Executando build..."
    npm run build
fi

# Parar PM2 se estiver rodando
echo "🛑 Parando processos PM2 anteriores..."
pm2 stop sistema-bolao 2>/dev/null || true
pm2 delete sistema-bolao 2>/dev/null || true

# Iniciar com PM2
echo "🚀 Iniciando aplicação com PM2..."
pm2 start npm --name "sistema-bolao" -- start

# Mostrar status
echo "📊 Status da aplicação:"
pm2 status

# Mostrar logs
echo "📋 Últimos logs:"
pm2 logs sistema-bolao --lines 10

echo "✅ Sistema iniciado com sucesso!"
echo "🌐 Acesse: http://localhost:3000"
echo "📊 Monitorar: pm2 monit"
echo "📋 Ver logs: pm2 logs sistema-bolao"
echo "🛑 Parar: pm2 stop sistema-bolao"
