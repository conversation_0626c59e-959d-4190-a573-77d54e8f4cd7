import { initializeDatabase, executeQuery } from '../lib/database-config.js'

async function createPaymentTables() {
  try {
    console.log('🏗️ Criando tabelas do sistema de pagamentos...')
    
    await initializeDatabase()
    
    // Criar tabela de usuários
    console.log('👤 Criando tabela de usuários...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS usuarios (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        cpf VARCHAR(14) UNIQUE NOT NULL,
        telefone VARCHAR(20),
        data_nascimento DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_cpf (cpf)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela usuarios criada')

    // Criar tabela de bilhetes
    console.log('🎫 Criando tabela de bilhetes...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS bilhetes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo VARCHAR(50) UNIQUE NOT NULL,
        usuario_id INT,
        usuario_nome VARCHAR(255) NOT NULL,
        usuario_email VARCHAR(255) NOT NULL,
        usuario_cpf VARCHAR(14) NOT NULL,
        valor_total DECIMAL(10,2) NOT NULL,
        quantidade_apostas INT NOT NULL DEFAULT 0,
        status ENUM('pendente', 'pago', 'cancelado', 'expirado') DEFAULT 'pendente',
        data_expiracao DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE SET NULL,
        INDEX idx_codigo (codigo),
        INDEX idx_usuario_email (usuario_email),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela bilhetes criada')

    // Criar tabela de apostas
    console.log('🎯 Criando tabela de apostas...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS apostas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bilhete_id INT NOT NULL,
        partida_id INT NOT NULL,
        palpite_casa INT,
        palpite_fora INT,
        valor_aposta DECIMAL(10,2) NOT NULL,
        multiplicador DECIMAL(5,2) DEFAULT 1.00,
        status ENUM('pendente', 'acertou', 'errou', 'cancelada') DEFAULT 'pendente',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (bilhete_id) REFERENCES bilhetes(id) ON DELETE CASCADE,
        FOREIGN KEY (partida_id) REFERENCES jogos(id) ON DELETE CASCADE,
        INDEX idx_bilhete_id (bilhete_id),
        INDEX idx_partida_id (partida_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela apostas criada')

    // Criar tabela de pagamentos
    console.log('💳 Criando tabela de pagamentos...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS pagamentos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bilhete_id INT,
        transaction_id VARCHAR(255) UNIQUE,
        order_id VARCHAR(255) UNIQUE,
        qr_code_payment_id VARCHAR(255),
        amount DECIMAL(10,2) NOT NULL,
        status ENUM('PENDING', 'PAID', 'FAILED', 'DECLINED', 'GENERATED', 'PROCESSING', 'WAITING') DEFAULT 'PENDING',
        end_to_end_id VARCHAR(255),
        error_message TEXT,
        qr_code_value TEXT,
        qrcode_image LONGTEXT,
        expiration_datetime DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (bilhete_id) REFERENCES bilhetes(id) ON DELETE SET NULL,
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_order_id (order_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela pagamentos criada')

    // Criar tabela de bolões
    console.log('🏆 Criando tabela de bolões...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS boloes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        descricao TEXT,
        valor_entrada DECIMAL(10,2) NOT NULL,
        premio_total DECIMAL(10,2) DEFAULT 0,
        data_inicio DATETIME NOT NULL,
        data_fim DATETIME NOT NULL,
        status ENUM('ativo', 'finalizado', 'cancelado') DEFAULT 'ativo',
        max_participantes INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_status (status),
        INDEX idx_data_inicio (data_inicio),
        INDEX idx_data_fim (data_fim)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela boloes criada')

    // Criar tabela de participações em bolões
    console.log('👥 Criando tabela de participações...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS bolao_participacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bolao_id INT NOT NULL,
        usuario_id INT NOT NULL,
        bilhete_id INT,
        pontuacao INT DEFAULT 0,
        posicao INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (bolao_id) REFERENCES boloes(id) ON DELETE CASCADE,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
        FOREIGN KEY (bilhete_id) REFERENCES bilhetes(id) ON DELETE SET NULL,
        UNIQUE KEY unique_participation (bolao_id, usuario_id),
        INDEX idx_bolao_id (bolao_id),
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_pontuacao (pontuacao)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela bolao_participacoes criada')

    // Verificar se as tabelas foram criadas
    const tables = await executeQuery(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'sistema-bolao-top'
      AND TABLE_NAME IN ('usuarios', 'bilhetes', 'apostas', 'pagamentos', 'boloes', 'bolao_participacoes')
      ORDER BY TABLE_NAME
    `)

    console.log('\n📊 Tabelas criadas:')
    tables.forEach(table => {
      console.log(`   ✅ ${table.TABLE_NAME}`)
    })

    console.log('\n🎉 Sistema de pagamentos configurado com sucesso!')
    console.log('\n📋 Próximos passos:')
    console.log('   1. Testar API PIX: POST /api/pix/qrcode')
    console.log('   2. Configurar webhook: /api/pix/webhook')
    console.log('   3. Verificar status: GET /api/pix/status/{transactionId}')

  } catch (error) {
    console.error('❌ Erro ao criar tabelas:', error.message)
  }
}

// Executar
createPaymentTables()
