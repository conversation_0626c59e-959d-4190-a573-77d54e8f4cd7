import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    await initializeDatabase()

    console.log("📊 Coletando dados do dashboard administrativo...")

    // Estatísticas básicas com tratamento de erro individual
    const stats: any = {}

    try {
      const [totalBoloes] = await executeQuery(`SELECT COUNT(*) as count FROM boloes`)
      stats.totalBoloes = totalBoloes?.count || 0
    } catch (error) {
      console.error("Erro ao buscar bolões:", error)
      stats.totalBoloes = 0
    }

    try {
      const [totalUsuarios] = await executeQuery(`SELECT COUNT(*) as count FROM usuarios`)
      stats.totalUsuarios = totalUsuarios?.count || 0
    } catch (error) {
      console.error("Erro ao buscar usuários:", error)
      stats.totalUsuarios = 0
    }

    try {
      const [faturamento] = await executeQuery(`
        SELECT COALESCE(SUM(valor_total), 0) as total
        FROM bilhetes
        WHERE status = 'pago'
        AND MONTH(created_at) = MONTH(NOW())
        AND YEAR(created_at) = YEAR(NOW())
      `)
      stats.faturamentoMes = faturamento?.total || 0
    } catch (error) {
      console.error("Erro ao buscar faturamento:", error)
      stats.faturamentoMes = 0
    }

    try {
      const [apostasHoje] = await executeQuery(`
        SELECT COUNT(*) as count
        FROM bilhetes
        WHERE DATE(created_at) = CURDATE()
      `)
      stats.apostasHoje = apostasHoje?.count || 0
    } catch (error) {
      console.error("Erro ao buscar apostas hoje:", error)
      stats.apostasHoje = 0
    }

    try {
      const [cambistasAtivos] = await executeQuery(`
        SELECT COUNT(*) as count
        FROM usuarios
        WHERE tipo = 'cambista' AND status = 'ativo'
      `)
      stats.cambistasAtivos = cambistasAtivos?.count || 0
    } catch (error) {
      console.error("Erro ao buscar cambistas:", error)
      stats.cambistasAtivos = 0
    }

    try {
      const [jogosHoje] = await executeQuery(`
        SELECT COUNT(*) as count
        FROM jogos
        WHERE DATE(data_jogo) = CURDATE()
      `)
      stats.jogosHoje = jogosHoje?.count || 0
    } catch (error) {
      console.error("Erro ao buscar jogos hoje:", error)
      stats.jogosHoje = 0
    }

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error("❌ Erro ao carregar dashboard:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        stats: {
          totalBoloes: 0,
          totalUsuarios: 0,
          faturamentoMes: 0,
          apostasHoje: 0,
          cambistasAtivos: 0,
          jogosHoje: 0,
        },
      },
      { status: 500 }
    )
  }
}
