'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
// import { WalletHeader } from '@/components/WalletHeader'
import {
  Wallet,
  TrendingUp,
  Users,
  DollarSign,
  Activity
} from 'lucide-react'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

export default function WalletDemoPage() {
  const [usuario, setUsuario] = useState<Usuario>({
    id: 585,
    nome: 'Guilherme',
    email: '<EMAIL>',
    saldo: 0.00
  })

  const handleSaldoUpdate = (novoSaldo: number) => {
    setUsuario(prev => ({ ...prev, saldo: novoSaldo }))
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header com Modal Integrado */}
      {/* <WalletHeader
        usuario={usuario}
        onSaldoUpdate={handleSaldoUpdate}
      /> */}

      {/* Conteúdo Principal */}
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center">
            <Wallet className="h-8 w-8 mr-3 text-blue-600" />
            Demo - Sistema de Carteira
          </h1>
          <p className="text-gray-600">
            Demonstração do novo sistema de saldo integrado. Clique em "Depósito" no header para abrir a carteira.
          </p>
        </div>

        {/* Cards de Estatísticas */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Saldo Atual</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(usuario.saldo)}
              </div>
              <p className="text-xs text-muted-foreground">
                Disponível para apostas
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Usuário</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{usuario.nome}</div>
              <p className="text-xs text-muted-foreground">
                ID: {usuario.id}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Ativo</div>
              <p className="text-xs text-muted-foreground">
                Sistema funcionando
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Modo</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">Demo</div>
              <p className="text-xs text-muted-foreground">
                Ambiente de teste
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Instruções */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800">Como usar a Carteira</CardTitle>
            <CardDescription className="text-blue-600">
              Siga os passos abaixo para testar o sistema
            </CardDescription>
          </CardHeader>
          <CardContent className="text-blue-700">
            <ol className="list-decimal list-inside space-y-2">
              <li>Clique no botão <strong>"Depósito"</strong> no header acima</li>
              <li>No modal que abrir, clique em <strong>"Bônus R$ 25,00"</strong> para adicionar saldo</li>
              <li>Teste fazer um <strong>"Depósito PIX"</strong> simulado</li>
              <li>Use o saldo para <strong>"Comprar bilhete"</strong></li>
              <li>Veja o <strong>"Histórico"</strong> de todas as transações</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

