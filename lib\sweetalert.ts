import Swal from 'sweetalert2'

// Configuração padrão do SweetAlert2
const defaultConfig = {
  customClass: {
    popup: 'rounded-2xl shadow-2xl',
    title: 'text-2xl font-bold',
    content: 'text-lg',
    confirmButton: 'bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200',
    cancelButton: 'bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200'
  },
  buttonsStyling: false,
  allowOutsideClick: false,
  allowEscapeKey: false,
  showClass: {
    popup: 'animate__animated animate__zoomIn animate__faster'
  },
  hideClass: {
    popup: 'animate__animated animate__zoomOut animate__faster'
  }
}

// Alerta de sucesso para pagamento
export const showPaymentSuccessAlert = (data: {
  codigo: string
  valor: string | number
  clientName?: string
  transactionId?: string
}) => {
  const valorFormatado = typeof data.valor === 'number' 
    ? data.valor.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })
    : `R$ ${data.valor}`

  return Swal.fire({
    ...defaultConfig,
    icon: 'success',
    title: '🎉 Pagamento Confirmado!',
    html: `
      <div class="text-center space-y-4">
        <div class="bg-green-100 border border-green-300 rounded-lg p-4 mb-4">
          <h3 class="text-lg font-semibold text-green-800 mb-2">Bilhete Aprovado</h3>
          <p class="text-green-700"><strong>Código:</strong> ${data.codigo}</p>
          <p class="text-green-700"><strong>Valor:</strong> ${valorFormatado}</p>
          ${data.clientName ? `<p class="text-green-700"><strong>Cliente:</strong> ${data.clientName}</p>` : ''}
          ${data.transactionId ? `<p class="text-sm text-green-600 mt-2">ID: ${data.transactionId}</p>` : ''}
        </div>
        <div class="text-gray-600">
          <p>✅ Pagamento processado com sucesso</p>
          <p>📱 Você pode acompanhar seus bilhetes na área do usuário</p>
        </div>
      </div>
    `,
    confirmButtonText: 'Entendi!',
    timer: 10000,
    timerProgressBar: true,
    customClass: {
      ...defaultConfig.customClass,
      popup: 'rounded-2xl shadow-2xl max-w-md',
      title: 'text-2xl font-bold text-green-600',
      htmlContainer: 'text-left'
    }
  })
}

// Alerta de erro para pagamento
export const showPaymentErrorAlert = (message: string = 'Erro no processamento do pagamento') => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'error',
    title: '❌ Erro no Pagamento',
    text: message,
    confirmButtonText: 'Tentar Novamente',
    customClass: {
      ...defaultConfig.customClass,
      title: 'text-2xl font-bold text-red-600',
      confirmButton: 'bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200'
    }
  })
}

// Alerta de confirmação genérico
export const showConfirmAlert = (
  title: string,
  text: string,
  confirmText: string = 'Confirmar',
  cancelText: string = 'Cancelar'
) => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'question',
    title,
    text,
    showCancelButton: true,
    confirmButtonText: confirmText,
    cancelButtonText: cancelText,
    customClass: {
      ...defaultConfig.customClass,
      title: 'text-2xl font-bold text-blue-600'
    }
  })
}

// Alerta de sucesso genérico
export const showSuccessAlert = (
  title: string,
  text: string,
  confirmText: string = 'OK'
) => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'success',
    title,
    text,
    confirmButtonText: confirmText,
    customClass: {
      ...defaultConfig.customClass,
      title: 'text-2xl font-bold text-green-600'
    }
  })
}

// Alerta de informação
export const showInfoAlert = (
  title: string,
  text: string,
  confirmText: string = 'Entendi'
) => {
  return Swal.fire({
    ...defaultConfig,
    icon: 'info',
    title,
    text,
    confirmButtonText: confirmText,
    customClass: {
      ...defaultConfig.customClass,
      title: 'text-2xl font-bold text-blue-600'
    }
  })
}

// Toast de sucesso rápido
export const showSuccessToast = (message: string) => {
  return Swal.fire({
    toast: true,
    position: 'top-end',
    icon: 'success',
    title: message,
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    customClass: {
      popup: 'rounded-lg shadow-lg',
      title: 'text-sm font-semibold'
    }
  })
}

export default {
  showPaymentSuccessAlert,
  showPaymentErrorAlert,
  showConfirmAlert,
  showSuccessAlert,
  showInfoAlert,
  showSuccessToast
}
