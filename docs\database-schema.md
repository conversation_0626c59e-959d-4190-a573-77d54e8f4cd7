# 📊 Sistema Bolão Top - Estrutura do Banco de Dados

## 🎯 **<PERSON>isão Geral**
- **Banco**: `sistema-bolao-top`
- **Total de Tabelas**: 17
- **Engine**: MySQL/MariaDB
- **Charset**: utf8mb4

## 📋 **Tabelas Principais**

### 👥 **Usuários e Autenticação**

#### `usuarios` (5 registros)
```sql
- id (PK, auto_increment)
- nome (varchar 255)
- email (varchar 255, UNIQUE)
- telefone (varchar 20)
- endereco (text)
- cpf_cnpj (varchar 20)
- senha_hash (varchar 255)
- tipo (enum: admin, usuario, cambista)
- status (enum: ativo, inativo, bloqueado)
- saldo (decimal 10,2)
- data_cadastro (timestamp)
- ultimo_acesso (timestamp)
- data_atualizacao (timestamp)
- afiliado_id (FK -> afiliados.id)
- porcentagem_comissao (decimal 5,2)
```

### 🏆 **Sistema de Afiliados**

#### `afiliados` (0 registros)
```sql
- id (PK, auto_increment)
- nome (varchar 255)
- email (varchar 255, UNIQUE)
- telefone (varchar 20)
- codigo_afiliado (varchar 20, UNIQUE)
- percentual_comissao (decimal 5,2, default: 5.00)
- comissao_total (decimal 10,2, default: 0.00)
- total_indicacoes (int, default: 0)
- senha_hash (varchar 255)
- status (enum: ativo, inativo, bloqueado)
- usuario_id (FK -> usuarios.id)
- data_cadastro (timestamp)
- data_atualizacao (timestamp)
```

#### `afiliado_indicacoes` (0 registros)
```sql
- id (PK, auto_increment)
- afiliado_id (FK -> afiliados.id)
- usuario_indicado_id (FK -> usuarios.id)
- valor_comissao (decimal 10,2, default: 0.00)
- status (enum: pendente, pago, cancelado)
- data_indicacao (timestamp)
- data_pagamento (timestamp)
```

### ⚽ **Esportes e Competições**

#### `campeonatos` (16 registros)
```sql
- id (PK, auto_increment)
- nome (varchar 255)
- descricao (text)
- pais (varchar 100)
- temporada (varchar 50)
- status (enum: ativo, encerrado, pausado)
- data_inicio (date)
- data_fim (date)
- api_id (varchar 50)
- logo_url (varchar 500)
- data_criacao (timestamp)
```

#### `times` (197 registros)
```sql
- id (PK, auto_increment)
- nome (varchar 255)
- nome_curto (varchar 50)
- cidade (varchar 100)
- estado (varchar 100)
- pais (varchar 100)
- logo_url (varchar 500)
- api_id (varchar 50)
- data_criacao (timestamp)
- image_id (varchar 50)
```

#### `jogos` (830 registros)
```sql
- id (PK, auto_increment)
- campeonato_id (FK -> campeonatos.id)
- time_casa_id (FK -> times.id)
- time_fora_id (FK -> times.id)
- data_jogo (timestamp)
- local_jogo (varchar 255)
- rodada (int)
- resultado_casa (int)
- resultado_fora (int)
- status (enum: agendado, ao_vivo, finalizado, cancelado)
- api_id (varchar 50)
- data_criacao (timestamp)
```

### 🎲 **Sistema de Bolões**

#### `boloes` (1 registro)
```sql
- id (PK, auto_increment)
- nome (varchar 255)
- descricao (text)
- valor_aposta (decimal 10,2)
- premio_total (decimal 10,2)
- max_participantes (int)
- min_acertos (int, default: 3)
- data_inicio (timestamp)
- data_fim (timestamp)
- status (enum: ativo, encerrado, em_breve)
- criado_por (FK -> usuarios.id)
- regras (text)
- campeonatos_selecionados (longtext JSON)
- partidas_selecionadas (longtext JSON)
- data_criacao (timestamp)
```

#### `bolao_jogos` (0 registros)
```sql
- id (PK, auto_increment)
- bolao_id (FK -> boloes.id)
- jogo_id (FK -> jogos.id)
- data_criacao (timestamp)
```

#### `bolao_participacoes` (0 registros)
```sql
- id (PK, auto_increment)
- bolao_id (FK -> boloes.id)
- usuario_id (FK -> usuarios.id)
- bilhete_id (FK -> bilhetes.id)
- pontuacao (int, default: 0)
- posicao (int)
- created_at (timestamp)
```

### 🎫 **Sistema de Apostas**

#### `bilhetes` (6 registros)
```sql
- id (PK, auto_increment)
- codigo (varchar 50)
- usuario_id (FK -> usuarios.id)
- usuario_nome (varchar 255)
- usuario_email (varchar 255)
- usuario_cpf (varchar 14)
- valor_total (decimal 10,2)
- quantidade_apostas (int, default: 0)
- status (enum: pendente, pago, cancelado, expirado)
- data_expiracao (datetime)
- created_at (timestamp)
- updated_at (timestamp)
```

#### `bilhete_apostas` (0 registros)
```sql
- id (PK, auto_increment)
- bilhete_id (FK -> bilhetes.id)
- match_id (FK -> jogos.id)
- resultado (enum: casa, empate, fora)
- created_at (timestamp)
```

#### `apostas` (0 registros)
```sql
- id (PK, auto_increment)
- usuario_id (FK -> usuarios.id)
- bolao_id (FK -> boloes.id)
- valor_total (decimal 10,2)
- status (enum: pendente, paga, cancelada)
- data_aposta (timestamp)
- data_pagamento (timestamp)
- codigo_bilhete (varchar 50, UNIQUE)
- data_criacao (timestamp)
```

#### `aposta_detalhes` (0 registros)
```sql
- id (PK, auto_increment)
- aposta_id (FK -> apostas.id)
- jogo_id (FK -> jogos.id)
- resultado_apostado (enum: casa, empate, fora)
- acertou (tinyint 1)
- data_criacao (timestamp)
```

### 💰 **Sistema de Pagamentos**

#### `pagamentos` (0 registros)
```sql
- id (PK, auto_increment)
- aposta_id (FK -> apostas.id)
- usuario_id (FK -> usuarios.id)
- valor (decimal 10,2)
- metodo_pagamento (enum: pix, cartao, dinheiro)
- status (enum: pendente, aprovado, rejeitado, cancelado)
- chave_pix (varchar 255)
- codigo_transacao (varchar 255)
- qr_code (text)
- txid (varchar 255)
- webhook_data (longtext)
- data_pagamento (timestamp)
- data_confirmacao (timestamp)
- data_vencimento (timestamp)
```

### 🏆 **Sistema de Prêmios**

#### `premios` (0 registros)
```sql
- id (PK, auto_increment)
- bolao_id (FK -> boloes.id)
- usuario_id (FK -> usuarios.id)
- aposta_id (FK -> apostas.id)
- acertos (int)
- valor_premio (decimal 10,2)
- status (enum: pendente, pago, cancelado)
- data_premio (timestamp)
- data_pagamento (timestamp)
```

### ⚙️ **Sistema e Configurações**

#### `configuracoes` (7 registros)
```sql
- id (PK, auto_increment)
- chave (varchar 100, UNIQUE)
- valor (text)
- descricao (text)
- tipo (enum: string, number, boolean, json)
- data_atualizacao (timestamp)
```

#### `logs` (0 registros)
```sql
- id (PK, auto_increment)
- usuario_id (FK -> usuarios.id)
- acao (varchar 255)
- tabela_afetada (varchar 100)
- registro_id (int)
- dados_anteriores (longtext)
- dados_novos (longtext)
- ip_address (varchar 45)
- user_agent (text)
- data_log (timestamp)
```

## 🔗 **Relacionamentos (Foreign Keys)**

1. **afiliados** → usuarios (usuario_id)
2. **afiliado_indicacoes** → afiliados (afiliado_id)
3. **afiliado_indicacoes** → usuarios (usuario_indicado_id)
4. **apostas** → usuarios (usuario_id)
5. **apostas** → boloes (bolao_id)
6. **aposta_detalhes** → apostas (aposta_id)
7. **aposta_detalhes** → jogos (jogo_id)
8. **bilhetes** → usuarios (usuario_id)
9. **bilhete_apostas** → bilhetes (bilhete_id)
10. **bolao_jogos** → boloes (bolao_id)
11. **bolao_jogos** → jogos (jogo_id)
12. **bolao_participacoes** → boloes (bolao_id)
13. **bolao_participacoes** → usuarios (usuario_id)
14. **bolao_participacoes** → bilhetes (bilhete_id)
15. **boloes** → usuarios (criado_por)
16. **jogos** → campeonatos (campeonato_id)
17. **jogos** → times (time_casa_id)
18. **jogos** → times (time_fora_id)
19. **logs** → usuarios (usuario_id)
20. **pagamentos** → apostas (aposta_id)
21. **pagamentos** → usuarios (usuario_id)
22. **premios** → boloes (bolao_id)
23. **premios** → usuarios (usuario_id)
24. **premios** → apostas (aposta_id)
25. **usuarios** → afiliados (afiliado_id)

## 📊 **Status dos Dados**

- ✅ **Campeonatos**: 16 registros (Brasileirão, Copa do Brasil, etc.)
- ✅ **Times**: 197 registros (times brasileiros e internacionais)
- ✅ **Jogos**: 830 registros (partidas agendadas)
- ✅ **Usuários**: 5 registros (incluindo admin e cambistas)
- ✅ **Bilhetes**: 6 registros (alguns já pagos)
- ✅ **Bolões**: 1 registro ativo
- ✅ **Configurações**: 7 registros (configurações do sistema)

## 🎯 **Funcionalidades Implementadas**

1. **✅ Sistema de Usuários** - Cadastro, login, tipos de usuário
2. **✅ Sistema de Afiliados** - Comissões e indicações
3. **✅ Sistema de Bolões** - Criação e participação
4. **✅ Sistema de Apostas** - Bilhetes e apostas individuais
5. **✅ Sistema de Pagamentos** - PIX com webhook
6. **✅ Sistema de Prêmios** - Distribuição de prêmios
7. **✅ Sistema de Logs** - Auditoria de ações
8. **✅ Configurações** - Parâmetros do sistema

## 🔧 **APIs Funcionais**

- `/api/webhook/pix` - Webhook para pagamentos PIX
- `/api/payment/success` - Verificação de pagamentos
- `/api/admin/*` - Endpoints administrativos
- `/api/bolao/*` - Gestão de bolões
- `/api/cambista/*` - Dashboard de cambistas
