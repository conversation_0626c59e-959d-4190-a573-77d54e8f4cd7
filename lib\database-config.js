// Configuração para usar APENAS MySQL
// Conforme solicitado pelo usuário - NUNCA usar SQLite

import mysql from "mysql2/promise"

let pool = null

// Configuração do banco de dados MySQL otimizada para evitar "Too many connections"
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4',
  timezone: '+00:00',

  // Pool de conexões otimizado
  connectionLimit: 5, // Reduzido para evitar sobrecarga
  maxIdle: 2, // Máximo de conexões inativas
  idleTimeout: 30000, // 30 segundos para conexões inativas
  queueLimit: 50, // Aumentado para suportar mais requisições em fila
  waitForConnections: true, // Aguardar conexões disponíveis
}

// Função para inicializar o pool de conexões MySQL
export async function initializeDatabase() {
  try {
    if (!pool) {
      console.log('🔧 Inicializando pool MySQL para sistema-bolao-top...')
      pool = mysql.createPool(dbConfig)
      console.log("✅ Pool de conexões MySQL inicializado com sucesso")
    }

    // Testar a conexão
    const connection = await pool.getConnection()
    await connection.ping()
    connection.release()

    console.log("✅ Conexão com MySQL sistema-bolao-top estabelecida com sucesso")
    return pool
  } catch (error) {
    console.error("❌ Erro ao inicializar banco de dados MySQL:", error)
    throw error
  }
}

// Função para obter o pool de conexões
export async function getDatabase() {
  if (!pool) {
    await initializeDatabase()
  }
  return pool
}

// Função para executar queries com retry automático
export async function executeQuery(query, params = [], retries = 3) {
  let connection = null
  let lastError = null

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const pool = await getDatabase()

      // Usar timeout para adquirir conexão
      connection = await Promise.race([
        pool.getConnection(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout ao adquirir conexão do pool')), 30000)
        )
      ])

      const [results] = await connection.execute(query, params)
      return results

    } catch (error) {
      lastError = error
      console.error(`❌ Erro na tentativa ${attempt}/${retries}:`, error.message)

      // Se é erro de muitas conexões, aguardar e tentar novamente
      if (error.code === 'ER_CON_COUNT_ERROR' ||
          error.code === 'ER_TOO_MANY_USER_CONNECTIONS' ||
          error.message?.includes('Too many connections') ||
          error.message?.includes('Timeout ao adquirir conexão')) {

        if (attempt < retries) {
          const waitTime = Math.min(attempt * 3000, 15000) // Máximo 15s
          console.log(`🔄 Aguardando ${waitTime}ms antes da próxima tentativa...`)
          await new Promise(resolve => setTimeout(resolve, waitTime))
          continue
        }
      }

      // Para outros erros de conexão, tentar novamente
      if (error.code === 'ECONNREFUSED' || error.code === 'PROTOCOL_CONNECTION_LOST') {
        if (attempt < retries) {
          console.log(`🔄 Tentando reconectar em ${attempt * 1000}ms...`)
          await new Promise(resolve => setTimeout(resolve, attempt * 1000))
          continue
        }
      }

      // Para outros erros, não tentar novamente
      break
    } finally {
      if (connection) {
        try {
          connection.release()
        } catch (releaseError) {
          console.warn("⚠️ Erro ao liberar conexão:", releaseError.message)
        }
        connection = null
      }
    }
  }

  // Se chegou aqui, todas as tentativas falharam
  console.error("❌ Falha após todas as tentativas:", lastError?.message || lastError)

  // Para erro de "Too many connections", retornar dados básicos em vez de falhar
  if (lastError?.code === 'ER_CON_COUNT_ERROR' ||
      lastError?.code === 'ER_TOO_MANY_USER_CONNECTIONS' ||
      lastError?.message?.includes('Too many connections')) {
    console.warn("⚠️ Erro ao acessar banco, retornando dados básicos:", lastError.message)
    return [] // Retorna array vazio em vez de falhar
  }

  throw lastError
}

// Função para executar query única
export async function executeQuerySingle(query, params = []) {
  const results = await executeQuery(query, params)
  return Array.isArray(results) && results.length > 0 ? results[0] : null
}

// Função para fechar conexões
export async function closeDatabase() {
  if (pool) {
    await pool.end()
    pool = null
    console.log("✅ Pool de conexões MySQL fechado")
  }
}
