/**
 * Script para corrigir a exibição dos nomes dos times
 * Verifica se há jogos no banco e cria alguns jogos de teste se necessário
 */

import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function fixTeamsDisplay() {
  try {
    console.log('🔧 Iniciando correção da exibição dos times...')
    
    await initializeDatabase()
    
    // 1. Verificar quantos jogos existem
    const jogosCount = await executeQuery('SELECT COUNT(*) as total FROM jogos')
    console.log(`📊 Total de jogos no banco: ${jogosCount[0].total}`)
    
    // 2. Verificar quantos times existem
    const timesCount = await executeQuery('SELECT COUNT(*) as total FROM times')
    console.log(`📊 Total de times no banco: ${timesCount[0].total}`)
    
    // 3. Verificar quantos campeonatos existem
    const campeonatosCount = await executeQuery('SELECT COUNT(*) as total FROM campeonatos')
    console.log(`📊 Total de campeonatos no banco: ${campeonatosCount[0].total}`)
    
    // 4. Se não há jogos suficientes, criar alguns jogos de teste
    if (jogosCount[0].total < 11) {
      console.log('⚠️ Poucos jogos encontrados. Criando jogos de teste...')
      await createTestGames()
    }
    
    // 5. Verificar se há bolões ativos
    const boloesAtivos = await executeQuery(`
      SELECT id, nome, status FROM boloes 
      WHERE status IN ('ativo', 'em_breve')
      ORDER BY id DESC
      LIMIT 5
    `)
    
    console.log(`📊 Bolões ativos: ${boloesAtivos.length}`)
    boloesAtivos.forEach(bolao => {
      console.log(`  - ID: ${bolao.id}, Nome: ${bolao.nome}, Status: ${bolao.status}`)
    })
    
    // 6. Se não há bolões ativos, criar um bolão de teste
    if (boloesAtivos.length === 0) {
      console.log('⚠️ Nenhum bolão ativo encontrado. Criando bolão de teste...')
      await createTestBolao()
    }
    
    // 7. Verificar dados de um jogo específico
    const sampleGame = await executeQuery(`
      SELECT 
        j.id, j.time_casa_id, j.time_fora_id,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        c.nome as campeonato_nome,
        c.codigo as campeonato_codigo,
        c.logo_url as campeonato_logo
      FROM jogos j
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      LIMIT 3
    `)
    
    console.log('\n📋 Amostra de jogos com dados completos:')
    sampleGame.forEach((jogo, index) => {
      console.log(`\n${index + 1}. Jogo ID: ${jogo.id}`)
      console.log(`   Casa: ${jogo.time_casa_nome || 'SEM NOME'} (ID: ${jogo.time_casa_id})`)
      console.log(`   Fora: ${jogo.time_fora_nome || 'SEM NOME'} (ID: ${jogo.time_fora_id})`)
      console.log(`   Campeonato: ${jogo.campeonato_nome || 'SEM NOME'} (${jogo.campeonato_codigo || 'SEM CÓDIGO'})`)
      console.log(`   Logos: Casa=${jogo.time_casa_logo ? 'SIM' : 'NÃO'}, Fora=${jogo.time_fora_logo ? 'SIM' : 'NÃO'}, Camp=${jogo.campeonato_logo ? 'SIM' : 'NÃO'}`)
    })
    
    console.log('\n✅ Diagnóstico completo!')
    
  } catch (error) {
    console.error('❌ Erro no diagnóstico:', error)
  }
}

async function createTestGames() {
  try {
    console.log('🎮 Criando jogos de teste...')
    
    // Buscar alguns times para criar jogos
    const times = await executeQuery('SELECT id, nome FROM times LIMIT 20')
    const campeonatos = await executeQuery('SELECT id, nome FROM campeonatos LIMIT 5')
    
    if (times.length < 4 || campeonatos.length < 1) {
      console.log('⚠️ Não há times ou campeonatos suficientes para criar jogos')
      return
    }
    
    const campeonato = campeonatos[0]
    const jogosParaCriar = [
      { casa: times[0], fora: times[1] },
      { casa: times[2], fora: times[3] },
      { casa: times[4], fora: times[5] },
      { casa: times[6], fora: times[7] },
      { casa: times[8], fora: times[9] },
      { casa: times[10], fora: times[11] },
      { casa: times[12], fora: times[13] },
      { casa: times[14], fora: times[15] },
      { casa: times[16], fora: times[17] },
      { casa: times[18], fora: times[19] },
      { casa: times[0], fora: times[19] }, // 11º jogo
    ]
    
    for (let i = 0; i < Math.min(jogosParaCriar.length, 11); i++) {
      const jogo = jogosParaCriar[i]
      const dataJogo = new Date()
      dataJogo.setDate(dataJogo.getDate() + i + 1) // Jogos nos próximos dias
      
      await executeQuery(`
        INSERT INTO jogos (
          campeonato_id, time_casa_id, time_fora_id, data_jogo, status
        ) VALUES (?, ?, ?, ?, 'agendado')
      `, [
        campeonato.id,
        jogo.casa.id,
        jogo.fora.id,
        dataJogo.toISOString().slice(0, 19).replace('T', ' ')
      ])
      
      console.log(`✅ Jogo criado: ${jogo.casa.nome} vs ${jogo.fora.nome}`)
    }
    
    console.log('🎮 Jogos de teste criados com sucesso!')
    
  } catch (error) {
    console.error('❌ Erro ao criar jogos de teste:', error)
  }
}

async function createTestBolao() {
  try {
    console.log('🏆 Criando bolão de teste...')
    
    const dataInicio = new Date()
    const dataFim = new Date()
    dataFim.setDate(dataFim.getDate() + 30)
    
    const result = await executeQuery(`
      INSERT INTO boloes (
        nome, descricao, valor_aposta, premio_total, max_participantes,
        min_acertos, data_inicio, data_fim, status, criado_por
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'ativo', 1)
    `, [
      'Bolão Teste - Times Reais',
      'Bolão de teste com times reais da API',
      25.00,
      1000.00,
      100,
      3,
      dataInicio.toISOString().slice(0, 19).replace('T', ' '),
      dataFim.toISOString().slice(0, 19).replace('T', ' ')
    ])
    
    const bolaoId = result.insertId
    
    // Associar alguns jogos ao bolão
    const jogos = await executeQuery('SELECT id FROM jogos ORDER BY data_jogo ASC LIMIT 11')
    
    for (const jogo of jogos) {
      await executeQuery(`
        INSERT INTO bolao_jogos (bolao_id, jogo_id)
        VALUES (?, ?)
      `, [bolaoId, jogo.id])
    }
    
    console.log(`🏆 Bolão de teste criado com ID: ${bolaoId}`)
    
  } catch (error) {
    console.error('❌ Erro ao criar bolão de teste:', error)
  }
}

// Executar o script
fixTeamsDisplay()
  .then(() => {
    console.log('🎯 Script concluído!')
    process.exit(0)
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error)
    process.exit(1)
  })
