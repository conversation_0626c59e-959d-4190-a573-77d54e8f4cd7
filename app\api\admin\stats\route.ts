import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET() {
  try {
    await initializeDatabase()

    console.log("📊 Coletando estatísticas do sistema...")

    // Contar registros em cada tabela principal
    const stats: any = {}

    const tables = [
      { name: 'campeonatos', label: 'Campeonatos' },
      { name: 'times', label: 'Times' },
      { name: 'jogos', label: 'Partidas' },
      { name: 'bilhetes', label: 'Bilhetes' },
      { name: 'usuarios', label: 'Usuários' },
      { name: 'afiliados', label: 'Afiliados' },
      { name: 'boloes', label: '<PERSON><PERSON><PERSON><PERSON>' },
      { name: 'pagamentos', label: 'Pagamentos' }
    ]

    for (const table of tables) {
      try {
        const [result] = await executeQuery(`SELECT COUNT(*) as count FROM ${table.name}`)
        stats[table.name] = {
          count: result.count,
          label: table.label
        }
      } catch (error) {
        console.error(`Erro ao contar ${table.name}:`, error)
        stats[table.name] = {
          count: 0,
          label: table.label,
          error: true
        }
      }
    }

    // Estatísticas específicas
    try {
      // Campeonatos por país
      const campeonatosPorPais = await executeQuery(`
        SELECT pais, COUNT(*) as count 
        FROM campeonatos 
        WHERE status = 'ativo'
        GROUP BY pais 
        ORDER BY count DESC 
        LIMIT 10
      `)

      // Times por país
      const timesPorPais = await executeQuery(`
        SELECT pais, COUNT(*) as count 
        FROM times 
        GROUP BY pais 
        ORDER BY count DESC 
        LIMIT 10
      `)

      // Partidas por status
      const partidasPorStatus = await executeQuery(`
        SELECT status, COUNT(*) as count 
        FROM jogos 
        GROUP BY status
      `)

      // Bilhetes por status
      const bilhetesPorStatus = await executeQuery(`
        SELECT status, COUNT(*) as count 
        FROM bilhetes 
        GROUP BY status
      `)

      // Últimas sincronizações (campeonatos com api_id)
      const campeonatosComAPI = await executeQuery(`
        SELECT COUNT(*) as count 
        FROM campeonatos 
        WHERE api_id IS NOT NULL
      `)

      const timesComAPI = await executeQuery(`
        SELECT COUNT(*) as count 
        FROM times 
        WHERE api_id IS NOT NULL
      `)

      // Próximas partidas
      const proximasPartidas = await executeQuery(`
        SELECT COUNT(*) as count 
        FROM jogos 
        WHERE data_jogo > NOW() 
        AND status = 'agendado'
      `)

      stats.detalhes = {
        campeonatos_por_pais: campeonatosPorPais,
        times_por_pais: timesPorPais,
        partidas_por_status: partidasPorStatus,
        bilhetes_por_status: bilhetesPorStatus,
        campeonatos_com_api: campeonatosComAPI[0].count,
        times_com_api: timesComAPI[0].count,
        proximas_partidas: proximasPartidas[0].count
      }

    } catch (error) {
      console.error("Erro ao coletar estatísticas detalhadas:", error)
      stats.detalhes = { error: "Erro ao coletar detalhes" }
    }

    return NextResponse.json({
      success: true,
      message: "Estatísticas coletadas com sucesso",
      data: stats,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro ao coletar estatísticas:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
