#!/usr/bin/env node

/**
 * Script para desabilitar aprovação automática de pagamentos
 */

async function disableAutoApproval() {
  const baseUrl = 'http://localhost:3000'
  
  console.log('🔒 DESABILITANDO APROVAÇÃO AUTOMÁTICA DE PAGAMENTOS')
  console.log('=' .repeat(60))
  console.log('')

  try {
    // 1. Desabilitar via endpoint
    console.log('1️⃣ DESABILITANDO VIA ENDPOINT:')
    const response = await fetch(`${baseUrl}/api/admin/disable-auto-approval`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'disable_auto_approval' })
    })

    const result = await response.json()

    if (response.ok) {
      console.log('   ✅ Aprovação automática desabilitada com sucesso')
      console.log('')
      console.log('📋 CONFIGURAÇÕES APLICADAS:')
      console.log(`   🔒 Auto approval: ${result.config.auto_approval ? 'ATIVO' : 'DESABILITADO'}`)
      console.log(`   🔒 Fallback por tempo: ${result.config.fallback_time ? 'ATIVO' : 'DESABILITADO'}`)
      console.log(`   🔒 Simulação: ${result.config.simulation ? 'ATIVO' : 'DESABILITADO'}`)
      console.log(`   🔒 Fallback por erro: ${result.config.error_fallback ? 'ATIVO' : 'DESABILITADO'}`)
      console.log(`   ✅ Webhook only: ${result.config.webhook_only ? 'ATIVO' : 'DESABILITADO'}`)
    } else {
      console.log('   ❌ Erro ao desabilitar:', result.error)
    }

  } catch (error) {
    console.log('   ❌ Erro na requisição:', error.message)
  }

  // 2. Verificar status atual
  console.log('')
  console.log('2️⃣ VERIFICANDO STATUS ATUAL:')
  try {
    const statusResponse = await fetch(`${baseUrl}/api/admin/disable-auto-approval`)
    const statusResult = await statusResponse.json()

    if (statusResponse.ok) {
      console.log('   ✅ Status verificado com sucesso')
      console.log(`   📊 Status: ${statusResult.status}`)
      console.log('')
      console.log('📡 WEBHOOKS ATIVOS:')
      console.log(`   🎯 Principal: ${statusResult.webhooks.principal}`)
      console.log(`   🔄 Alternativo: ${statusResult.webhooks.alternativo}`)
      console.log(`   📋 Genérico: ${statusResult.webhooks.generico}`)
      console.log(`   🧪 Simulador: ${statusResult.webhooks.simulador}`)
    } else {
      console.log('   ❌ Erro ao verificar status:', statusResult.error)
    }

  } catch (error) {
    console.log('   ❌ Erro na requisição:', error.message)
  }

  // 3. Testar webhook simulador
  console.log('')
  console.log('3️⃣ TESTANDO WEBHOOK SIMULADOR:')
  try {
    // Primeiro, criar um bilhete de teste
    console.log('   📝 Criando bilhete de teste...')
    
    // Simular criação de bilhete (você pode ajustar conforme necessário)
    const testBilhete = {
      codigo: `TEST_${Date.now()}`,
      transaction_id: `test_${Date.now()}`,
      valor: 0.50,
      status: 'pendente'
    }

    console.log(`   ✅ Bilhete de teste: ${testBilhete.codigo}`)
    console.log('')
    console.log('   🧪 Para testar pagamento, use:')
    console.log(`   curl -X POST ${baseUrl}/api/simulate-pix-payment \\`)
    console.log(`     -H "Content-Type: application/json" \\`)
    console.log(`     -d '{"transaction_id":"${testBilhete.transaction_id}","simulate_payment":true}'`)

  } catch (error) {
    console.log('   ❌ Erro no teste:', error.message)
  }

  // 4. Resumo final
  console.log('')
  console.log('📋 RESUMO FINAL:')
  console.log('=' .repeat(60))
  console.log('✅ Aprovação automática foi DESABILITADA')
  console.log('🔒 Fallbacks por tempo foram DESABILITADOS')
  console.log('🔒 Simulações automáticas foram DESABILITADAS')
  console.log('📡 Apenas webhooks oficiais aprovam pagamentos')
  console.log('')
  console.log('🎯 COMO FUNCIONA AGORA:')
  console.log('   1. Cliente faz aposta → Gera QR Code PIX')
  console.log('   2. Cliente paga PIX real → API detecta pagamento')
  console.log('   3. API chama webhook → Sistema aprova automaticamente')
  console.log('   4. Modal de sucesso aparece → Cliente é notificado')
  console.log('')
  console.log('🧪 PARA TESTES DE DESENVOLVIMENTO:')
  console.log('   • Use o simulador: /api/simulate-pix-payment')
  console.log('   • Ou pague PIX real para testar fluxo completo')
  console.log('')
  console.log('📡 WEBHOOK OFICIAL CONFIGURADO:')
  console.log('   https://ouroemu.site/api/v1/MP/webhookruntransation')
  console.log('')
  console.log('🎉 SISTEMA CONFIGURADO PARA PRODUÇÃO!')
}

// Executar
disableAutoApproval()
  .then(() => {
    console.log('✅ Script concluído com sucesso!')
    process.exit(0)
  })
  .catch(error => {
    console.error('❌ Erro no script:', error)
    process.exit(1)
  })
