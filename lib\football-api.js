// Biblioteca para integração com a API Football-Data.org
// Configure sua API key nas variáveis de ambiente

const API_BASE_URL = "https://api.football-data.org/v4"
const API_KEY = process.env.FOOTBALL_API_TOKEN || "YOUR_API_KEY_HERE"

class FootballAPI {
  constructor() {
    this.headers = {
      "X-Auth-Token": API_KEY,
      "Content-Type": "application/json",
    }
  }

  async makeRequest(endpoint, params = {}) {
    try {
      const url = new URL(`${API_BASE_URL}${endpoint}`)

      // Adicionar parâmetros de query
      Object.keys(params).forEach((key) => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key])
        }
      })

      const response = await fetch(url.toString(), {
        headers: this.headers,
      })

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} - ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error("Football API Error:", error)
      throw error
    }
  }

  // Listar todas as competições disponíveis
  async getCompetitions(areas = null) {
    const params = areas ? { areas } : {}
    return await this.makeRequest("/competitions", params)
  }

  // Obter detalhes de uma competição específica
  async getCompetition(competitionId) {
    return await this.makeRequest(`/competitions/${competitionId}`)
  }

  // Listar partidas de uma competição
  async getCompetitionMatches(competitionId, filters = {}) {
    return await this.makeRequest(`/competitions/${competitionId}/matches`, filters)
  }

  // Listar times de uma competição
  async getCompetitionTeams(competitionId, season = null) {
    const params = season ? { season } : {}
    return await this.makeRequest(`/competitions/${competitionId}/teams`, params)
  }

  // Obter classificação de uma competição
  async getCompetitionStandings(competitionId, filters = {}) {
    return await this.makeRequest(`/competitions/${competitionId}/standings`, filters)
  }

  // Listar artilheiros de uma competição
  async getCompetitionScorers(competitionId, filters = {}) {
    return await this.makeRequest(`/competitions/${competitionId}/scorers`, filters)
  }

  // Obter detalhes de um time
  async getTeam(teamId) {
    return await this.makeRequest(`/teams/${teamId}`)
  }

  // Listar partidas de um time
  async getTeamMatches(teamId, filters = {}) {
    return await this.makeRequest(`/teams/${teamId}/matches`, filters)
  }

  // Obter detalhes de uma partida
  async getMatch(matchId) {
    return await this.makeRequest(`/matches/${matchId}`)
  }

  // Listar partidas (geral)
  async getMatches(filters = {}) {
    return await this.makeRequest("/matches", filters)
  }

  // Obter histórico de confrontos
  async getMatchHead2Head(matchId, filters = {}) {
    return await this.makeRequest(`/matches/${matchId}/head2head`, filters)
  }

  // Obter detalhes de uma pessoa (jogador/técnico)
  async getPerson(personId) {
    return await this.makeRequest(`/persons/${personId}`)
  }

  // Listar partidas de uma pessoa
  async getPersonMatches(personId, filters = {}) {
    return await this.makeRequest(`/persons/${personId}/matches`, filters)
  }

  // Listar áreas (países/regiões)
  async getAreas() {
    return await this.makeRequest("/areas")
  }

  // Obter detalhes de uma área
  async getArea(areaId) {
    return await this.makeRequest(`/areas/${areaId}`)
  }
}

// Funções utilitárias
const footballAPI = new FootballAPI()

// Exemplo de uso das principais funções
async function demonstrateAPI() {
  try {
    console.log("🏆 Demonstração da API Football-Data.org")

    // 1. Listar competições
    console.log("\n📋 Carregando competições...")
    const competitions = await footballAPI.getCompetitions()
    console.log(`Encontradas ${competitions.competitions?.length || 0} competições`)

    // 2. Obter partidas do dia
    console.log("\n⚽ Carregando partidas de hoje...")
    const todayMatches = await footballAPI.getMatches({
      dateFrom: new Date().toISOString().split("T")[0],
      dateTo: new Date().toISOString().split("T")[0],
    })
    console.log(`Encontradas ${todayMatches.matches?.length || 0} partidas hoje`)

    // 3. Exemplo com Premier League
    console.log("\n🏴󠁧󠁢󠁥󠁮󠁧󠁿 Carregando dados da Premier League...")
    const plMatches = await footballAPI.getCompetitionMatches("PL", {
      status: "SCHEDULED",
      limit: 10,
    })
    console.log(`Próximas ${plMatches.matches?.length || 0} partidas da Premier League`)

    // 4. Classificação da Premier League
    const plStandings = await footballAPI.getCompetitionStandings("PL")
    console.log(`Classificação da Premier League: ${plStandings.standings?.[0]?.table?.length || 0} times`)

    console.log("\n✅ Demonstração concluída!")
  } catch (error) {
    console.error("❌ Erro na demonstração:", error.message)
  }
}

// Executar demonstração apenas se chamado diretamente
if (typeof process !== 'undefined' && process.argv && process.argv[1] && process.argv[1].includes('football-api.js')) {
  demonstrateAPI()
}

// Exportar para uso em outros módulos
if (typeof module !== "undefined" && module.exports) {
  module.exports = { FootballAPI, footballAPI }
}

// Exportar para ES modules
export { FootballAPI, footballAPI }
