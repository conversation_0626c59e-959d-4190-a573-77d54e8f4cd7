import React, { useState } from 'react'
import Image from 'next/image'

interface CompetitionEmblemProps {
  src?: string
  alt: string
  competitionCode?: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const CompetitionEmblem: React.FC<CompetitionEmblemProps> = ({ 
  src, 
  alt, 
  competitionCode,
  size = 'md', 
  className = '' 
}) => {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-10 h-10'
  }

  // Emblemas padrão para competições conhecidas
  const defaultEmblems: { [key: string]: string } = {
    'PL': 'https://crests.football-data.org/PL.png',
    'PD': 'https://crests.football-data.org/PD.png',
    'SA': 'https://crests.football-data.org/SA.png',
    'BL1': 'https://crests.football-data.org/BL1.png',
    'FL1': 'https://crests.football-data.org/FL1.png',
    'CL': 'https://crests.football-data.org/CL.png',
    'EL': 'https://crests.football-data.org/EL.png',
    'WC': 'https://crests.football-data.org/WC.png',
    'EC': 'https://crests.football-data.org/EC.png',
    'CLI': 'https://crests.football-data.org/CLI.png',
    'BSA': '/images/brasileirao-logo.svg', // Brasileirão Série A
    'BSB': '/images/brasileirao-logo.svg', // Brasileirão Série B
    'BRASILEIRAO-SERIE-A': '/images/brasileirao-logo.svg',
    'PREMIER-LEAGUE': 'https://crests.football-data.org/PL.png',
    'LA-LIGA': 'https://crests.football-data.org/PD.png',
    'SERIE-A': 'https://crests.football-data.org/SA.png',
    'BUNDESLIGA': 'https://crests.football-data.org/BL1.png',
    'LIGUE-1': 'https://crests.football-data.org/FL1.png'
  }

  const fallbackImage = '/images/competition-placeholder.png'
  let imageSrc = src

  // Se não tem src, tentar usar emblema padrão baseado no código
  if (!imageSrc && competitionCode) {
    imageSrc = defaultEmblems[competitionCode]
  }

  // Se ainda não tem imagem, usar fallback
  if (imageError || !imageSrc) {
    imageSrc = fallbackImage
  }

  const handleImageError = () => {
    setImageError(true)
    setIsLoading(false)
  }

  const handleImageLoad = () => {
    setIsLoading(false)
  }

  return (
    <div className={`${sizeClasses[size]} relative flex items-center justify-center ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      
      <Image
        src={imageSrc}
        alt={alt}
        width={size === 'sm' ? 16 : size === 'md' ? 24 : 40}
        height={size === 'sm' ? 16 : size === 'md' ? 24 : 40}
        className={`object-contain ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        unoptimized // Para permitir URLs externas
      />
      
      {imageError && (
        <div className="absolute inset-0 bg-gradient-to-br from-green-500 to-green-600 rounded flex items-center justify-center shadow-md">
          <span className={`font-bold text-white ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'}`}>
            {(competitionCode || alt).substring(0, 2).toUpperCase()}
          </span>
        </div>
      )}
    </div>
  )
}

export default CompetitionEmblem
