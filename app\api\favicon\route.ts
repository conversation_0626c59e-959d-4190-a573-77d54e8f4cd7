import { NextResponse } from 'next/server'

export async function GET() {
  // Retornar um favicon SVG simples
  const svg = `
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="32" height="32" rx="6" fill="#10B981"/>
      <circle cx="16" cy="12" r="6" fill="white"/>
      <rect x="10" y="18" width="12" height="8" rx="2" fill="white"/>
      <circle cx="13" cy="22" r="1" fill="#10B981"/>
      <circle cx="19" cy="22" r="1" fill="#10B981"/>
    </svg>
  `

  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=86400', // Cache por 24 horas
    },
  })
}