import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: Request) {
  try {
    await initializeDatabase()

    const { type = 'all' } = await request.json()

    console.log(`🚀 Iniciando sincronização com football-data.org API - Tipo: ${type}`)

    // Configuração da API
    const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'YOUR_TOKEN_HERE'
    const FOOTBALL_API_BASE = 'https://api.football-data.org/v4'

    if (!FOOTBALL_API_TOKEN || FOOTBALL_API_TOKEN === 'YOUR_TOKEN_HERE') {
      return NextResponse.json({
        success: false,
        error: "Token da API não configurado",
        message: "Configure a variável FOOTBALL_API_TOKEN no arquivo .env"
      }, { status: 400 })
    }

    const headers = {
      'X-Auth-Token': FOOTBALL_API_TOKEN,
      'Content-Type': 'application/json'
    }

    let result = {}

    if (type === 'competitions' || type === 'all') {
      console.log('🏆 Sincronizando competições...')
      
      try {
        const response = await fetch(`${FOOTBALL_API_BASE}/competitions`, { headers })
        
        if (!response.ok) {
          throw new Error(`API Error: ${response.status} - ${response.statusText}`)
        }

        const data = await response.json()
        const competitions = data.competitions || []

        console.log(`📊 Encontradas ${competitions.length} competições na API`)

        // Filtrar TODAS as competições relevantes (mais abrangente)
        const relevantCompetitions = competitions.filter((comp: any) => {
          const name = comp.name.toLowerCase()
          const area = comp.area?.name?.toLowerCase() || ''
          const code = comp.code?.toLowerCase() || ''

          return (
            // América do Sul
            area.includes('brazil') ||
            area.includes('south america') ||
            area.includes('argentina') ||
            area.includes('uruguay') ||
            area.includes('chile') ||
            area.includes('colombia') ||
            area.includes('peru') ||
            area.includes('ecuador') ||
            area.includes('bolivia') ||
            area.includes('paraguay') ||
            area.includes('venezuela') ||

            // Europa - Principais Ligas
            area.includes('england') ||
            area.includes('spain') ||
            area.includes('italy') ||
            area.includes('germany') ||
            area.includes('france') ||
            area.includes('portugal') ||
            area.includes('netherlands') ||
            area.includes('belgium') ||
            area.includes('turkey') ||
            area.includes('russia') ||
            area.includes('ukraine') ||
            area.includes('poland') ||
            area.includes('czech republic') ||
            area.includes('austria') ||
            area.includes('switzerland') ||
            area.includes('denmark') ||
            area.includes('sweden') ||
            area.includes('norway') ||
            area.includes('scotland') ||
            area.includes('greece') ||
            area.includes('croatia') ||
            area.includes('serbia') ||

            // Competições Internacionais
            area.includes('europe') ||
            area.includes('world') ||
            name.includes('champions league') ||
            name.includes('europa league') ||
            name.includes('conference league') ||
            name.includes('copa libertadores') ||
            name.includes('copa sudamericana') ||
            name.includes('copa america') ||
            name.includes('world cup') ||
            name.includes('euro') ||

            // Por nome específico
            name.includes('premier league') ||
            name.includes('la liga') ||
            name.includes('serie a') ||
            name.includes('bundesliga') ||
            name.includes('ligue 1') ||
            name.includes('primeira liga') ||
            name.includes('eredivisie') ||
            name.includes('brasileirão') ||
            name.includes('brasileiro') ||
            name.includes('copa do brasil') ||
            name.includes('libertadores') ||
            name.includes('sul-americana') ||
            name.includes('sudamericana') ||

            // Por código
            ['PL', 'PD', 'SA', 'BL1', 'FL1', 'PPL', 'DED', 'BSA', 'CLI', 'CSA', 'CL', 'EL', 'ECL'].includes(comp.code) ||

            // Competições nacionais importantes
            name.includes('championship') ||
            name.includes('primera división') ||
            name.includes('liga mx') ||
            name.includes('mls') ||
            name.includes('j1 league') ||
            name.includes('k league') ||
            name.includes('chinese super league') ||
            name.includes('indian super league') ||
            name.includes('a-league')
          )
        })

        console.log(`🎯 Filtrando para ${relevantCompetitions.length} competições relevantes de ${competitions.length} totais`)

        let syncedCompetitions = 0

        // Processar TODAS as competições relevantes (sem limite)
        for (const comp of relevantCompetitions) {
          try {
            // Verificar se já existe
            const existing = await executeQuery(`
              SELECT id FROM campeonatos WHERE api_id = ?
            `, [comp.id.toString()])

            const competitionData = {
              nome: comp.name,
              descricao: comp.name,
              pais: comp.area?.name || 'Internacional',
              temporada: comp.currentSeason?.startDate?.substring(0, 4) || '2024',
              status: 'ativo',
              data_inicio: comp.currentSeason?.startDate || null,
              data_fim: comp.currentSeason?.endDate || null,
              api_id: comp.id.toString(),
              logo_url: comp.emblem || null
            }

            if (existing.length === 0) {
              // Inserir nova competição
              await executeQuery(`
                INSERT INTO campeonatos (
                  nome, descricao, pais, temporada, status, 
                  data_inicio, data_fim, api_id, logo_url, data_criacao
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
              `, [
                competitionData.nome,
                competitionData.descricao,
                competitionData.pais,
                competitionData.temporada,
                competitionData.status,
                competitionData.data_inicio,
                competitionData.data_fim,
                competitionData.api_id,
                competitionData.logo_url
              ])
              
              syncedCompetitions++
              console.log(`✅ Adicionada: ${comp.name} (${comp.area?.name})`)
            } else {
              // Atualizar existente
              await executeQuery(`
                UPDATE campeonatos 
                SET nome = ?, descricao = ?, pais = ?, temporada = ?, 
                    data_inicio = ?, data_fim = ?, logo_url = ?
                WHERE api_id = ?
              `, [
                competitionData.nome,
                competitionData.descricao,
                competitionData.pais,
                competitionData.temporada,
                competitionData.data_inicio,
                competitionData.data_fim,
                competitionData.logo_url,
                competitionData.api_id
              ])
              
              console.log(`🔄 Atualizada: ${comp.name}`)
            }

            // Delay menor para processar mais rápido
            await new Promise(resolve => setTimeout(resolve, 100))

          } catch (compError) {
            console.error(`❌ Erro ao processar ${comp.name}:`, compError)
          }
        }

        result = {
          ...result,
          competitions: {
            total_found: competitions.length,
            relevant: relevantCompetitions.length,
            synced: syncedCompetitions
          }
        }

      } catch (apiError) {
        console.error('❌ Erro na API de competições:', apiError)
        result = {
          ...result,
          competitions: {
            error: (apiError as Error).message
          }
        }
      }
    }

    if (type === 'matches' || type === 'all') {
      console.log('⚽ Sincronizando partidas...')
      
      try {
        // Buscar TODAS as competições com api_id
        const competitions = await executeQuery(`
          SELECT id, api_id, nome FROM campeonatos
          WHERE api_id IS NOT NULL AND status = 'ativo'
          ORDER BY id DESC
        `)

        let syncedMatches = 0

        for (const competition of competitions) {
          try {
            console.log(`🔄 Buscando partidas de ${competition.nome}...`)
            
            const response = await fetch(
              `${FOOTBALL_API_BASE}/competitions/${competition.api_id}/matches?status=SCHEDULED`,
              { headers }
            )

            if (!response.ok) {
              console.log(`⚠️ Erro ao buscar partidas de ${competition.nome}: ${response.status}`)
              continue
            }

            const data = await response.json()
            const matches = data.matches || []

            console.log(`📊 ${matches.length} partidas encontradas em ${competition.nome}`)

            for (const match of matches.slice(0, 100)) { // Mais partidas por competição
              try {
                // Verificar se a partida já existe
                const existingMatch = await executeQuery(`
                  SELECT id FROM jogos WHERE api_id = ?
                `, [match.id.toString()])

                if (existingMatch.length === 0) {
                  // Buscar ou criar times
                  const homeTeamId = await getOrCreateTeam(match.homeTeam)
                  const awayTeamId = await getOrCreateTeam(match.awayTeam)

                  if (homeTeamId && awayTeamId) {
                    await executeQuery(`
                      INSERT INTO jogos (
                        campeonato_id, time_casa_id, time_fora_id, data_jogo,
                        status, api_id, data_criacao
                      ) VALUES (?, ?, ?, ?, ?, ?, NOW())
                    `, [
                      competition.id,
                      homeTeamId,
                      awayTeamId,
                      match.utcDate,
                      'agendado',
                      match.id.toString()
                    ])

                    syncedMatches++
                  }
                }

              } catch (matchError) {
                console.error(`❌ Erro ao processar partida ${match.id}:`, matchError)
              }
            }

            // Delay menor entre competições
            await new Promise(resolve => setTimeout(resolve, 500))

          } catch (compError) {
            console.error(`❌ Erro ao processar competição ${competition.nome}:`, compError)
          }
        }

        result = {
          ...result,
          matches: {
            synced: syncedMatches
          }
        }

      } catch (matchError) {
        console.error('❌ Erro na sincronização de partidas:', matchError)
        result = {
          ...result,
          matches: {
            error: (matchError as Error).message
          }
        }
      }
    }

    // Função auxiliar para buscar ou criar time
    async function getOrCreateTeam(teamData: any): Promise<number | null> {
      try {
        // Verificar se o time já existe
        const existing = await executeQuery(`
          SELECT id FROM times WHERE api_id = ? OR nome = ?
        `, [teamData.id?.toString(), teamData.name])

        if (existing.length > 0) {
          return existing[0].id
        }

        // Criar novo time
        const result = await executeQuery(`
          INSERT INTO times (
            nome, nome_curto, logo_url, api_id, data_criacao
          ) VALUES (?, ?, ?, ?, NOW())
        `, [
          teamData.name,
          teamData.shortName || teamData.name.substring(0, 10),
          teamData.crest || null,
          teamData.id?.toString()
        ])

        return result.insertId
      } catch (error) {
        console.error(`❌ Erro ao criar time ${teamData.name}:`, error)
        return null
      }
    }

    return NextResponse.json({
      success: true,
      message: "Sincronização concluída com sucesso!",
      data: result
    })

  } catch (error) {
    console.error("❌ Erro na sincronização:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
