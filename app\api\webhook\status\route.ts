import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()
    
    const { searchParams } = new URL(request.url)
    const transaction_id = searchParams.get('transaction_id')
    const order_id = searchParams.get('order_id')
    
    // Se não foi fornecido ID específico, retornar estatísticas gerais
    if (!transaction_id && !order_id) {
      const bilhetesStats = await executeQuery(`
        SELECT 
          status,
          COUNT(*) as count,
          SUM(valor_total) as total_valor,
          AVG(valor_total) as valor_medio
        FROM bilhetes 
        GROUP BY status
        ORDER BY count DESC
      `)

      const bilhetesRecentes = await executeQuery(`
        SELECT 
          codigo, 
          status, 
          valor_total, 
          transaction_id,
          created_at,
          usuario_nome
        FROM bilhetes 
        ORDER BY created_at DESC 
        LIMIT 10
      `)

      const totalBilhetes = await executeQuery(`
        SELECT COUNT(*) as total FROM bilhetes
      `)

      return NextResponse.json({
        message: "Status geral dos pagamentos",
        timestamp: new Date().toISOString(),
        status: "active",
        estatisticas: Array.isArray(bilhetesStats) ? bilhetesStats.reduce((acc: any, item: any) => {
          acc[item.status] = {
            count: item.count,
            total_valor: parseFloat(item.total_valor || 0),
            valor_medio: parseFloat(item.valor_medio || 0),
            percentual: ((item.count / (totalBilhetes[0] as any)?.total) * 100).toFixed(1) + '%'
          }
          return acc
        }, {}) : {},
        bilhetes_recentes: Array.isArray(bilhetesRecentes) ? bilhetesRecentes.map((b: any) => ({
          codigo: b.codigo,
          status: b.status,
          valor: parseFloat(b.valor_total),
          transaction_id: b.transaction_id,
          usuario: b.usuario_nome,
          data: b.created_at
        })) : [],
        total_bilhetes: (totalBilhetes[0] as any)?.total || 0
      })
    }

    // Buscar bilhete específico
    const paymentId = transaction_id || order_id
    const bilhete = await executeQuery(`
      SELECT 
        id,
        codigo,
        status,
        valor_total,
        transaction_id,
        usuario_nome,
        usuario_email,
        created_at,
        updated_at
      FROM bilhetes 
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [paymentId, paymentId])

    if (!Array.isArray(bilhete) || bilhete.length === 0) {
      return NextResponse.json({
        message: "Pagamento não encontrado",
        timestamp: new Date().toISOString(),
        status: "not_found",
        transaction_id: transaction_id,
        order_id: order_id,
        searched_id: paymentId
      }, { status: 404 })
    }

    const bilheteData = bilhete[0] as any
    
    // Mapear status para formato padronizado
    let statusPadronizado = bilheteData.status
    let isPago = false
    let isPendente = false
    
    switch (bilheteData.status) {
      case 'pago':
        statusPadronizado = 'pago'
        isPago = true
        break
      case 'pendente':
        statusPadronizado = 'pendente'
        isPendente = true
        break
      case 'cancelado':
        statusPadronizado = 'cancelado'
        break
      default:
        statusPadronizado = 'pendente'
        isPendente = true
    }

    return NextResponse.json({
      message: "Status do pagamento encontrado",
      timestamp: new Date().toISOString(),
      status: statusPadronizado,
      bilhete: {
        id: bilheteData.id,
        codigo: bilheteData.codigo,
        valor: parseFloat(bilheteData.valor_total),
        transaction_id: bilheteData.transaction_id,
        usuario_nome: bilheteData.usuario_nome,
        usuario_email: bilheteData.usuario_email,
        created_at: bilheteData.created_at,
        updated_at: bilheteData.updated_at
      },
      payment_info: {
        is_paid: isPago,
        is_pending: isPendente,
        is_cancelled: statusPadronizado === 'cancelado',
        status_original: bilheteData.status,
        status_padronizado: statusPadronizado
      }
    })

  } catch (error) {
    console.error('❌ Erro ao consultar status:', error)
    return NextResponse.json({
      message: "Erro interno do servidor",
      timestamp: new Date().toISOString(),
      status: "error",
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: "Use GET para consultar status",
    timestamp: new Date().toISOString(),
    status: "method_not_allowed",
    usage: {
      method: "GET",
      parameters: {
        transaction_id: "ID da transação (opcional)",
        order_id: "ID do pedido (opcional)"
      },
      examples: [
        "/api/webhook/status",
        "/api/webhook/status?transaction_id=TXN123",
        "/api/webhook/status?order_id=ORDER456"
      ]
    }
  }, { status: 405 })
}
