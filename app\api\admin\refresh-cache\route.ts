import { NextResponse } from "next/server"

export async function POST() {
  try {
    console.log("🔄 Forçando atualização de cache...")

    // Adicionar headers para evitar cache
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }

    return NextResponse.json({
      success: true,
      message: "Cache atualizado com sucesso",
      timestamp: new Date().toISOString()
    }, { headers })

  } catch (error) {
    console.error("❌ Erro ao atualizar cache:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
