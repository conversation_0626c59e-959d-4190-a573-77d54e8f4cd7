import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    const { bilhete_codigo, user_id } = await request.json()

    console.log("🔍 Verificando status de pagamento:", { bilhete_codigo, user_id })

    if (!bilhete_codigo) {
      return NextResponse.json({ 
        error: "bilhete_codigo é obrigatório" 
      }, { status: 400 })
    }

    try {
      // Buscar bilhete no banco
      const bilhetes = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE codigo = ? AND user_id = ?
        LIMIT 1
      `, [bilhete_codigo, user_id])

      if (!Array.isArray(bilhetes) || bilhetes.length === 0) {
        return NextResponse.json({
          error: "Bilhete não encontrado",
          bilhete_codigo
        }, { status: 404 })
      }

      const bilhete = bilhetes[0] as any
      
      console.log("🎫 Bilhete encontrado:", {
        codigo: bilhete.codigo,
        status: bilhete.status,
        valor: bilhete.valor_total,
        created_at: bilhete.created_at
      })

      // Se já está pago, retornar sucesso
      if (bilhete.status === 'pago') {
        return NextResponse.json({
          message: "Bilhete já está pago",
          status: "pago",
          bilhete: {
            codigo: bilhete.codigo,
            status: bilhete.status,
            valor: parseFloat(bilhete.valor_total),
            created_at: bilhete.created_at,
            updated_at: bilhete.updated_at
          },
          already_paid: true
        })
      }

      // Verificar se o bilhete é muito antigo (mais de 1 hora) e ainda está pendente
      const createdAt = new Date(bilhete.created_at)
      const now = new Date()
      const diffMinutes = (now.getTime() - createdAt.getTime()) / (1000 * 60)

      console.log(`⏰ Bilhete criado há ${Math.round(diffMinutes)} minutos`)

      // Se está pendente há mais de 5 minutos, permitir confirmação manual
      const canConfirmManually = diffMinutes > 5

      return NextResponse.json({
        message: "Status do bilhete verificado",
        status: bilhete.status,
        bilhete: {
          codigo: bilhete.codigo,
          status: bilhete.status,
          valor: parseFloat(bilhete.valor_total),
          created_at: bilhete.created_at,
          updated_at: bilhete.updated_at,
          minutes_since_creation: Math.round(diffMinutes)
        },
        can_confirm_manually: canConfirmManually,
        already_paid: false
      })

    } catch (dbError) {
      console.error("❌ Erro no banco de dados:", dbError)
      return NextResponse.json({
        error: "Erro interno no servidor",
        details: dbError instanceof Error ? dbError.message : String(dbError)
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro geral:", error)
    return NextResponse.json({
      error: "Erro interno no servidor",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const bilhete_codigo = searchParams.get('bilhete_codigo')
  const user_id = searchParams.get('user_id')

  if (!bilhete_codigo || !user_id) {
    return NextResponse.json({ 
      error: "bilhete_codigo e user_id são obrigatórios" 
    }, { status: 400 })
  }

  // Redirecionar para POST
  return NextResponse.json({
    message: "Use POST para verificar status",
    endpoint: "/api/payment/check-status",
    method: "POST",
    body: { bilhete_codigo, user_id }
  })
}
