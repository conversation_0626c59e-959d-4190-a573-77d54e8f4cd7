/**
 * Utilitários para detectar se estamos em modo de build
 */

/**
 * Verifica se estamos em modo de build do Next.js
 * @param request - Request object (opcional)
 * @returns true se estiver em modo de build
 */
export function isBuildMode(request?: Request): boolean {
  // Verificar variável de ambiente personalizada
  if (process.env.NEXT_BUILD_MODE === 'true') {
    return true
  }

  // Verificar variável de ambiente do Next.js
  if (process.env.NEXT_PHASE === 'phase-production-build') {
    return true
  }

  // Verificar se estamos em produção sem headers de request
  if (process.env.NODE_ENV === 'production' && request && !request.headers.get('host')) {
    return true
  }

  // Verificar se não há URL (indicativo de build)
  if (request && !request.url) {
    return true
  }

  return false
}

/**
 * Retorna uma resposta padrão para modo de build
 * @param data - Dados padrão para retornar
 * @returns NextResponse com dados vazios
 */
export function buildModeResponse(data: Record<string, any> = {}) {
  return {
    ...data,
    message: "Build mode - static generation",
    timestamp: new Date().toISOString(),
    build_mode: true
  }
}

/**
 * Middleware para verificar build mode em rotas API
 * @param request - Request object
 * @param defaultResponse - Resposta padrão para build mode
 * @returns null se não estiver em build mode, ou resposta de build mode
 */
export function checkBuildMode(request: Request, defaultResponse: Record<string, any> = {}) {
  if (isBuildMode(request)) {
    return buildModeResponse(defaultResponse)
  }
  return null
}
