import { initializeDatabase, runMigrations } from "../lib/database.js"

async function main() {
  try {
    console.log("🚀 Inicializando banco de dados...")

    // Inicializar o banco
    await initializeDatabase()

    // Executar migrations
    await runMigrations()

    console.log("✅ Banco de dados inicializado com sucesso!")
    console.log("📁 Localização: database/sistema-bolao.sqlite")

    process.exit(0)
  } catch (error) {
    console.error("❌ Erro ao inicializar banco:", error)
    process.exit(1)
  }
}

main()
