/**
 * Sistema SIMPLES de Exibição de Logos dos Times
 * APENAS BUSCA E EXIBE - NUNCA ALTERA OS LOGOS NO BANCO
 */

/**
 * Busca o logo do time no banco de dados (SOMENTE LEITURA)
 * @param {number} timeId - ID do time
 * @param {function} executeQuery - Função de query do banco
 * @returns {string|null} - URL do logo ou null
 */
export async function getTeamLogo(timeId, executeQuery) {
  try {
    if (!timeId) return null
    
    const result = await executeQuery(
      'SELECT logo_url FROM times WHERE id = ? LIMIT 1',
      [timeId]
    )
    
    if (result.length > 0 && result[0].logo_url) {
      return result[0].logo_url
    }
    
    return null
  } catch (error) {
    console.warn(`⚠️ Erro ao buscar logo do time ${timeId}:`, error.message)
    return null
  }
}

/**
 * Busca informações completas do time (SOMENTE LEITURA)
 * @param {number} timeId - ID do time
 * @param {function} executeQuery - Função de query do banco
 * @returns {object|null} - Dados do time ou null
 */
export async function getTeamInfo(timeId, executeQuery) {
  try {
    if (!timeId) return null
    
    const result = await executeQuery(
      'SELECT id, nome, nome_curto, logo_url, image_id FROM times WHERE id = ? LIMIT 1',
      [timeId]
    )
    
    if (result.length > 0) {
      return {
        id: result[0].id,
        nome: result[0].nome,
        nome_curto: result[0].nome_curto,
        logo_url: result[0].logo_url,
        image_id: result[0].image_id
      }
    }
    
    return null
  } catch (error) {
    console.warn(`⚠️ Erro ao buscar informações do time ${timeId}:`, error.message)
    return null
  }
}

/**
 * Busca logos de múltiplos times de uma vez (SOMENTE LEITURA)
 * @param {number[]} timeIds - Array de IDs dos times
 * @param {function} executeQuery - Função de query do banco
 * @returns {object} - Objeto com timeId como chave e logo_url como valor
 */
export async function getMultipleTeamLogos(timeIds, executeQuery) {
  try {
    if (!timeIds || timeIds.length === 0) return {}
    
    const placeholders = timeIds.map(() => '?').join(',')
    const result = await executeQuery(
      `SELECT id, logo_url FROM times WHERE id IN (${placeholders})`,
      timeIds
    )
    
    const logos = {}
    result.forEach(row => {
      logos[row.id] = row.logo_url
    })
    
    return logos
  } catch (error) {
    console.warn('⚠️ Erro ao buscar logos múltiplos:', error.message)
    return {}
  }
}

/**
 * Formata dados do time para exibição (SOMENTE FORMATAÇÃO)
 * @param {object} teamData - Dados do time do banco
 * @returns {object} - Dados formatados para o frontend
 */
export function formatTeamForDisplay(teamData) {
  if (!teamData) {
    return {
      id: null,
      name: 'Time não encontrado',
      shortName: 'N/A',
      crest: '/placeholder.svg'
    }
  }
  
  return {
    id: teamData.id,
    name: teamData.nome || 'Nome não disponível',
    shortName: teamData.nome_curto || teamData.nome || 'N/A',
    crest: teamData.logo_url || '/placeholder.svg'
  }
}

/**
 * Busca e formata dados de um jogo com logos dos times (SOMENTE LEITURA)
 * @param {object} jogoData - Dados do jogo
 * @param {function} executeQuery - Função de query do banco
 * @returns {object} - Dados do jogo formatados com logos
 */
export async function formatGameWithLogos(jogoData, executeQuery) {
  try {
    // Buscar informações dos times
    const timeCasa = await getTeamInfo(jogoData.time_casa_id, executeQuery)
    const timeFora = await getTeamInfo(jogoData.time_fora_id, executeQuery)
    
    return {
      ...jogoData,
      homeTeam: formatTeamForDisplay(timeCasa),
      awayTeam: formatTeamForDisplay(timeFora)
    }
  } catch (error) {
    console.warn('⚠️ Erro ao formatar jogo com logos:', error.message)
    
    // Retornar dados básicos em caso de erro
    return {
      ...jogoData,
      homeTeam: {
        id: jogoData.time_casa_id,
        name: jogoData.time_casa_nome || 'Time Casa',
        shortName: jogoData.time_casa_curto || 'TC',
        crest: '/placeholder.svg'
      },
      awayTeam: {
        id: jogoData.time_fora_id,
        name: jogoData.time_fora_nome || 'Time Fora',
        shortName: jogoData.time_fora_curto || 'TF',
        crest: '/placeholder.svg'
      }
    }
  }
}

/**
 * Middleware para garantir que NUNCA se altere logos (PROTEÇÃO)
 * Esta função bloqueia qualquer tentativa de alterar logos
 */
export function protectTeamLogos() {
  console.log('🛡️ PROTEÇÃO ATIVA: Logos dos times são SOMENTE LEITURA')
  console.log('📋 Sistema configurado para APENAS BUSCAR e EXIBIR logos')
  console.log('🚫 NENHUMA alteração automática será feita nos logos')
  
  return {
    readOnly: true,
    message: 'Logos dos times são fixos e não podem ser alterados automaticamente'
  }
}

/**
 * Função de verificação do sistema (SOMENTE LEITURA)
 * @param {function} executeQuery - Função de query do banco
 */
export async function verifyTeamLogosReadOnly(executeQuery) {
  try {
    console.log('🔍 VERIFICAÇÃO DO SISTEMA (SOMENTE LEITURA)')
    console.log('=' .repeat(50))
    
    // Contar times com logos
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url IS NOT NULL AND logo_url != '' THEN 1 END) as com_logo,
        COUNT(CASE WHEN logo_url IS NULL OR logo_url = '' THEN 1 END) as sem_logo
      FROM times
    `)
    
    const data = stats[0]
    console.log(`📊 Total de times: ${data.total}`)
    console.log(`✅ Com logo: ${data.com_logo} (${((data.com_logo/data.total)*100).toFixed(1)}%)`)
    console.log(`❌ Sem logo: ${data.sem_logo} (${((data.sem_logo/data.total)*100).toFixed(1)}%)`)
    
    // Mostrar alguns exemplos
    const exemplos = await executeQuery(`
      SELECT nome, logo_url 
      FROM times 
      WHERE logo_url IS NOT NULL AND logo_url != '' 
      ORDER BY nome 
      LIMIT 10
    `)
    
    console.log('\n📋 Exemplos de times com logos:')
    exemplos.forEach(time => {
      console.log(`  ✅ ${time.nome}: ${time.logo_url}`)
    })
    
    console.log('\n🛡️ SISTEMA EM MODO SOMENTE LEITURA')
    console.log('📋 Logos são buscados do banco e exibidos sem alterações')
    console.log('=' .repeat(50))
    
    return {
      total: data.total,
      com_logo: data.com_logo,
      sem_logo: data.sem_logo,
      readOnly: true
    }
    
  } catch (error) {
    console.error('❌ Erro na verificação:', error.message)
    return null
  }
}
