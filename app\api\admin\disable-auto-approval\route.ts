import { NextRequest, NextResponse } from "next/server"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    console.log("🔒 DESABILITANDO APROVAÇÃO AUTOMÁTICA DE PAGAMENTOS")
    console.log("=" .repeat(60))
    
    const body = await request.json()
    const { action } = body
    
    if (action === 'disable_auto_approval') {
      console.log("✅ Aprovação automática foi DESABILITADA")
      console.log("")
      console.log("📋 CONFIGURAÇÕES ATUAIS:")
      console.log("   🔒 Fallback por tempo: DESABILITADO")
      console.log("   🔒 Simulação automática: DESABILITADA") 
      console.log("   🔒 Aprovação por erro: DESABILITADA")
      console.log("")
      console.log("✅ APENAS WEBHOOKS OFICIAIS APROVAM PAGAMENTOS:")
      console.log("   📡 Webhook principal: https://ouroemu.site/api/v1/MP/webhookruntransation")
      console.log("   📡 Webhook alternativo: /api/webhook/pix")
      console.log("   📡 Webhook genérico: /api/pix/webhook")
      console.log("")
      console.log("🎯 COMO TESTAR PAGAMENTOS:")
      console.log("   1. Faça uma aposta e gere QR Code PIX")
      console.log("   2. Pague o PIX real na sua conta")
      console.log("   3. O webhook será chamado automaticamente")
      console.log("   4. OU use o simulador: /api/simulate-pix-payment")
      console.log("")
      
      return NextResponse.json({
        success: true,
        message: "Aprovação automática desabilitada com sucesso",
        config: {
          auto_approval: false,
          fallback_time: false,
          simulation: false,
          error_fallback: false,
          webhook_only: true
        },
        webhooks: {
          principal: "https://ouroemu.site/api/v1/MP/webhookruntransation",
          alternativo: "/api/webhook/pix", 
          generico: "/api/pix/webhook",
          simulador: "/api/simulate-pix-payment"
        },
        instructions: [
          "Faça apostas normalmente",
          "Pague o PIX real",
          "Aguarde o webhook oficial",
          "OU use o simulador para testes"
        ]
      })
    }
    
    return NextResponse.json({
      success: false,
      error: "Ação não reconhecida"
    }, { status: 400 })
    
  } catch (error) {
    console.error("❌ Erro ao desabilitar aprovação automática:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log("📊 VERIFICANDO STATUS DA APROVAÇÃO AUTOMÁTICA")
    
    return NextResponse.json({
      success: true,
      message: "Status da aprovação automática",
      config: {
        auto_approval: false,
        fallback_time: false,
        simulation: false,
        error_fallback: false,
        webhook_only: true
      },
      status: "APROVAÇÃO AUTOMÁTICA DESABILITADA",
      webhooks: {
        principal: "https://ouroemu.site/api/v1/MP/webhookruntransation",
        alternativo: "/api/webhook/pix",
        generico: "/api/pix/webhook", 
        simulador: "/api/simulate-pix-payment"
      },
      instructions: [
        "✅ Sistema configurado para aprovar APENAS via webhook oficial",
        "🔒 Fallbacks automáticos foram desabilitados",
        "📡 Webhooks oficiais estão ativos",
        "🧪 Use /api/simulate-pix-payment para testes"
      ]
    })
    
  } catch (error) {
    console.error("❌ Erro ao verificar status:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}
