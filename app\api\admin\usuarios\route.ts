import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"
import { getUsuarios, getUsuariosStats } from "@/lib/database"
import bcrypt from 'bcryptjs'

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const filters = {
      status: searchParams.get("status") || undefined,
      tipo: searchParams.get("tipo") || undefined,
      search: searchParams.get("search") || undefined,
    }

    const [usuarios, stats] = await Promise.all([getUsuarios(filters), getUsuariosStats()])

    return NextResponse.json({
      usuarios: usuarios || [],
      stats: stats || { total: 0, ativos: 0, cambistas: 0, bloqueados: 0 },
    })
  } catch (error) {
    console.error("Erro ao buscar usuários:", error)
    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        usuarios: [],
        stats: { total: 0, ativos: 0, cambistas: 0, bloqueados: 0 },
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { id, nome, email, telefone, cpf, tipo, status, senha } = body

    console.log("✏️ Atualizando usuário:", { id, nome, email, tipo, status })

    // Validações básicas
    if (!id || !nome || !email) {
      return NextResponse.json(
        { error: "ID, nome e email são obrigatórios" },
        { status: 400 }
      )
    }

    // Verificar se usuário existe
    const existingUser = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE id = ?",
      [id]
    )

    if (!existingUser) {
      return NextResponse.json(
        { error: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    // Verificar se email já existe em outro usuário
    const emailExists = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE email = ? AND id != ?",
      [email, id]
    )

    if (emailExists) {
      return NextResponse.json(
        { error: "Email já está em uso por outro usuário" },
        { status: 409 }
      )
    }

    // Preparar parâmetros seguros (apenas campos que existem na tabela)
    const safeParams = {
      nome: nome || null,
      email: email || null,
      tipo: tipo || 'usuario',
      status: status || 'ativo'
    }

    console.log("🔍 Parâmetros seguros:", safeParams)

    // Preparar query de atualização (sem campos que não existem)
    let updateQuery = `
      UPDATE usuarios SET
        nome = ?,
        email = ?,
        tipo = ?,
        status = ?,
        data_atualizacao = NOW()
      WHERE id = ?
    `
    let updateParams = [
      safeParams.nome,
      safeParams.email,
      safeParams.tipo,
      safeParams.status,
      id
    ]

    // Se senha foi fornecida, incluir na atualização
    if (senha && senha.trim() !== '') {
      const senhaHash = await bcrypt.hash(senha, 10)
      updateQuery = `
        UPDATE usuarios SET
          nome = ?,
          email = ?,
          tipo = ?,
          status = ?,
          senha_hash = ?,
          data_atualizacao = NOW()
        WHERE id = ?
      `
      updateParams = [
        safeParams.nome,
        safeParams.email,
        safeParams.tipo,
        safeParams.status,
        senhaHash,
        id
      ]
    }

    console.log("🔍 Query:", updateQuery)
    console.log("🔍 Parâmetros:", updateParams)

    await executeQuery(updateQuery, updateParams)

    console.log("✅ Usuário atualizado com sucesso:", id)

    return NextResponse.json({
      success: true,
      message: "Usuário atualizado com sucesso"
    })

  } catch (error: any) {
    console.error("❌ Erro ao atualizar usuário:", error)

    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: "Não foi possível atualizar o usuário"
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json(
        { error: "ID do usuário é obrigatório" },
        { status: 400 }
      )
    }

    console.log("🗑️ Deletando usuário:", id)

    // Verificar se usuário existe
    const existingUser = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE id = ?",
      [id]
    )

    if (!existingUser) {
      return NextResponse.json(
        { error: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    // Deletar usuário (as indicações serão mantidas com foreign key null)
    await executeQuery("DELETE FROM usuarios WHERE id = ?", [id])

    console.log("✅ Usuário deletado com sucesso:", id)

    return NextResponse.json({
      success: true,
      message: "Usuário deletado com sucesso"
    })

  } catch (error: any) {
    console.error("❌ Erro ao deletar usuário:", error)

    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: "Não foi possível deletar o usuário"
      },
      { status: 500 }
    )
  }
}
