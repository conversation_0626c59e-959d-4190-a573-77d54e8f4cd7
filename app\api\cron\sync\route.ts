import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Iniciando sincronização automática...')
    
    // Fazer a sincronização chamando o endpoint existente
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
    const response = await fetch(`${baseUrl}/admin/sync`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`Erro na sincronização: ${response.status}`)
    }

    const result = await response.json()
    console.log('✅ Sincronização automática concluída:', result)

    return NextResponse.json({
      success: true,
      message: 'Sincronização automática executada com sucesso',
      timestamp: new Date().toISOString(),
      data: result
    })

  } catch (error) {
    console.error('❌ Erro na sincronização automática:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Erro na sincronização automática',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}

// Permitir apenas GET para este endpoint
export async function POST() {
  return NextResponse.json({ error: 'Método não permitido' }, { status: 405 })
}
