import { NextResponse } from "next/server"
import { syncFootballData, startAutoSync } from "@/lib/sync-football-data"

export async function POST(request: Request) {
  try {
    const { action } = await request.json()
    
    if (action === 'sync-now') {
      console.log('🔄 Sincronização manual temporariamente desabilitada')
      // await syncFootballData() // Temporariamente desabilitado devido a erros de banco

      return NextResponse.json({
        success: true,
        message: 'Sincronização temporariamente desabilitada - usando dados locais'
      })
    }
    
    if (action === 'start-auto-sync') {
      console.log('⏰ Sincronização automática temporariamente desabilitada')
      // startAutoSync() // Temporariamente desabilitado devido a erros de banco

      return NextResponse.json({
        success: true,
        message: 'Sincronização automática temporariamente desabilitada'
      })
    }
    
    return NextResponse.json({
      success: false,
      message: 'Ação não reconhecida'
    }, { status: 400 })
    
  } catch (error) {
    console.error('❌ Erro na API de sincronização:', error)
    
    return NextResponse.json({
      success: false,
      message: 'Erro interno do servidor',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      message: 'API de sincronização funcionando',
      endpoints: {
        'POST /api/sync': {
          'sync-now': 'Executa sincronização imediatamente',
          'start-auto-sync': 'Inicia sincronização automática de hora em hora'
        }
      }
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Erro ao verificar status da API'
    }, { status: 500 })
  }
}
