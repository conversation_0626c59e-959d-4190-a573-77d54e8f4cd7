"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Target, Plus, Users, DollarSign, Calendar, Loader2, Trophy, Clock, Edit, Trash2, Power, PowerOff } from "lucide-react"
import { CriarBolaoModal } from "@/components/admin/criar-bolao-modal"
import { toast } from "sonner"
import CompetitionEmblem from "@/components/CompetitionEmblem"

interface Bolao {
  id: number
  nome: string
  descricao: string
  valor_aposta: number
  premio_total: number
  max_participantes: number
  status: string
  data_inicio: string
  data_fim: string
  criado_por_nome: string
  campeonatos_selecionados: string
  partidas_selecionadas: string
}

interface Stats {
  ativos: number
  participantes: number
  faturamento: number
  finalizandoHoje: number
}

export default function BoloesPage() {
  const [boloes, setBoloes] = useState<Bolao[]>([])
  const [stats, setStats] = useState<Stats>({ ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 })
  const [loading, setLoading] = useState(true)
  const [showCriarBolaoModal, setShowCriarBolaoModal] = useState(false)
  const [showEditBolaoModal, setShowEditBolaoModal] = useState(false)
  const [editingBolao, setEditingBolao] = useState<Bolao | null>(null)
  const [actionLoading, setActionLoading] = useState<number | null>(null)
  const [loadingAction, setLoadingAction] = useState<number | null>(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/admin/boloes")

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        throw new Error("Resposta não é JSON válido")
      }

      const data = await response.json()

      if (data.success === false) {
        console.error("Erro da API:", data.message)
        setBoloes([])
        setStats({ ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 })
        return
      }

      setBoloes(data.boloes || [])
      setStats(data.stats || { ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 })
    } catch (error) {
      console.error("Erro ao carregar bolões:", error)
      setBoloes([])
      setStats({ ativos: 0, participantes: 0, faturamento: 0, finalizandoHoje: 0 })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const handleToggleStatus = async (bolaoId: number, currentStatus: string) => {
    try {
      setActionLoading(bolaoId)
      const newStatus = currentStatus === 'ativo' ? 'inativo' : 'ativo'

      const response = await fetch(`/api/admin/boloes/${bolaoId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        await fetchData() // Recarregar dados
        toast.success(`Bolão ${newStatus === 'ativo' ? 'ativado' : 'desativado'} com sucesso`)
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'Erro ao alterar status do bolão')
      }
    } catch (error) {
      console.error('Erro ao alterar status:', error)
      toast.error('Erro ao alterar status do bolão')
    } finally {
      setActionLoading(null)
    }
  }

  const handleDelete = async (bolaoId: number, bolaoNome: string) => {
    if (!confirm(`Tem certeza que deseja deletar o bolão "${bolaoNome}"?`)) {
      return
    }

    try {
      setActionLoading(bolaoId)

      const response = await fetch(`/api/admin/boloes/${bolaoId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchData() // Recarregar dados
        toast.success('Bolão deletado com sucesso')
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'Erro ao deletar bolão')
      }
    } catch (error) {
      console.error('Erro ao deletar:', error)
      toast.error('Erro ao deletar bolão')
    } finally {
      setActionLoading(null)
    }
  }

  const handleEdit = (bolaoId: number) => {
    const bolao = boloes.find(b => b.id === bolaoId)
    if (bolao) {
      setEditingBolao(bolao)
      setShowEditBolaoModal(true)
    } else {
      toast.error('Bolão não encontrado')
    }
  }

  const parseCampeonatos = (campeonatosStr: string) => {
    try {
      return JSON.parse(campeonatosStr || "[]")
    } catch {
      return []
    }
  }

  const parsePartidas = (partidasStr: string) => {
    try {
      return JSON.parse(partidasStr || "[]")
    } catch {
      return []
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Bolões</h1>
          <p className="text-gray-600 mt-2">Gerencie todos os bolões do sistema</p>
        </div>
        <Button className="bg-green-600 hover:bg-green-700" onClick={() => setShowCriarBolaoModal(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Novo Bolão
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Bolões Ativos</p>
                <p className="text-3xl font-bold text-gray-900">{stats.ativos}</p>
              </div>
              <Target className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Participantes</p>
                <p className="text-3xl font-bold text-gray-900">{stats.participantes}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Faturamento</p>
                <p className="text-3xl font-bold text-gray-900">
                  R$ {(stats.faturamento || 0).toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Finalizando Hoje</p>
                <p className="text-3xl font-bold text-gray-900">{stats.finalizandoHoje}</p>
              </div>
              <Calendar className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Bolões */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Bolões</CardTitle>
          <CardDescription>Todos os bolões cadastrados no sistema</CardDescription>
        </CardHeader>
        <CardContent>
          {boloes.length === 0 ? (
            <div className="text-center py-12">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Nenhum bolão encontrado</p>
            </div>
          ) : (
            <div className="space-y-6">
              {boloes.map((bolao) => {
                const campeonatos = parseCampeonatos(bolao.campeonatos_selecionados)
                const partidas = parsePartidas(bolao.partidas_selecionadas)

                return (
                  <Card key={bolao.id} className="hover:shadow-lg transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900">{bolao.nome}</h3>
                          <p className="text-gray-600 mt-1">{bolao.descricao}</p>
                        </div>
                        <Badge
                          className={
                            bolao.status === "ativo"
                              ? "bg-green-100 text-green-800"
                              : bolao.status === "em_breve"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-gray-100 text-gray-800"
                          }
                        >
                          {bolao.status.replace("_", " ")}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <DollarSign className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                          <p className="text-sm text-gray-600">Valor da Aposta</p>
                          <p className="font-semibold text-blue-600">R$ {parseFloat(String(bolao.valor_aposta)).toFixed(2)}</p>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <Trophy className="h-5 w-5 text-green-600 mx-auto mb-1" />
                          <p className="text-sm text-gray-600">Prêmio Total</p>
                          <p className="font-semibold text-green-600">R$ {parseFloat(String(bolao.premio_total)).toFixed(2)}</p>
                        </div>
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <Users className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                          <p className="text-sm text-gray-600">Max. Participantes</p>
                          <p className="font-semibold text-purple-600">{bolao.max_participantes}</p>
                        </div>
                        <div className="text-center p-3 bg-orange-50 rounded-lg">
                          <Clock className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                          <p className="text-sm text-gray-600">Período</p>
                          <p className="font-semibold text-orange-600 text-xs">
                            {new Date(bolao.data_inicio).toLocaleDateString("pt-BR")} -{" "}
                            {new Date(bolao.data_fim).toLocaleDateString("pt-BR")}
                          </p>
                        </div>
                      </div>

                      {/* Campeonatos Selecionados */}
                      {campeonatos.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                            <Trophy className="h-4 w-4 mr-2 text-yellow-600" />
                            Campeonatos ({campeonatos.length})
                          </h4>
                          <div className="flex flex-wrap gap-3">
                            {campeonatos && campeonatos.map((campeonato: any, index: number) => (
                              <div key={index} className="flex items-center space-x-2 bg-yellow-50 border border-yellow-200 rounded-lg px-3 py-2">
                                <CompetitionEmblem
                                  src={campeonato?.logo_url}
                                  alt={campeonato?.nome || 'Campeonato'}
                                  competitionCode={campeonato?.codigo}
                                  size="sm"
                                />
                                <span className="text-sm font-medium text-yellow-800">
                                  {campeonato?.nome || 'Campeonato'}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Partidas Selecionadas */}
                      <div className="mb-4">
                        <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                          <Target className="h-4 w-4 mr-2 text-green-600" />
                          Partidas Selecionadas (3)
                        </h4>
                      </div>

                      {/* Botões de Ação */}
                      <div className="flex justify-end space-x-2 border-t pt-4 mt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          onClick={() => handleEdit(bolao.id)}
                          disabled={actionLoading === bolao.id}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Editar
                        </Button>

                        {bolao.status === 'ativo' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                            onClick={() => handleToggleStatus(bolao.id, bolao.status)}
                            disabled={actionLoading === bolao.id}
                          >
                            {actionLoading === bolao.id ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <PowerOff className="h-4 w-4 mr-1" />
                            )}
                            Desativar
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50"
                            onClick={() => handleToggleStatus(bolao.id, bolao.status)}
                            disabled={actionLoading === bolao.id}
                          >
                            {actionLoading === bolao.id ? (
                              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            ) : (
                              <Power className="h-4 w-4 mr-1" />
                            )}
                            Ativar
                          </Button>
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDelete(bolao.id, bolao.nome)}
                          disabled={actionLoading === bolao.id}
                        >
                          {actionLoading === bolao.id ? (
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4 mr-1" />
                          )}
                          Deletar
                        </Button>
                      </div>

                      <div className="text-xs text-gray-500 border-t pt-3 mt-3">
                        <strong>Criado por:</strong> {bolao.criado_por_nome}
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      <CriarBolaoModal
        open={showCriarBolaoModal}
        onOpenChange={setShowCriarBolaoModal}
        onSuccess={fetchData}
      />

      <CriarBolaoModal
        open={showEditBolaoModal}
        onOpenChange={setShowEditBolaoModal}
        onSuccess={fetchData}
        editData={editingBolao}
        isEditing={true}
      />
    </div>
  )
}
