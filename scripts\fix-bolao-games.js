/**
 * Script para associar jogos reais ao bolão ativo
 * Corrige o problema dos times sem nome
 */

import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function fixBolaoGames() {
  try {
    console.log('🔧 Corrigindo jogos do bolão ativo...')
    
    await initializeDatabase()
    
    // 1. Buscar bolão ativo
    const boloesAtivos = await executeQuery(`
      SELECT id, nome FROM boloes 
      WHERE status = 'ativo'
      ORDER BY id DESC
      LIMIT 1
    `)
    
    if (boloesAtivos.length === 0) {
      console.log('❌ Nenhum bolão ativo encontrado!')
      return
    }
    
    const bolao = boloesAtivos[0]
    console.log(`🏆 Bolão ativo: ${bolao.nome} (ID: ${bolao.id})`)
    
    // 2. Limpar associações existentes
    await executeQuery('DELETE FROM bolao_jogos WHERE bolao_id = ?', [bolao.id])
    console.log('🧹 Associações antigas removidas')
    
    // 3. Buscar jogos com times e campeonatos válidos
    const jogosValidos = await executeQuery(`
      SELECT 
        j.id,
        j.data_jogo,
        j.status,
        tc.nome as time_casa_nome,
        tf.nome as time_fora_nome,
        c.nome as campeonato_nome,
        c.codigo as campeonato_codigo
      FROM jogos j
      INNER JOIN times tc ON j.time_casa_id = tc.id
      INNER JOIN times tf ON j.time_fora_id = tf.id
      INNER JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE j.status IN ('agendado', 'ao_vivo')
      AND tc.nome IS NOT NULL 
      AND tf.nome IS NOT NULL
      AND tc.nome != ''
      AND tf.nome != ''
      AND j.data_jogo >= NOW()
      ORDER BY j.data_jogo ASC
      LIMIT 15
    `)
    
    console.log(`📊 Jogos válidos encontrados: ${jogosValidos.length}`)
    
    if (jogosValidos.length === 0) {
      console.log('❌ Nenhum jogo válido encontrado!')
      
      // Criar alguns jogos de teste se necessário
      console.log('🎮 Criando jogos de teste...')
      await createTestGames()
      
      // Buscar novamente
      const novosJogos = await executeQuery(`
        SELECT 
          j.id,
          j.data_jogo,
          j.status,
          tc.nome as time_casa_nome,
          tf.nome as time_fora_nome,
          c.nome as campeonato_nome,
          c.codigo as campeonato_codigo
        FROM jogos j
        INNER JOIN times tc ON j.time_casa_id = tc.id
        INNER JOIN times tf ON j.time_fora_id = tf.id
        INNER JOIN campeonatos c ON j.campeonato_id = c.id
        WHERE j.status = 'agendado'
        AND tc.nome IS NOT NULL 
        AND tf.nome IS NOT NULL
        ORDER BY j.data_jogo ASC
        LIMIT 11
      `)
      
      console.log(`📊 Novos jogos criados: ${novosJogos.length}`)
      
      // Usar os novos jogos
      for (const jogo of novosJogos) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao.id, jogo.id])
        
        console.log(`✅ Jogo associado: ${jogo.time_casa_nome} vs ${jogo.time_fora_nome}`)
      }
      
    } else {
      // 4. Associar os melhores jogos ao bolão (máximo 11)
      const jogosParaAssociar = jogosValidos.slice(0, 11)
      
      console.log('\n📋 Associando jogos ao bolão:')
      for (const jogo of jogosParaAssociar) {
        await executeQuery(`
          INSERT INTO bolao_jogos (bolao_id, jogo_id)
          VALUES (?, ?)
        `, [bolao.id, jogo.id])
        
        console.log(`✅ ${jogo.time_casa_nome} vs ${jogo.time_fora_nome} (${jogo.campeonato_nome})`)
      }
    }
    
    // 5. Verificar resultado final
    const jogosAssociados = await executeQuery(`
      SELECT 
        j.id,
        tc.nome as time_casa_nome,
        tf.nome as time_fora_nome,
        c.nome as campeonato_nome
      FROM bolao_jogos bj
      JOIN jogos j ON bj.jogo_id = j.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE bj.bolao_id = ?
    `, [bolao.id])
    
    console.log(`\n🎯 Total de jogos associados ao bolão: ${jogosAssociados.length}`)
    
    console.log('\n✅ Correção concluída! Agora os times devem aparecer com nomes corretos.')
    
  } catch (error) {
    console.error('❌ Erro na correção:', error)
  }
}

async function createTestGames() {
  try {
    // Buscar times brasileiros conhecidos
    const timesBrasileiros = await executeQuery(`
      SELECT id, nome FROM times 
      WHERE nome LIKE '%Flamengo%' 
         OR nome LIKE '%Palmeiras%' 
         OR nome LIKE '%Corinthians%'
         OR nome LIKE '%São Paulo%'
         OR nome LIKE '%Santos%'
         OR nome LIKE '%Botafogo%'
         OR nome LIKE '%Vasco%'
         OR nome LIKE '%Fluminense%'
         OR nome LIKE '%Grêmio%'
         OR nome LIKE '%Internacional%'
      LIMIT 20
    `)
    
    // Buscar campeonato brasileiro
    const campeonato = await executeQuery(`
      SELECT id FROM campeonatos 
      WHERE codigo = 'BSA' OR nome LIKE '%Brasil%'
      LIMIT 1
    `)
    
    if (timesBrasileiros.length >= 4 && campeonato.length > 0) {
      console.log(`🇧🇷 Criando jogos com times brasileiros...`)
      
      const jogosParaCriar = [
        { casa: timesBrasileiros[0], fora: timesBrasileiros[1] },
        { casa: timesBrasileiros[2], fora: timesBrasileiros[3] },
        { casa: timesBrasileiros[4], fora: timesBrasileiros[5] },
        { casa: timesBrasileiros[6], fora: timesBrasileiros[7] },
        { casa: timesBrasileiros[8], fora: timesBrasileiros[9] },
        { casa: timesBrasileiros[10], fora: timesBrasileiros[11] },
        { casa: timesBrasileiros[12], fora: timesBrasileiros[13] },
        { casa: timesBrasileiros[14], fora: timesBrasileiros[15] },
        { casa: timesBrasileiros[16], fora: timesBrasileiros[17] },
        { casa: timesBrasileiros[18], fora: timesBrasileiros[19] },
        { casa: timesBrasileiros[0], fora: timesBrasileiros[19] }, // 11º jogo
      ]
      
      for (let i = 0; i < Math.min(jogosParaCriar.length, 11); i++) {
        const jogo = jogosParaCriar[i]
        if (!jogo.casa || !jogo.fora) continue
        
        const dataJogo = new Date()
        dataJogo.setDate(dataJogo.getDate() + i + 1) // Jogos nos próximos dias
        
        await executeQuery(`
          INSERT INTO jogos (
            campeonato_id, time_casa_id, time_fora_id, data_jogo, status
          ) VALUES (?, ?, ?, ?, 'agendado')
        `, [
          campeonato[0].id,
          jogo.casa.id,
          jogo.fora.id,
          dataJogo.toISOString().slice(0, 19).replace('T', ' ')
        ])
        
        console.log(`✅ Jogo criado: ${jogo.casa.nome} vs ${jogo.fora.nome}`)
      }
    }
    
  } catch (error) {
    console.error('❌ Erro ao criar jogos de teste:', error)
  }
}

// Executar a correção
fixBolaoGames()
  .then(() => {
    console.log('🎯 Correção concluída!')
    process.exit(0)
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error)
    process.exit(1)
  })
