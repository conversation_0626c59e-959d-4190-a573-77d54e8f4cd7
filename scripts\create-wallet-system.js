import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function createWalletSystem() {
  try {
    console.log('🏦 Iniciando criação do sistema de carteira...')
    
    await initializeDatabase()

    // 1. <PERSON><PERSON>r tabela de transações de saldo
    console.log('💰 Criando tabela de transações de saldo...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS saldo_transacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        tipo ENUM('deposito', 'compra_bilhete', 'premio', 'estorno', 'bonus') NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        saldo_anterior DECIMAL(10,2) NOT NULL,
        saldo_posterior DECIMAL(10,2) NOT NULL,
        descricao VARCHAR(255) NOT NULL,
        bilhete_id INT NULL,
        transaction_id VARCHAR(255) NULL,
        status ENUM('pendente', 'confirmado', 'cancelado') DEFAULT 'confirmado',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
        FOREIGN KEY (bilhete_id) REFERENCES bilhetes(id) ON DELETE SET NULL,
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_tipo (tipo),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_transaction_id (transaction_id)
      )
    `)

    // 2. Criar tabela de depósitos PIX para carteira
    console.log('💳 Criando tabela de depósitos PIX...')
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS depositos_pix (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        transaction_id VARCHAR(255) UNIQUE NOT NULL,
        pix_order_id VARCHAR(255) NULL,
        qr_code_value TEXT NULL,
        qrcode_image LONGTEXT NULL,
        status ENUM('pendente', 'pago', 'expirado', 'cancelado') DEFAULT 'pendente',
        expiration_datetime DATETIME NULL,
        client_name VARCHAR(255) NOT NULL,
        client_email VARCHAR(255) NOT NULL,
        client_document VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      )
    `)

    // 3. Verificar se a coluna saldo existe na tabela usuarios
    console.log('👤 Verificando coluna saldo na tabela usuarios...')
    try {
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD COLUMN saldo DECIMAL(10,2) DEFAULT 0.00 AFTER status
      `)
      console.log('✅ Coluna saldo adicionada à tabela usuarios')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✅ Coluna saldo já existe na tabela usuarios')
      } else {
        throw error
      }
    }

    // 4. Adicionar índice na coluna saldo
    try {
      await executeQuery(`
        ALTER TABLE usuarios 
        ADD INDEX idx_saldo (saldo)
      `)
      console.log('✅ Índice adicionado na coluna saldo')
    } catch (error) {
      if (error.message.includes('Duplicate key name')) {
        console.log('✅ Índice na coluna saldo já existe')
      } else {
        console.log('⚠️ Aviso ao criar índice saldo:', error.message)
      }
    }

    // 5. Modificar tabela bilhetes para incluir campo de desconto do saldo
    console.log('🎫 Modificando tabela bilhetes...')
    try {
      await executeQuery(`
        ALTER TABLE bilhetes 
        ADD COLUMN pago_com_saldo BOOLEAN DEFAULT FALSE AFTER status
      `)
      console.log('✅ Campo pago_com_saldo adicionado à tabela bilhetes')
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('✅ Campo pago_com_saldo já existe na tabela bilhetes')
      } else {
        throw error
      }
    }

    // 6. Criar função para atualizar saldo do usuário
    console.log('🔧 Criando função para gerenciar saldo...')
    
    console.log('✅ Sistema de carteira criado com sucesso!')
    console.log('')
    console.log('📋 Tabelas criadas:')
    console.log('   - saldo_transacoes: Histórico de todas as transações')
    console.log('   - depositos_pix: Depósitos via PIX para carteira')
    console.log('   - usuarios.saldo: Campo de saldo atualizado')
    console.log('   - bilhetes.pago_com_saldo: Flag para bilhetes pagos com saldo')
    console.log('')
    console.log('🎯 Próximos passos:')
    console.log('   1. Implementar APIs de depósito')
    console.log('   2. Modificar criação de bilhetes')
    console.log('   3. Criar interface de carteira')

  } catch (error) {
    console.error('❌ Erro ao criar sistema de carteira:', error)
    throw error
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  createWalletSystem()
    .then(() => {
      console.log('🎉 Sistema de carteira configurado!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Falha na configuração:', error)
      process.exit(1)
    })
}

export { createWalletSystem }
