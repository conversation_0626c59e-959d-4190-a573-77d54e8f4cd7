import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    console.log("⏰ Cron job iniciado: Verificação automática de pagamentos")

    // Verificar se é uma chamada autorizada (opcional, para segurança)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET || 'default-secret'
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      console.log("🔒 Chamada não autorizada para cron job")
      // Em desenvolvimento, permitir sem autenticação
      if (process.env.NODE_ENV === 'production') {
        return NextResponse.json({
          success: false,
          error: "Não autorizado"
        }, { status: 401 })
      }
    }

    // Chamar a API de verificação de todos os bilhetes pendentes
    const checkResponse = await fetch(`${request.nextUrl.origin}/api/pix/check-all-pending`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (checkResponse.ok) {
      const checkData = await checkResponse.json()
      console.log("✅ Cron job concluído:", checkData)

      return NextResponse.json({
        success: true,
        message: "Cron job executado com sucesso",
        cron_timestamp: new Date().toISOString(),
        check_result: checkData
      })
    } else {
      console.error("❌ Erro na verificação automática:", checkResponse.status)
      return NextResponse.json({
        success: false,
        error: "Erro na verificação automática",
        status: checkResponse.status
      }, { status: 500 })
    }

  } catch (error) {
    console.error("❌ Erro no cron job:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do cron job",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

// Também permitir POST para flexibilidade
export async function POST(request: NextRequest) {
  return GET(request)
}
