import { NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    console.log("📊 CONSULTANDO LOGS DE WEBHOOKS")
    console.log("=" .repeat(40))
    
    await initializeDatabase()
    
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const transaction_id = searchParams.get('transaction_id')
    const status = searchParams.get('status')
    
    // Construir query com filtros
    let whereClause = ''
    let queryParams = []
    
    if (transaction_id) {
      whereClause += ' WHERE transaction_id LIKE ?'
      queryParams.push(`%${transaction_id}%`)
    }
    
    if (status) {
      whereClause += whereClause ? ' AND status = ?' : ' WHERE status = ?'
      queryParams.push(status)
    }
    
    // Buscar logs
    const logs = await executeQuery(`
      SELECT 
        id,
        transaction_id,
        order_id,
        amount,
        status,
        end_to_end_id,
        webhook_data,
        processed_at
      FROM webhook_logs 
      ${whereClause}
      ORDER BY processed_at DESC 
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset])
    
    // Contar total
    const totalResult = await executeQuery(`
      SELECT COUNT(*) as total 
      FROM webhook_logs 
      ${whereClause}
    `, queryParams)
    
    const total = totalResult[0].total
    
    // Processar logs para melhor visualização
    const processedLogs = logs.map(log => {
      let webhookData = {}
      try {
        webhookData = JSON.parse(log.webhook_data || '{}')
      } catch (e) {
        webhookData = { raw: log.webhook_data }
      }
      
      return {
        id: log.id,
        transaction_id: log.transaction_id,
        order_id: log.order_id,
        amount: parseFloat(log.amount || 0),
        status: log.status,
        end_to_end_id: log.end_to_end_id,
        processed_at: log.processed_at,
        webhook_data: webhookData,
        source: webhookData.source || 'unknown',
        source_ip: webhookData.source_ip || 'unknown',
        user_agent: webhookData.user_agent || 'unknown'
      }
    })
    
    // Estatísticas
    const stats = await executeQuery(`
      SELECT 
        status,
        COUNT(*) as count,
        SUM(amount) as total_amount
      FROM webhook_logs 
      GROUP BY status
      ORDER BY count DESC
    `)
    
    console.log(`📊 Retornando ${logs.length} logs de ${total} total`)
    
    return NextResponse.json({
      success: true,
      message: "Logs de webhooks consultados com sucesso",
      logs: processedLogs,
      pagination: {
        total,
        limit,
        offset,
        has_more: (offset + limit) < total
      },
      filters: {
        transaction_id: transaction_id || null,
        status: status || null
      },
      statistics: stats.map(stat => ({
        status: stat.status,
        count: stat.count,
        total_amount: parseFloat(stat.total_amount || 0)
      })),
      summary: {
        total_logs: total,
        showing: logs.length,
        page: Math.floor(offset / limit) + 1,
        total_pages: Math.ceil(total / limit)
      }
    })
    
  } catch (error) {
    console.error("❌ Erro ao consultar logs de webhooks:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🧹 LIMPANDO LOGS DE WEBHOOKS")
    
    const body = await request.json()
    const { action, days_old } = body
    
    await initializeDatabase()
    
    if (action === 'cleanup') {
      const daysOld = parseInt(days_old || '30')
      
      const result = await executeQuery(`
        DELETE FROM webhook_logs 
        WHERE processed_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [daysOld])
      
      console.log(`🗑️ ${result.affectedRows} logs antigos removidos (mais de ${daysOld} dias)`)
      
      return NextResponse.json({
        success: true,
        message: `${result.affectedRows} logs antigos removidos`,
        days_old: daysOld,
        removed_count: result.affectedRows
      })
    }
    
    if (action === 'clear_all') {
      const result = await executeQuery('DELETE FROM webhook_logs')
      
      console.log(`🗑️ Todos os ${result.affectedRows} logs foram removidos`)
      
      return NextResponse.json({
        success: true,
        message: "Todos os logs foram removidos",
        removed_count: result.affectedRows
      })
    }
    
    return NextResponse.json({
      success: false,
      error: "Ação não reconhecida",
      available_actions: ['cleanup', 'clear_all']
    }, { status: 400 })
    
  } catch (error) {
    console.error("❌ Erro ao limpar logs:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
