import { executeQuerySingle, executeQuery } from './lib/database.js';

(async () => {
  try {
    console.log('📊 Verificando tabelas do banco...');
    
    const campeonatos = await executeQuerySingle('SELECT COUNT(*) as count FROM campeonatos');
    const times = await executeQuerySingle('SELECT COUNT(*) as count FROM times');
    const jogos = await executeQuerySingle('SELECT COUNT(*) as count FROM jogos');
    const usuarios = await executeQuerySingle('SELECT COUNT(*) as count FROM usuarios');
    const bilhetes = await executeQuerySingle('SELECT COUNT(*) as count FROM bilhetes');
    
    console.log('📊 Dados no banco:');
    console.log('🏆 Campeonatos:', campeonatos.count);
    console.log('⚽ Times:', times.count);
    console.log('🎮 Jogos:', jogos.count);
    console.log('👤 Usuários:', usuarios.count);
    console.log('🎫 Bilhetes:', bilhetes.count);
    
    // Verificar alguns campeonatos
    const sampleCampeonatos = await executeQuery('SELECT id, nome, pais FROM campeonatos LIMIT 5');
    console.log('\n🏆 Alguns campeonatos:');
    sampleCampeonatos.forEach(c => {
      console.log(`  - ${c.nome} (${c.pais})`);
    });
    
  } catch (error) {
    console.error('❌ Erro:', error.message);
  }
  process.exit(0);
})();
