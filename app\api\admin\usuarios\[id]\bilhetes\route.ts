import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = params.id

    if (!userId) {
      return NextResponse.json(
        { error: 'ID do usuário é obrigatório' },
        { status: 400 }
      )
    }

    // Buscar bilhetes do usuário
    const bilhetes = await executeQuery(`
      SELECT 
        b.id,
        b.codigo,
        b.usuario_nome,
        b.usuario_email,
        b.usuario_cpf,
        b.valor_total,
        b.quantidade_apostas,
        b.status,
        b.transaction_id,
        b.end_to_end_id,
        b.data_expiracao,
        b.created_at,
        b.updated_at
      FROM bilhetes b
      WHERE b.usuario_id = ?
      ORDER BY b.created_at DESC
    `, [userId])

    // Para cada bilhete, buscar as apostas relacionadas
    const bilhetesComApostas = await Promise.all(
      bilhetes.map(async (bilhete: any) => {
        // Buscar apostas do bilhete na tabela bilhete_apostas
        const apostas = await executeQuery(`
          SELECT 
            ba.id,
            ba.resultado,
            ba.created_at,
            j.id as jogo_id,
            j.data_jogo,
            j.status as jogo_status,
            j.resultado_casa,
            j.resultado_fora,
            tc.nome as time_casa_nome,
            tc.nome_curto as time_casa_curto,
            tc.logo_url as time_casa_logo,
            tf.nome as time_fora_nome,
            tf.nome_curto as time_fora_curto,
            tf.logo_url as time_fora_logo,
            c.nome as campeonato_nome,
            c.logo_url as campeonato_logo
          FROM bilhete_apostas ba
          LEFT JOIN jogos j ON ba.match_id = j.id
          LEFT JOIN times tc ON j.time_casa_id = tc.id
          LEFT JOIN times tf ON j.time_fora_id = tf.id
          LEFT JOIN campeonatos c ON j.campeonato_id = c.id
          WHERE ba.bilhete_id = ?
          ORDER BY j.data_jogo ASC
        `, [bilhete.id])

        // Se não encontrou apostas na tabela bilhete_apostas, 
        // tentar buscar na tabela apostas (sistema antigo)
        let apostasAlternativas = []
        if (apostas.length === 0) {
          apostasAlternativas = await executeQuery(`
            SELECT 
              ad.id,
              ad.resultado_apostado as resultado,
              ad.acertou,
              ad.data_criacao as created_at,
              j.id as jogo_id,
              j.data_jogo,
              j.status as jogo_status,
              j.resultado_casa,
              j.resultado_fora,
              tc.nome as time_casa_nome,
              tc.nome_curto as time_casa_curto,
              tc.logo_url as time_casa_logo,
              tf.nome as time_fora_nome,
              tf.nome_curto as time_fora_curto,
              tf.logo_url as time_fora_logo,
              c.nome as campeonato_nome,
              c.logo_url as campeonato_logo,
              a.codigo_bilhete
            FROM apostas a
            JOIN aposta_detalhes ad ON a.id = ad.aposta_id
            LEFT JOIN jogos j ON ad.jogo_id = j.id
            LEFT JOIN times tc ON j.time_casa_id = tc.id
            LEFT JOIN times tf ON j.time_fora_id = tf.id
            LEFT JOIN campeonatos c ON j.campeonato_id = c.id
            WHERE a.usuario_id = ? AND a.codigo_bilhete = ?
            ORDER BY j.data_jogo ASC
          `, [userId, bilhete.codigo])
        }

        return {
          ...bilhete,
          apostas: apostas.length > 0 ? apostas : apostasAlternativas,
          total_apostas: apostas.length > 0 ? apostas.length : apostasAlternativas.length
        }
      })
    )

    // Calcular estatísticas
    const stats = {
      total_bilhetes: bilhetes.length,
      bilhetes_pendentes: bilhetes.filter((b: any) => b.status === 'pendente').length,
      bilhetes_pagos: bilhetes.filter((b: any) => b.status === 'pago').length,
      bilhetes_cancelados: bilhetes.filter((b: any) => b.status === 'cancelado').length,
      valor_total: bilhetes.reduce((sum: number, b: any) => sum + parseFloat(b.valor_total || 0), 0),
      valor_pago: bilhetes
        .filter((b: any) => b.status === 'pago')
        .reduce((sum: number, b: any) => sum + parseFloat(b.valor_total || 0), 0)
    }

    return NextResponse.json({
      success: true,
      bilhetes: bilhetesComApostas,
      stats
    })

  } catch (error) {
    console.error('Erro ao buscar bilhetes do usuário:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
