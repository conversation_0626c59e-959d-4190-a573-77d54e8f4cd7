import { NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

export async function POST(request: NextRequest) {
  try {
    console.log('🔔 Webhook PIX local recebido')
    
    const body = await request.json()
    console.log('📦 Dados recebidos:', body)

    await initializeDatabase()

    const { transaction_id, order_id, status, amount, end_to_end_id } = body

    // Identificar o bilhete
    let bilhete = null
    
    if (transaction_id) {
      const bilhetes = await executeQuery(`
        SELECT * FROM bilhetes WHERE transaction_id = ? LIMIT 1
      `, [transaction_id])
      if (bilhetes.length > 0) bilhete = bilhetes[0]
    }
    
    if (!bilhete && order_id) {
      const bilhetes = await executeQuery(`
        SELECT * FROM bilhetes WHERE pix_order_id = ? OR codigo = ? LIMIT 1
      `, [order_id, order_id])
      if (bilhetes.length > 0) bilhete = bilhetes[0]
    }

    if (!bilhete) {
      console.log('⚠️ Bilhete não encontrado para:', { transaction_id, order_id })
      return NextResponse.json({
        success: false,
        message: 'Bilhete não encontrado',
        transaction_id,
        order_id
      }, { status: 404 })
    }

    console.log(`📋 Bilhete encontrado: ${bilhete.codigo}`)

    // Atualizar status se for pagamento aprovado
    if (status === 'PAID' || status === 'APPROVED' || status === 'COMPLETED') {
      await executeQuery(`
        UPDATE bilhetes 
        SET status = 'pago', updated_at = NOW() 
        WHERE id = ?
      `, [bilhete.id])

      console.log(`✅ Bilhete ${bilhete.codigo} atualizado para PAGO`)

      return NextResponse.json({
        success: true,
        message: 'Pagamento processado com sucesso',
        bilhete_codigo: bilhete.codigo,
        status_anterior: bilhete.status,
        status_novo: 'pago',
        valor: parseFloat(bilhete.valor_total)
      })
    }

    // Para outros status, apenas registrar
    console.log(`📊 Status recebido para ${bilhete.codigo}: ${status}`)

    return NextResponse.json({
      success: true,
      message: 'Webhook processado',
      bilhete_codigo: bilhete.codigo,
      status_recebido: status,
      acao: 'Nenhuma ação necessária'
    })

  } catch (error) {
    console.error('❌ Erro no webhook local:', error)
    return NextResponse.json({
      error: 'Erro interno do servidor',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Webhook PIX Local - Sistema Bolão',
    status: 'ativo',
    endpoints: {
      webhook: 'POST /api/pix/webhook-local',
      teste: 'GET /api/pix/testar-pagamento'
    },
    instrucoes: [
      'Este webhook recebe notificações de pagamento PIX',
      'Configure na API PIX: http://localhost:3001/api/pix/webhook-local',
      'Para testar pagamentos: /api/pix/testar-pagamento?bilhete=CODIGO'
    ]
  })
}
