'use client'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Clock, Wallet, Plus, Minus, User, LogOut, ChevronDown, CreditCard, Receipt, UserCircle } from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface WalletHeaderProps {
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  onSearchClick?: () => void
  onLogout?: () => void
  onLoginClick?: () => void
  onRegisterClick?: () => void
  onDepositClick?: () => void
  onWithdrawClick?: () => void
  onPerfilClick?: () => void
  onPagamentosClick?: () => void
  onBilhetesClick?: () => void
}

export function WalletHeader({ usuario, onSaldoUpdate, onSearchClick, onLogout, onLoginClick, onRegisterClick, onDepositClick, onWithdrawClick, onPerfilClick, onPagamentosClick, onBilhetesClick }: WalletHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showDepositModal, setShowDepositModal] = useState(false)
  const [showWithdrawModal, setShowWithdrawModal] = useState(false)
  const [depositAmount, setDepositAmount] = useState('')
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Fechar menu quando clicar fora
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu])

  const formatDateTime = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getInitials = (nome: string) => {
    return nome
      .split(' ')
      .map(n => n.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  const handleDeposit = async () => {
    if (!depositAmount || parseFloat(depositAmount) <= 0) {
      toast.error('Digite um valor válido para depósito')
      return
    }

    setIsProcessing(true)
    try {
      // Aqui você pode chamar a API de depósito
      if (onDepositClick) {
        onDepositClick()
      }

      // Simular processamento
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast.success(`Depósito de ${formatCurrency(parseFloat(depositAmount))} solicitado com sucesso!`)
      setDepositAmount('')
      setShowDepositModal(false)

      // Atualizar saldo se callback fornecido
      if (onSaldoUpdate && usuario) {
        onSaldoUpdate(usuario.saldo + parseFloat(depositAmount))
      }
    } catch (error) {
      toast.error('Erro ao processar depósito')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleWithdraw = async () => {
    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      toast.error('Digite um valor válido para saque')
      return
    }

    if (usuario && parseFloat(withdrawAmount) > usuario.saldo) {
      toast.error('Saldo insuficiente')
      return
    }

    setIsProcessing(true)
    try {
      // Aqui você pode chamar a API de saque
      if (onWithdrawClick) {
        onWithdrawClick()
      }

      // Simular processamento
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast.success(`Saque de ${formatCurrency(parseFloat(withdrawAmount))} solicitado com sucesso!`)
      setWithdrawAmount('')
      setShowWithdrawModal(false)

      // Atualizar saldo se callback fornecido
      if (onSaldoUpdate && usuario) {
        onSaldoUpdate(usuario.saldo - parseFloat(withdrawAmount))
      }
    } catch (error) {
      toast.error('Erro ao processar saque')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <header className="wallet-header bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-slate-700/50 shadow-2xl sticky top-0 z-50 backdrop-blur-sm">
      <div className="w-full max-w-7xl mx-auto px-4">
        {/* Desktop Layout */}
        <div className="hidden lg:flex items-center justify-between py-3 gap-6">
          {/* Left Section - Data e Hora */}
          <div className="flex items-center space-x-4 bg-slate-800/50 rounded-xl px-4 py-3 border border-slate-700/30">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg shadow-lg">
              <Clock className="h-5 w-5 text-white" />
            </div>
            <div className="text-white">
              <div className="text-sm font-semibold tracking-wide">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-slate-400 font-medium">
                UTC-03:00 • Brasil
              </div>
            </div>
          </div>

          {/* Center Section - User Wallet */}
          <div className="flex items-center space-x-4">
            {usuario ? (
              <>
                {/* Wallet Card */}
                <div className="flex items-center space-x-4 bg-gradient-to-r from-slate-800/80 to-slate-700/80 rounded-2xl px-6 py-4 border border-slate-600/30 shadow-xl backdrop-blur-sm">

                    <DialogContent className="sm:max-w-md bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700">
                      <DialogHeader>
                        <DialogTitle className="text-white text-xl font-bold flex items-center gap-3">
                          <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg">
                            <Plus className="h-5 w-5 text-white" />
                          </div>
                          Fazer Depósito
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-6 pt-4">
                        <div className="space-y-3">
                          <Label htmlFor="deposit-amount" className="text-slate-300 font-medium">Valor do Depósito</Label>
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 font-semibold">R$</span>
                            <Input
                              id="deposit-amount"
                              type="number"
                              placeholder="0,00"
                              value={depositAmount}
                              onChange={(e) => setDepositAmount(e.target.value)}
                              min="0"
                              step="0.01"
                              className="pl-10 bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-emerald-500 focus:ring-emerald-500 h-12 text-lg font-semibold"
                            />
                          </div>
                        </div>
                        <div className="flex gap-3">
                          <Button
                            onClick={handleDeposit}
                            disabled={isProcessing}
                            className="flex-1 bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white border-0 shadow-lg h-12 font-semibold"
                          >
                            {isProcessing ? (
                              <div className="flex items-center gap-2">
                                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                Processando...
                              </div>
                            ) : (
                              'Confirmar Depósito'
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowDepositModal(false)}
                            disabled={isProcessing}
                            className="bg-slate-700 border-slate-600 text-slate-300 hover:bg-slate-600 h-12 px-6"
                          >
                            Cancelar
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* User Profile with Dropdown */}
                  <div className="relative" ref={userMenuRef}>
                    <button
                      onClick={() => setShowUserMenu(!showUserMenu)}
                      className="flex items-center space-x-4 hover:bg-slate-700/50 rounded-2xl p-2 transition-all duration-300 group"
                    >
                      <div className="relative">
                        <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white/10 group-hover:scale-105 transition-transform">
                          {getInitials(usuario.nome)}
                        </div>
                        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-slate-800 shadow-sm"></div>
                      </div>
                      <div className="text-white">
                        <div className="text-lg font-bold tracking-wide flex items-center gap-2">
                          {usuario.nome}
                          <ChevronDown className={`h-4 w-4 transition-transform duration-300 ${showUserMenu ? 'rotate-180' : ''}`} />
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-slate-400">ID:</span>
                          <span className="text-emerald-400 font-semibold">{usuario.id}</span>
                          <div className="w-1 h-1 bg-slate-500 rounded-full"></div>
                          <span className="text-green-400 font-bold">{formatCurrency(usuario.saldo)}</span>
                        </div>
                      </div>
                    </button>

                    {/* Dropdown Menu */}
                    {showUserMenu && (
                      <div className="absolute top-full right-0 mt-2 w-64 bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-700/50 backdrop-blur-sm z-50 overflow-hidden">
                        <div className="p-2">
                          {/* Sacar */}
                          <Dialog open={showWithdrawModal} onOpenChange={setShowWithdrawModal}>
                            <DialogTrigger asChild>
                              <button
                                onClick={() => setShowUserMenu(false)}
                                className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-slate-700/50 rounded-xl transition-all duration-200 text-white group"
                              >
                                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg group-hover:scale-110 transition-transform">
                                  <Minus className="h-5 w-5 text-white" />
                                </div>
                                <div>
                                  <div className="font-semibold">Sacar</div>
                                  <div className="text-xs text-slate-400">Retirar dinheiro</div>
                                </div>
                              </button>
                            </DialogTrigger>
                          </Dialog>

                          {/* Depositar */}
                          <Dialog open={showDepositModal} onOpenChange={setShowDepositModal}>
                            <DialogTrigger asChild>
                              <button
                                onClick={() => setShowUserMenu(false)}
                                className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-slate-700/50 rounded-xl transition-all duration-200 text-white group"
                              >
                                <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg group-hover:scale-110 transition-transform">
                                  <Plus className="h-5 w-5 text-white" />
                                </div>
                                <div>
                                  <div className="font-semibold">Depositar</div>
                                  <div className="text-xs text-slate-400">Adicionar dinheiro</div>
                                </div>
                              </button>
                            </DialogTrigger>
                          </Dialog>

                          {/* Perfil */}
                          <button
                            onClick={() => {
                              setShowUserMenu(false)
                              onPerfilClick?.()
                            }}
                            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-slate-700/50 rounded-xl transition-all duration-200 text-white group"
                          >
                            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg group-hover:scale-110 transition-transform">
                              <UserCircle className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <div className="font-semibold">Perfil</div>
                              <div className="text-xs text-slate-400">Editar informações</div>
                            </div>
                          </button>

                          {/* Meus Pagamentos */}
                          <button
                            onClick={() => {
                              setShowUserMenu(false)
                              onPagamentosClick?.()
                            }}
                            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-slate-700/50 rounded-xl transition-all duration-200 text-white group"
                          >
                            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg group-hover:scale-110 transition-transform">
                              <CreditCard className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <div className="font-semibold">Meus Pagamentos</div>
                              <div className="text-xs text-slate-400">Histórico financeiro</div>
                            </div>
                          </button>

                          {/* Meus Bilhetes */}
                          <button
                            onClick={() => {
                              setShowUserMenu(false)
                              onBilhetesClick?.()
                            }}
                            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-slate-700/50 rounded-xl transition-all duration-200 text-white group"
                          >
                            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-lg group-hover:scale-110 transition-transform">
                              <Receipt className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <div className="font-semibold">Meus Bilhetes</div>
                              <div className="text-xs text-slate-400">Apostas realizadas</div>
                            </div>
                          </button>

                          {/* Divider */}
                          <div className="my-2 border-t border-slate-700/50"></div>

                          {/* Sair */}
                          <button
                            onClick={() => {
                              setShowUserMenu(false)
                              onLogout?.()
                            }}
                            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-red-500/10 rounded-xl transition-all duration-200 text-red-400 group"
                          >
                            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg group-hover:scale-110 transition-transform">
                              <LogOut className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <div className="font-semibold">Sair</div>
                              <div className="text-xs text-red-300/70">Fazer logout</div>
                            </div>
                          </button>
                        </div>
                      </div>
                    )}
                  </div>


                    <DialogContent className="sm:max-w-md bg-gradient-to-br from-slate-900 to-slate-800 border-slate-700">
                      <DialogHeader>
                        <DialogTitle className="text-white text-xl font-bold flex items-center gap-3">
                          <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg">
                            <Minus className="h-5 w-5 text-white" />
                          </div>
                          Fazer Saque
                        </DialogTitle>
                      </DialogHeader>
                      <div className="space-y-6 pt-4">
                        <div className="space-y-3">
                          <Label htmlFor="withdraw-amount" className="text-slate-300 font-medium">Valor do Saque</Label>
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 font-semibold">R$</span>
                            <Input
                              id="withdraw-amount"
                              type="number"
                              placeholder="0,00"
                              value={withdrawAmount}
                              onChange={(e) => setWithdrawAmount(e.target.value)}
                              min="0"
                              max={usuario.saldo}
                              step="0.01"
                              className="pl-10 bg-slate-800 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500 focus:ring-orange-500 h-12 text-lg font-semibold"
                            />
                          </div>
                          <div className="flex items-center justify-between bg-slate-800/50 rounded-lg px-3 py-2 border border-slate-700/50">
                            <span className="text-slate-400 text-sm">Saldo disponível:</span>
                            <span className="text-green-400 font-bold">{formatCurrency(usuario.saldo)}</span>
                          </div>
                        </div>
                        <div className="flex gap-3">
                          <Button
                            onClick={handleWithdraw}
                            disabled={isProcessing}
                            className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-0 shadow-lg h-12 font-semibold"
                          >
                            {isProcessing ? (
                              <div className="flex items-center gap-2">
                                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                Processando...
                              </div>
                            ) : (
                              'Confirmar Saque'
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowWithdrawModal(false)}
                            disabled={isProcessing}
                            className="bg-slate-700 border-slate-600 text-slate-300 hover:bg-slate-600 h-12 px-6"
                          >
                            Cancelar
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </>
            ) : (
              <>
                {/* Login Button */}
                <Button
                  onClick={onLoginClick}
                  variant="outline"
                  size="lg"
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-bold px-8 py-3 rounded-xl"
                >
                  Login
                </Button>

                {/* Register Button */}
                <Button
                  onClick={onRegisterClick}
                  variant="outline"
                  size="lg"
                  className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-bold px-8 py-3 rounded-xl"
                >
                  Registro
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden flex items-center justify-between p-3 gap-3">
          {/* Left Section - Data e Hora (Mobile) */}
          <div className="flex items-center space-x-3 bg-slate-800/50 rounded-lg px-3 py-2 border border-slate-700/30">
            <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg">
              <Clock className="h-4 w-4 text-white" />
            </div>
            <div className="text-white">
              <div className="text-xs font-semibold">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-slate-400">
                UTC-03:00
              </div>
            </div>
          </div>

          {/* Right Section - User Actions (Mobile) */}
          <div className="flex items-center space-x-2">
            {usuario ? (
              <>
                {/* Mobile Wallet Section */}
                <div className="flex items-center space-x-2 bg-gradient-to-r from-slate-800/80 to-slate-700/80 rounded-xl px-3 py-2 border border-slate-600/30">
                  {/* Deposit Button (Mobile) */}
                  <Dialog open={showDepositModal} onOpenChange={setShowDepositModal}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white border-0 shadow-md p-2 rounded-lg"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                  </Dialog>

                  {/* User Profile (Mobile) */}
                  <div className="flex items-center space-x-2">
                    <div className="relative">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-xs shadow-md">
                        {getInitials(usuario.nome)}
                      </div>
                      <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border border-slate-800"></div>
                    </div>
                    <div className="text-white">
                      <div className="text-xs font-semibold">{usuario.nome}</div>
                      <div className="text-xs text-emerald-400 font-medium">
                        {formatCurrency(usuario.saldo)}
                      </div>
                    </div>
                  </div>

                  {/* Withdraw Button (Mobile) */}
                  <Dialog open={showWithdrawModal} onOpenChange={setShowWithdrawModal}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white border-0 shadow-md p-2 rounded-lg"
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                  </Dialog>
                </div>

                {/* Logout Button (Mobile) */}
                <Button
                  onClick={onLogout}
                  variant="outline"
                  size="sm"
                  className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0 shadow-md p-2 rounded-lg"
                >
                  <LogOut className="h-4 w-4" />
                </Button>
              </>
            ) : (
              <>
                {/* Login Button (Mobile) */}
                <Button
                  onClick={onLoginClick}
                  variant="outline"
                  size="sm"
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black border-0 shadow-md font-bold text-xs px-4 py-2 rounded-lg"
                >
                  Login
                </Button>

                {/* Register Button (Mobile) */}
                <Button
                  onClick={onRegisterClick}
                  variant="outline"
                  size="sm"
                  className="bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 text-white border-0 shadow-md font-bold text-xs px-4 py-2 rounded-lg"
                >
                  Registro
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

// Componente de Status Badge para diferentes estados
export function StatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pago':
        return { color: 'bg-green-500', text: 'Pago' }
      case 'pendente':
        return { color: 'bg-yellow-500', text: 'Pendente' }
      case 'expirado':
        return { color: 'bg-red-500', text: 'Expirado' }
      case 'cancelado':
        return { color: 'bg-gray-500', text: 'Cancelado' }
      default:
        return { color: 'bg-gray-500', text: status }
    }
  }

  const config = getStatusConfig(status)

  return (
    <div className={`${config.color} text-white text-xs px-2 py-1 rounded`}>
      {config.text}
    </div>
  )
}
