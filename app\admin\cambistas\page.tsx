"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Users, Search, Download, UserPlus, DollarSign, TrendingUp, Loader2, Edit, Trash2, Ban, CheckCircle, Percent } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "sonner"

interface Cambista {
  id: number
  nome: string
  email: string
  telefone: string
  endereco: string
  cpf_cnpj: string
  status: "ativo" | "inativo" | "bloqueado"
  tipo: string
  data_cadastro: string
  porcentagem_comissao?: number
}

interface Stats {
  ativos: number
  totalVendas: number
  vendasHoje: number
  totalComissoes: number
}

export default function CambistasPage() {
  const [cambistas, setCambistas] = useState<Cambista[]>([])
  const [stats, setStats] = useState<Stats>({ ativos: 0, totalVendas: 0, vendasHoje: 0, totalComissoes: 0 })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isCreating, setIsCreating] = useState(false)
  const [formData, setFormData] = useState({
    nome: "",
    email: "",
    telefone: "",
    endereco: "",
    cpf_cnpj: "",
    senha: "",
  })

  // Estados para modais de ação
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showPercentDialog, setShowPercentDialog] = useState(false)
  const [selectedCambista, setSelectedCambista] = useState<Cambista | null>(null)
  const [editForm, setEditForm] = useState({
    nome: "",
    email: "",
    telefone: "",
    endereco: "",
    cpf_cnpj: "",
    status: "ativo",
    senha: "",
    porcentagem_comissao: 0
  })
  const [actionLoading, setActionLoading] = useState(false)

  const fetchData = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (searchTerm) params.append("search", searchTerm)

      const response = await fetch(`/api/admin/cambistas?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      setCambistas(data.cambistas || [])
      setStats(data.stats || { ativos: 0, totalVendas: 0, vendasHoje: 0, totalComissoes: 0 })
    } catch (error) {
      console.error("Erro ao carregar cambistas:", error)
      toast.error("Erro ao carregar cambistas")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchData()
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchTerm])

  const handleCreateCambista = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsCreating(true)

    try {
      const response = await fetch("/api/admin/cambistas", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        throw new Error("Erro ao criar cambista")
      }

      const result = await response.json()

      if (result.success) {
        toast.success("Cambista criado com sucesso!")
        setIsDialogOpen(false)
        setFormData({ nome: "", email: "", telefone: "", endereco: "", cpf_cnpj: "", senha: "" })
        fetchData()
      } else {
        throw new Error(result.error || "Erro desconhecido")
      }
    } catch (error) {
      console.error("Erro ao criar cambista:", error)
      toast.error("Erro ao criar cambista")
    } finally {
      setIsCreating(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const handleEdit = (cambista: Cambista) => {
    setSelectedCambista(cambista)
    setEditForm({
      nome: cambista.nome,
      email: cambista.email,
      telefone: cambista.telefone || "",
      endereco: cambista.endereco || "",
      cpf_cnpj: cambista.cpf_cnpj || "",
      status: cambista.status,
      senha: "",
      porcentagem_comissao: cambista.porcentagem_comissao || 0
    })
    setShowEditDialog(true)
  }

  const handleDelete = (cambista: Cambista) => {
    setSelectedCambista(cambista)
    setShowDeleteDialog(true)
  }

  const handleEditPercent = (cambista: Cambista) => {
    setSelectedCambista(cambista)
    setEditForm({
      ...editForm,
      porcentagem_comissao: cambista.porcentagem_comissao || 0
    })
    setShowPercentDialog(true)
  }

  const handleToggleStatus = async (cambista: Cambista) => {
    const newStatus = cambista.status === "ativo" ? "inativo" : "ativo"

    try {
      setActionLoading(true)

      const response = await fetch('/api/admin/cambistas', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: cambista.id,
          nome: cambista.nome,
          email: cambista.email,
          telefone: cambista.telefone,
          endereco: cambista.endereco,
          cpf_cnpj: cambista.cpf_cnpj,
          status: newStatus,
          porcentagem_comissao: cambista.porcentagem_comissao
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao alterar status')
      }

      toast.success(`Cambista ${newStatus === "ativo" ? "ativado" : "desativado"} com sucesso!`)
      fetchData()
    } catch (error: any) {
      console.error("Erro ao alterar status:", error)
      toast.error(error.message || "Erro ao alterar status do cambista")
    } finally {
      setActionLoading(false)
    }
  }

  const handleSaveEdit = async () => {
    if (!selectedCambista) return

    try {
      setActionLoading(true)

      const response = await fetch('/api/admin/cambistas', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedCambista.id,
          ...editForm
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao atualizar cambista')
      }

      toast.success("Cambista atualizado com sucesso!")
      setShowEditDialog(false)
      fetchData()
    } catch (error: any) {
      console.error("Erro ao atualizar cambista:", error)
      toast.error(error.message || "Erro ao atualizar cambista")
    } finally {
      setActionLoading(false)
    }
  }

  const handleSavePercent = async () => {
    if (!selectedCambista) return

    try {
      setActionLoading(true)

      const response = await fetch('/api/admin/cambistas', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedCambista.id,
          nome: selectedCambista.nome,
          email: selectedCambista.email,
          telefone: selectedCambista.telefone,
          endereco: selectedCambista.endereco,
          cpf_cnpj: selectedCambista.cpf_cnpj,
          status: selectedCambista.status,
          porcentagem_comissao: editForm.porcentagem_comissao
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao atualizar porcentagem')
      }

      toast.success("Porcentagem atualizada com sucesso!")
      setShowPercentDialog(false)
      fetchData()
    } catch (error: any) {
      console.error("Erro ao atualizar porcentagem:", error)
      toast.error(error.message || "Erro ao atualizar porcentagem")
    } finally {
      setActionLoading(false)
    }
  }

  const handleConfirmDelete = async () => {
    if (!selectedCambista) return

    try {
      setActionLoading(true)

      const response = await fetch(`/api/admin/cambistas?id=${selectedCambista.id}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao deletar cambista')
      }

      toast.success("Cambista deletado com sucesso!")
      setShowDeleteDialog(false)
      fetchData()
    } catch (error: any) {
      console.error("Erro ao deletar cambista:", error)
      toast.error(error.message || "Erro ao deletar cambista")
    } finally {
      setActionLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Cambistas</h1>
          <p className="text-gray-600 mt-2">Gerencie todos os cambistas do sistema</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="h-4 w-4 mr-2" />
              Novo Cambista
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Criar Novo Cambista</DialogTitle>
              <DialogDescription>Preencha os dados para criar um novo cambista no sistema.</DialogDescription>
            </DialogHeader>
            <form onSubmit={handleCreateCambista}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="nome" className="text-right">
                    Nome
                  </Label>
                  <Input
                    id="nome"
                    value={formData.nome}
                    onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="col-span-3"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="telefone" className="text-right">
                    Telefone
                  </Label>
                  <Input
                    id="telefone"
                    value={formData.telefone}
                    onChange={(e) => setFormData({ ...formData, telefone: e.target.value })}
                    className="col-span-3"
                    placeholder="(11) 99999-9999"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="endereco" className="text-right">
                    Endereço
                  </Label>
                  <Input
                    id="endereco"
                    value={formData.endereco}
                    onChange={(e) => setFormData({ ...formData, endereco: e.target.value })}
                    className="col-span-3"
                    placeholder="Rua, número - Cidade/UF"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="cpf_cnpj" className="text-right">
                    CPF/CNPJ
                  </Label>
                  <Input
                    id="cpf_cnpj"
                    value={formData.cpf_cnpj}
                    onChange={(e) => setFormData({ ...formData, cpf_cnpj: e.target.value })}
                    className="col-span-3"
                    placeholder="000.000.000-00"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="senha" className="text-right">
                    Senha
                  </Label>
                  <Input
                    id="senha"
                    type="password"
                    value={formData.senha}
                    onChange={(e) => setFormData({ ...formData, senha: e.target.value })}
                    className="col-span-3"
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Criando...
                    </>
                  ) : (
                    "Criar Cambista"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Cambistas Ativos</p>
                <p className="text-3xl font-bold text-gray-900">{stats.ativos}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Vendas</p>
                <p className="text-3xl font-bold text-green-600">{formatCurrency(stats.totalVendas)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Vendas Hoje</p>
                <p className="text-3xl font-bold text-blue-600">{formatCurrency(stats.vendasHoje)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Comissões</p>
                <p className="text-3xl font-bold text-purple-600">{formatCurrency(stats.totalComissoes)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Cambistas */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Cambistas</CardTitle>
          <CardDescription>Visualize e gerencie todos os cambistas cadastrados</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nome ou email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </Button>
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Cambista</TableHead>
                  <TableHead>Contato</TableHead>
                  <TableHead>Endereço</TableHead>
                  <TableHead>CPF/CNPJ</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Comissão</TableHead>
                  <TableHead>Data Cadastro</TableHead>
                  <TableHead>Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {cambistas.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                      Nenhum cambista encontrado
                    </TableCell>
                  </TableRow>
                ) : (
                  cambistas.map((cambista) => (
                    <TableRow key={cambista.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarImage src={`/placeholder.svg?height=32&width=32`} />
                            <AvatarFallback>
                              {cambista.nome
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{cambista.nome}</p>
                            <p className="text-sm text-gray-500">ID: {cambista.id}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="text-sm">{cambista.email}</p>
                          <p className="text-sm text-gray-500">{cambista.telefone || "N/A"}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{cambista.endereco || "N/A"}</p>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{cambista.cpf_cnpj || "N/A"}</p>
                      </TableCell>
                      <TableCell>
                        <Badge className={cambista.status === "ativo" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                          {cambista.status === "ativo" ? "Ativo" : cambista.status === "inativo" ? "Inativo" : "Bloqueado"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <span className="text-sm font-medium">{cambista.porcentagem_comissao || 0}%</span>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleEditPercent(cambista)}
                            disabled={actionLoading}
                            className="h-6 w-6 p-0"
                          >
                            <Percent className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{new Date(cambista.data_cadastro).toLocaleDateString("pt-BR")}</p>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEdit(cambista)}
                            disabled={actionLoading}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleToggleStatus(cambista)}
                            disabled={actionLoading}
                            className={cambista.status === "ativo" ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"}
                          >
                            {cambista.status === "ativo" ? <Ban className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDelete(cambista)}
                            disabled={actionLoading}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Edição */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Editar Cambista</DialogTitle>
            <DialogDescription>
              Altere as informações do cambista {selectedCambista?.nome}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-nome">Nome</Label>
              <Input
                id="edit-nome"
                value={editForm.nome}
                onChange={(e) => setEditForm({ ...editForm, nome: e.target.value })}
                placeholder="Nome completo"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-telefone">Telefone</Label>
              <Input
                id="edit-telefone"
                value={editForm.telefone}
                onChange={(e) => setEditForm({ ...editForm, telefone: e.target.value })}
                placeholder="(11) 99999-9999"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-endereco">Endereço</Label>
              <Input
                id="edit-endereco"
                value={editForm.endereco}
                onChange={(e) => setEditForm({ ...editForm, endereco: e.target.value })}
                placeholder="Rua, número - Cidade/UF"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-cpf">CPF/CNPJ</Label>
              <Input
                id="edit-cpf"
                value={editForm.cpf_cnpj}
                onChange={(e) => setEditForm({ ...editForm, cpf_cnpj: e.target.value })}
                placeholder="000.000.000-00"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-status">Status</Label>
              <Select value={editForm.status} onValueChange={(value) => setEditForm({ ...editForm, status: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ativo">Ativo</SelectItem>
                  <SelectItem value="inativo">Inativo</SelectItem>
                  <SelectItem value="bloqueado">Bloqueado</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-porcentagem">Porcentagem de Comissão (%)</Label>
              <Input
                id="edit-porcentagem"
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={editForm.porcentagem_comissao}
                onChange={(e) => setEditForm({ ...editForm, porcentagem_comissao: parseFloat(e.target.value) || 0 })}
                placeholder="0.0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-senha">Nova Senha (opcional)</Label>
              <Input
                id="edit-senha"
                type="password"
                value={editForm.senha}
                onChange={(e) => setEditForm({ ...editForm, senha: e.target.value })}
                placeholder="Deixe em branco para manter a atual"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSaveEdit} disabled={actionLoading}>
              {actionLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Porcentagem */}
      <Dialog open={showPercentDialog} onOpenChange={setShowPercentDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Ajustar Porcentagem de Comissão</DialogTitle>
            <DialogDescription>
              Defina a porcentagem de comissão para {selectedCambista?.nome}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="porcentagem">Porcentagem de Comissão (%)</Label>
              <Input
                id="porcentagem"
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={editForm.porcentagem_comissao}
                onChange={(e) => setEditForm({ ...editForm, porcentagem_comissao: parseFloat(e.target.value) || 0 })}
                placeholder="0.0"
              />
              <p className="text-sm text-gray-500">
                Porcentagem atual: {selectedCambista?.porcentagem_comissao || 0}%
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPercentDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSavePercent} disabled={actionLoading}>
              {actionLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Salvar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Confirmação de Exclusão */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir o cambista <strong>{selectedCambista?.nome}</strong>?
              Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete} disabled={actionLoading}>
              {actionLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
