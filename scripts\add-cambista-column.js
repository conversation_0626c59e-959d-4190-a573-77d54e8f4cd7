// Adicionar coluna cambista_id na tabela bilhetes
import mysql from 'mysql2/promise'

async function addCambistaColumn() {
  let connection
  
  try {
    console.log('🔗 Conectando ao banco de dados...')
    
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'sistema-bolao-top'
    })

    console.log('✅ Conectado ao banco!')

    // Verificar se a coluna já existe
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM information_schema.columns 
      WHERE table_schema = 'sistema-bolao-top' 
      AND table_name = 'bilhetes' 
      AND column_name = 'cambista_id'
    `)

    if (columns.length > 0) {
      console.log('✅ Coluna cambista_id já existe na tabela bilhetes')
    } else {
      console.log('➕ Adicionando coluna cambista_id na tabela bilhetes...')
      
      await connection.execute(`
        ALTER TABLE bilhetes 
        ADD COLUMN cambista_id INT NULL AFTER usuario_id,
        ADD INDEX idx_cambista_id (cambista_id),
        ADD FOREIGN KEY (cambista_id) REFERENCES usuarios(id) ON DELETE SET NULL
      `)
      
      console.log('✅ Coluna cambista_id adicionada com sucesso!')
    }

    // Verificar estrutura atual da tabela
    console.log('\n📋 Estrutura atual da tabela bilhetes:')
    const [structure] = await connection.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_KEY
      FROM information_schema.columns 
      WHERE table_schema = 'sistema-bolao-top' AND table_name = 'bilhetes'
      ORDER BY ORDINAL_POSITION
    `)

    structure.forEach((col, index) => {
      console.log(`  ${index + 1}. ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE} - Key: ${col.COLUMN_KEY}`)
    })

    // Verificar se há bilhetes existentes
    const [bilhetes] = await connection.execute(`
      SELECT COUNT(*) as total FROM bilhetes
    `)

    console.log(`\n📊 Total de bilhetes na tabela: ${bilhetes[0].total}`)

    if (bilhetes[0].total > 0) {
      console.log('\n⚠️ ATENÇÃO: Há bilhetes existentes sem cambista_id associado.')
      console.log('   Para associar bilhetes existentes a cambistas, você pode:')
      console.log('   1. Executar UPDATE manual para associar bilhetes específicos')
      console.log('   2. Deixar como NULL (bilhetes vendidos pelo sistema)')
      console.log('   3. Associar todos a um cambista padrão')
    }

    console.log('\n🎯 PRÓXIMOS PASSOS:')
    console.log('1. A coluna cambista_id foi adicionada à tabela bilhetes')
    console.log('2. Novos bilhetes criados por cambistas serão automaticamente associados')
    console.log('3. A dashboard de cambistas agora pode funcionar corretamente')

  } catch (error) {
    console.error('❌ Erro:', error)
  } finally {
    if (connection) {
      await connection.end()
      console.log('\n🔌 Conexão fechada')
    }
  }
}

addCambistaColumn()
