'use client'

import { useEffect, useRef } from 'react'

export function usePixAutoCheck() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastBilhetesRef = useRef<string>('')

  useEffect(() => {
    // Função para executar a verificação automática
    const executarVerificacaoPixAutomatica = async () => {
      try {
        console.log('🔄 Executando verificação automática de PIX...')

        // Verificar bilhetes do usuário atual
        const userId = localStorage.getItem('user_id')
        if (!userId) {
          console.log('⚠️ Usuário não logado, pulando verificação PIX')
          return
        }

        // Buscar todos os bilhetes do usuário
        const bilhetesResponse = await fetch(`/api/user/bilhetes?user_id=${userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        if (!bilhetesResponse.ok) {
          console.error('❌ Erro ao buscar bilhetes:', bilhetesResponse.status)
          return
        }

        const bilhetesData = await bilhetesResponse.json()
        const bilhetes = bilhetesData.bilhetes || []

        // Criar hash dos bilhetes para detectar mudanças
        const bilhetesHash = JSON.stringify(bilhetes.map((b: any) => ({ codigo: b.codigo, status: b.status })))

        // Se houve mudança nos bilhetes
        if (lastBilhetesRef.current && lastBilhetesRef.current !== bilhetesHash) {
          console.log('📊 Detectada mudança nos bilhetes!')

          // Verificar se algum bilhete mudou para "pago"
          const bilhetesPagos = bilhetes.filter((b: any) => b.status === 'pago')

          for (const bilhete of bilhetesPagos) {
            // Verificar se este bilhete não estava pago antes
            const lastBilhetes = JSON.parse(lastBilhetesRef.current || '[]')
            const bilheteAnterior = lastBilhetes.find((b: any) => b.codigo === bilhete.codigo)

            if (!bilheteAnterior || bilheteAnterior.status !== 'pago') {
              console.log(`🎉 Bilhete ${bilhete.codigo} foi PAGO!`)

              // Disparar evento customizado para mostrar modal de sucesso
              window.dispatchEvent(new CustomEvent('pixPaymentConfirmed', {
                detail: {
                  codigo: bilhete.codigo,
                  valor: bilhete.valor,
                  data: new Date().toLocaleString('pt-BR'),
                  transactionId: bilhete.transaction_id || bilhete.codigo
                }
              }))
            }
          }
        }

        // Atualizar referência
        lastBilhetesRef.current = bilhetesHash

      } catch (error) {
        console.error('❌ Erro na verificação automática PIX:', error)
      }
    }

    // Executar verificação imediatamente ao carregar
    executarVerificacaoPixAutomatica()

    // Configurar intervalo de 5 segundos para verificação rápida
    // Isso permite detecção quase instantânea de pagamentos
    intervalRef.current = setInterval(executarVerificacaoPixAutomatica, 5 * 1000)

    // Cleanup ao desmontar
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])

  return null
}
