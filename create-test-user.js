// Script para criar usuário de teste
import { initializeDatabase, executeQuery } from './lib/database.js'
import bcrypt from 'bcryptjs'

async function createTestUser() {
  try {
    console.log('🔧 Inicializando banco de dados...')
    await initializeDatabase()

    // Verificar se usuário já existe
    const existingUser = await executeQuery(
      "SELECT id FROM usuarios WHERE id = 585",
      []
    )

    if (existingUser && existingUser.length > 0) {
      console.log('✅ Usuário de teste já existe (ID: 585)')
      return
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash('123456', 10)

    // Criar usuário de teste
    const result = await executeQuery(`
      INSERT INTO usuarios (
        id, nome, email, telefone, cpf_cnpj, senha_hash, 
        tipo, status, saldo, data_cadastro
      ) VALUES (585, ?, ?, ?, ?, ?, 'usuario', 'ativo', 0.00, NOW())
    `, [
      'Guilherme Teste',
      '<EMAIL>',
      '11999999999',
      '12345678901',
      senhaHash
    ])

    console.log('✅ Usuário de teste criado com sucesso!')
    console.log('📋 Dados do usuário:')
    console.log('   ID: 585')
    console.log('   Nome: Guilherme Teste')
    console.log('   Email: <EMAIL>')
    console.log('   Senha: 123456')
    console.log('   Saldo: R$ 0,00')

  } catch (error) {
    console.error('❌ Erro ao criar usuário de teste:', error)
  }
}

createTestUser()
