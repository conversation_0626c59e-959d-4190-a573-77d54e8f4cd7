import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    console.log("🎫 Buscando bilhetes para user_id:", userId)

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    try {
      await initializeDatabase()

      // Buscar bilhetes do usuário com timeout
      const bilhetes = await Promise.race([
        executeQuery(`
          SELECT
            b.id,
            b.codigo,
            b.usuario_id,
            b.usuario_nome,
            b.usuario_email,
            b.valor_total,
            b.quantidade_apostas,
            b.status,
            b.transaction_id,
            b.created_at
          FROM bilhetes b
          WHERE b.usuario_id = ?
          ORDER BY b.created_at DESC
          LIMIT 50
        `, [userId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na consulta de bilhetes')), 5000)
        )
      ])

      console.log("📊 Bilhetes encontrados:", bilhetes?.length || 0)

      // Para cada bilhete, buscar as apostas relacionadas
      const bilhetesFormatados = await Promise.all((bilhetes || []).map(async (bilhete: any) => {
        // Buscar apostas do bilhete na tabela bilhete_apostas
        let apostas = []
        try {
          apostas = await executeQuery(`
            SELECT
              ba.resultado,
              j.data_jogo,
              tc.nome as time_casa_nome,
              tc.nome_curto as time_casa_curto,
              tf.nome as time_fora_nome,
              tf.nome_curto as time_fora_curto,
              c.nome as campeonato_nome
            FROM bilhete_apostas ba
            LEFT JOIN jogos j ON ba.match_id = j.id
            LEFT JOIN times tc ON j.time_casa_id = tc.id
            LEFT JOIN times tf ON j.time_fora_id = tf.id
            LEFT JOIN campeonatos c ON j.campeonato_id = c.id
            WHERE ba.bilhete_id = ?
            ORDER BY j.data_jogo ASC
          `, [bilhete.id])
        } catch (apostasError) {
          console.warn(`⚠️ Erro ao buscar apostas do bilhete ${bilhete.id}:`, apostasError.message)
          apostas = []
        }

        // Formatar apostas para o frontend
        const apostasFormatadas = apostas.map((aposta: any) => ({
          jogo: `${aposta.time_casa_nome || 'Time Casa'} x ${aposta.time_fora_nome || 'Time Fora'}`,
          resultado: aposta.resultado === 'casa' ? 'Casa' :
                    aposta.resultado === 'empate' ? 'Empate' : 'Fora'
        }))

        return {
          id: "BLT" + bilhete.id,
          codigo: bilhete.codigo,
          transaction_id: bilhete.transaction_id || bilhete.codigo,
          data: new Date(bilhete.created_at).toLocaleDateString('pt-BR'),
          hora: new Date(bilhete.created_at).toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
          }),
          apostas: apostasFormatadas,
          valor: parseFloat(bilhete.valor_total),
          status: bilhete.status,
          premio: 0,
          qr_code_pix: "",
          db_id: bilhete.id
        }
      }))

      console.log(`✅ ${bilhetesFormatados.length} bilhetes formatados para usuário ${userId}`)

      // Log das apostas encontradas para debug
      bilhetesFormatados.forEach((bilhete, index) => {
        console.log(`📋 Bilhete ${index + 1} (${bilhete.id}): ${bilhete.apostas.length} apostas`)
        if (bilhete.apostas.length > 0) {
          bilhete.apostas.forEach((aposta, apostaIndex) => {
            console.log(`   ${apostaIndex + 1}. ${aposta.jogo} - ${aposta.resultado}`)
          })
        }
      })

      return NextResponse.json({
        success: true,
        bilhetes: bilhetesFormatados,
        total: bilhetesFormatados.length,
        source: 'database'
      })

    } catch (dbError) {
      console.warn(`⚠️ Erro ao acessar banco, retornando dados básicos:`, dbError.message)

      // Retornar dados básicos sem erro
      return NextResponse.json({
        success: true,
        bilhetes: [],
        total: 0,
        source: 'fallback'
      })
    }

  } catch (error) {
    console.error("❌ Erro ao buscar bilhetes:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
