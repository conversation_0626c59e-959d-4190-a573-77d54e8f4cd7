import { NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

const PIX_API_TOKEN = process.env.PIX_API_TOKEN || 'Gk0e0W5bc2Guozw97NXv8duZbZCDbsedwMuTQNqqn5ZMT549Z0qDDZBF5ZbG9sHhXw9egbly3eiFJ4PKfs6ur+zzXs30gSDV6CYl8sQmSPJaISqpqf/FtJ2k30O3kO9ZsNwasalD0PGpD2wlAbb4ynO5S07P1kgWZRtw4w=='

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Verificando todos os bilhetes pendentes...')

    await initializeDatabase()

    // Buscar bilhetes pendentes das últimas 24 horas
    const bilhetesPendentes = await executeQuery(`
      SELECT id, codigo, transaction_id, pix_order_id, status, valor_total, created_at
      FROM bilhetes 
      WHERE status = 'pendente' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      ORDER BY created_at DESC
      LIMIT 20
    `)

    console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes`)

    const resultados = []
    let bilhetesAtualizados = 0

    for (const bilhete of bilhetesPendentes) {
      try {
        console.log(`🔍 Verificando bilhete: ${bilhete.codigo}`)

        const resultado = {
          codigo: bilhete.codigo,
          transaction_id: bilhete.transaction_id,
          status_original: bilhete.status,
          status_novo: bilhete.status,
          verificado: false,
          erro: null
        }

        // Tentar consultar por transaction_id
        if (bilhete.transaction_id) {
          try {
            const response = await fetch('https://api.meiodepagamento.com/api/V1/ConsultarTransacao', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                token: PIX_API_TOKEN,
                transaction_id: bilhete.transaction_id
              })
            })

            if (response.ok) {
              const data = await response.json()
              resultado.verificado = true
              resultado.api_status = data.status
              resultado.api_response = data

              console.log(`📊 Status API para ${bilhete.codigo}: ${data.status}`)

              // Se pagamento foi aprovado, atualizar
              if (data.status === 'PAID' || data.status === 'APPROVED' || data.status === 'COMPLETED') {
                await executeQuery(`
                  UPDATE bilhetes 
                  SET status = 'pago', updated_at = NOW() 
                  WHERE id = ?
                `, [bilhete.id])

                resultado.status_novo = 'pago'
                resultado.atualizado = true
                bilhetesAtualizados++

                console.log(`✅ Bilhete ${bilhete.codigo} atualizado para PAGO`)
              }
            } else {
              resultado.erro = `HTTP ${response.status}: ${await response.text()}`
            }
          } catch (apiError) {
            resultado.erro = apiError instanceof Error ? apiError.message : String(apiError)
          }
        } else {
          resultado.erro = 'Transaction ID não disponível'
        }

        resultados.push(resultado)

        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (error) {
        console.error(`❌ Erro ao verificar bilhete ${bilhete.codigo}:`, error)
        resultados.push({
          codigo: bilhete.codigo,
          erro: error instanceof Error ? error.message : String(error),
          verificado: false
        })
      }
    }

    console.log(`✅ Verificação concluída: ${bilhetesAtualizados} bilhetes atualizados`)

    return NextResponse.json({
      success: true,
      message: `Verificação manual concluída`,
      resumo: {
        bilhetes_verificados: bilhetesPendentes.length,
        bilhetes_atualizados: bilhetesAtualizados,
        bilhetes_ainda_pendentes: bilhetesPendentes.length - bilhetesAtualizados
      },
      resultados,
      instrucoes: {
        como_usar: [
          'GET /api/pix/verificar-todos',
          'Este endpoint verifica manualmente todos os bilhetes pendentes',
          'Atualiza automaticamente os que foram pagos'
        ],
        proximos_passos: bilhetesAtualizados > 0 ? [
          `✅ ${bilhetesAtualizados} pagamento(s) confirmado(s)!`,
          '🔄 Recarregue a página para ver as atualizações',
          '🎉 Os bilhetes pagos já estão atualizados'
        ] : [
          '⏳ Nenhum pagamento novo encontrado',
          '🔄 Tente novamente em alguns minutos',
          '💡 Certifique-se de que o pagamento foi realmente efetuado'
        ]
      }
    })

  } catch (error) {
    console.error('❌ Erro na verificação manual:', error)
    return NextResponse.json({
      error: 'Erro na verificação manual',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
