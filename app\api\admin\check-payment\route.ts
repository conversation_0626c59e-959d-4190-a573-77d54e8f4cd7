import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: Request) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { pixCode, transactionId, orderId } = body

    console.log("🔍 Verificando pagamento:", { pixCode, transactionId, orderId })

    // Buscar bilhete pelo código PIX ou transaction_id
    let bilhetes = []
    
    if (pixCode) {
      // Buscar pelo código PIX (pode estar no campo codigo ou transaction_id)
      bilhetes = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE codigo LIKE ? OR transaction_id LIKE ? OR pix_code LIKE ?
        ORDER BY data_criacao DESC
        LIMIT 5
      `, [`%${pixCode}%`, `%${pixCode}%`, `%${pixCode}%`])
    }

    if (bilhetes.length === 0 && transactionId) {
      bilhetes = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE transaction_id = ? OR codigo = ?
        ORDER BY data_criacao DESC
        LIMIT 5
      `, [transactionId, transactionId])
    }

    if (bilhetes.length === 0 && orderId) {
      bilhetes = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE transaction_id = ? OR codigo = ?
        ORDER BY data_criacao DESC
        LIMIT 5
      `, [orderId, orderId])
    }

    // Se não encontrou, buscar bilhetes recentes com valor R$ 1,00
    if (bilhetes.length === 0) {
      console.log("🔍 Buscando bilhetes recentes com valor R$ 1,00...")
      bilhetes = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE valor_total = 1.00 
        AND data_criacao >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY data_criacao DESC
        LIMIT 10
      `)
    }

    console.log(`📊 Encontrados ${bilhetes.length} bilhetes`)

    // Mostrar detalhes dos bilhetes encontrados
    for (const bilhete of bilhetes) {
      console.log(`📋 Bilhete ID: ${bilhete.id}`)
      console.log(`   Código: ${bilhete.codigo}`)
      console.log(`   Transaction ID: ${bilhete.transaction_id}`)
      console.log(`   Status: ${bilhete.status}`)
      console.log(`   Valor: R$ ${bilhete.valor_total}`)
      console.log(`   Data: ${bilhete.data_criacao}`)
      console.log(`   PIX Code: ${bilhete.pix_code || 'N/A'}`)
    }

    // Se encontrou bilhetes, tentar atualizar o status
    if (bilhetes.length > 0) {
      const bilhete = bilhetes[0] // Pegar o mais recente
      
      if (bilhete.status !== 'pago') {
        console.log(`💰 Atualizando bilhete ${bilhete.id} para status 'pago'`)
        
        await executeQuery(`
          UPDATE bilhetes 
          SET status = 'pago', updated_at = NOW() 
          WHERE id = ?
        `, [bilhete.id])

        // Buscar bilhete atualizado
        const bilheteAtualizado = await executeQuery(`
          SELECT * FROM bilhetes WHERE id = ?
        `, [bilhete.id])

        console.log(`✅ Bilhete atualizado com sucesso!`)

        return NextResponse.json({
          success: true,
          message: "Pagamento confirmado e bilhete atualizado",
          bilhete: bilheteAtualizado[0],
          action: "updated"
        })
      } else {
        console.log(`✅ Bilhete já está pago`)
        return NextResponse.json({
          success: true,
          message: "Bilhete já estava pago",
          bilhete: bilhete,
          action: "already_paid"
        })
      }
    }

    // Buscar todos os bilhetes recentes para debug
    const bilhetesRecentes = await executeQuery(`
      SELECT id, codigo, transaction_id, status, valor_total, data_criacao 
      FROM bilhetes 
      ORDER BY data_criacao DESC 
      LIMIT 20
    `)

    console.log(`📋 Últimos 20 bilhetes:`)
    bilhetesRecentes.forEach((b: any) => {
      console.log(`  ID: ${b.id}, Código: ${b.codigo}, Status: ${b.status}, Valor: R$ ${b.valor_total}`)
    })

    return NextResponse.json({
      success: false,
      message: "Bilhete não encontrado",
      searched: { pixCode, transactionId, orderId },
      bilhetesRecentes: bilhetesRecentes.slice(0, 5)
    })

  } catch (error) {
    console.error("❌ Erro ao verificar pagamento:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
