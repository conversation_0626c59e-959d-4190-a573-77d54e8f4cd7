// Biblioteca para impressão em impressora térmica 57mm - VERSÃO ATUALIZADA
class ThermalPrinter {
  constructor() {
    this.width = 30 // Caracteres por linha em impressora 57mm
    this.commands = {
      INIT: "\x1B\x40", // Inicializar impressora
      FEED: "\x0A", // Quebra de linha
      CUT: "\x1D\x56\x00", // Cortar papel
      ALIGN_CENTER: "\x1B\x61\x01", // Centralizar
      ALIGN_LEFT: "\x1B\x61\x00", // Alinhar à esquerda
      BOLD_ON: "\x1B\x45\x01", // Negrito ligado
      BOLD_OFF: "\x1B\x45\x00", // Negrito desligado
      DOUBLE_HEIGHT: "\x1B\x21\x10", // Altura dupla
      NORMAL_SIZE: "\x1B\x21\x00", // Tamanho normal
    }
  }

  // Centralizar texto
  centerText(text) {
    const padding = Math.max(0, Math.floor((this.width - text.length) / 2))
    return " ".repeat(padding) + text
  }

  // Criar linha separadora
  createSeparator(char = "-") {
    return char.repeat(this.width)
  }

  // Quebrar texto em linhas
  wrapText(text, maxWidth = this.width) {
    const words = text.split(" ")
    const lines = []
    let currentLine = ""

    for (const word of words) {
      if ((currentLine + word).length <= maxWidth) {
        currentLine += (currentLine ? " " : "") + word
      } else {
        if (currentLine) lines.push(currentLine)
        currentLine = word
      }
    }
    if (currentLine) lines.push(currentLine)

    return lines
  }

  // Gerar bilhete de aposta - VERSÃO ATUALIZADA SEM PRÊMIO POTENCIAL
  generateBilhete(dadosAposta) {
    let bilhete = ""

    // Inicializar impressora
    bilhete += this.commands.INIT

    // Cabeçalho
    bilhete += this.commands.ALIGN_CENTER
    bilhete += this.commands.BOLD_ON
    bilhete += this.commands.DOUBLE_HEIGHT
    bilhete += "BOLAO BRASIL\n"
    bilhete += this.commands.NORMAL_SIZE
    bilhete += this.commands.BOLD_OFF
    bilhete += this.createSeparator("=") + "\n"

    // Informações do bilhete
    bilhete += this.commands.ALIGN_LEFT
    bilhete += `Bilhete: ${dadosAposta.id}\n`
    bilhete += `Data: ${dadosAposta.data} ${dadosAposta.hora}\n`
    bilhete += this.createSeparator("-") + "\n"

    // Apostas
    bilhete += this.commands.BOLD_ON
    bilhete += "SUAS APOSTAS:\n"
    bilhete += this.commands.BOLD_OFF

    dadosAposta.apostas.forEach((aposta, index) => {
      // Quebrar nomes longos para impressora 57mm (30 chars)
      const jogoFormatado = aposta.jogo.length > 28 ?
        aposta.jogo.substring(0, 28) + '...' : aposta.jogo

      bilhete += `${index + 1}. ${jogoFormatado}\n`
      bilhete += `   Aposta: ${aposta.resultado}\n`
    })

    bilhete += this.createSeparator("-") + "\n"

    // Valor (SEM PRÊMIO POTENCIAL)
    bilhete += `Valor Apostado: R$ ${dadosAposta.valor.toFixed(2)}\n`
    bilhete += this.createSeparator("-") + "\n"

    // QR Code PIX (se disponível)
    if (dadosAposta.qr_code_pix) {
      bilhete += this.commands.ALIGN_CENTER
      bilhete += "QR CODE PIX:\n"
      bilhete += this.commands.ALIGN_LEFT
      bilhete += `${dadosAposta.qr_code_pix}\n`
      bilhete += this.createSeparator("-") + "\n"
    }

    // Transaction ID (se disponível)
    if (dadosAposta.transaction_id) {
      bilhete += `ID Transacao: ${dadosAposta.transaction_id}\n`
      bilhete += this.createSeparator("-") + "\n"
    }

    // Rodapé
    bilhete += this.commands.ALIGN_CENTER
    bilhete += "Boa sorte!\n"
    bilhete += this.createSeparator("=") + "\n"
    bilhete += "www.bolaobrasil.com\n"
    bilhete += "Suporte: (11) 99999-9999\n"

    // Alimentar papel e cortar
    bilhete += "\n\n\n"
    bilhete += this.commands.CUT

    return bilhete
  }

  // Simular impressão (em ambiente web) - VERSÃO ATUALIZADA
  async printBilhete(dadosAposta) {
    try {
      const bilheteText = this.generateBilhete(dadosAposta)

      console.log("📄 Imprimindo bilhete:")
      console.log(bilheteText)

      // Em um ambiente real, aqui seria feita a comunicação com a impressora
      // via USB, Serial, ou Bluetooth

      // Simular tempo de impressão
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Criar blob para download (simulação)
      const blob = new Blob([bilheteText], { type: "text/plain" })
      const url = URL.createObjectURL(blob)

      // Criar link temporário para download
      const a = document.createElement("a")
      a.href = url
      a.download = `bilhete_${dadosAposta.id}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      return { success: true, message: "Bilhete impresso com sucesso!" }
    } catch (error) {
      console.error("❌ Erro ao imprimir bilhete:", error)
      return { success: false, message: "Erro ao imprimir bilhete" }
    }
  }

  // Imprimir recibo de pagamento PIX
  async printReciboPagamento(dadosPagamento) {
    try {
      let recibo = ""

      recibo += this.commands.INIT
      recibo += this.commands.ALIGN_CENTER
      recibo += this.commands.BOLD_ON
      recibo += "COMPROVANTE PIX\n"
      recibo += this.commands.BOLD_OFF
      recibo += this.createSeparator("=") + "\n"

      recibo += this.commands.ALIGN_LEFT
      recibo += `Transacao: ${dadosPagamento.transaction_id}\n`
      recibo += `Valor: R$ ${dadosPagamento.valor.toFixed(2)}\n`
      recibo += `Data: ${new Date().toLocaleString("pt-BR")}\n`
      recibo += `Status: PAGO\n`

      if (dadosPagamento.qr_code_pix) {
        recibo += this.createSeparator("-") + "\n"
        recibo += "QR Code PIX:\n"
        recibo += `${dadosPagamento.qr_code_pix}\n`
      }

      recibo += this.createSeparator("-") + "\n"

      recibo += this.commands.ALIGN_CENTER
      recibo += "Pagamento confirmado!\n"
      recibo += this.createSeparator("=") + "\n"

      recibo += "\n\n"
      recibo += this.commands.CUT

      console.log("🧾 Imprimindo recibo:", recibo)

      // Simular tempo de impressão
      await new Promise((resolve) => setTimeout(resolve, 1500))

      return { success: true, message: "Recibo impresso com sucesso!" }
    } catch (error) {
      console.error("❌ Erro ao imprimir recibo:", error)
      return { success: false, message: "Erro ao imprimir recibo" }
    }
  }
}

// Instância global da impressora
const thermalPrinter = new ThermalPrinter()

// Exportar para uso em outros módulos
if (typeof module !== "undefined" && module.exports) {
  module.exports = { ThermalPrinter, thermalPrinter }
}
