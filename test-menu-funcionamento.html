<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Menu - Sistema <PERSON>ão</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .user-menu-dropdown {
            animation: slideDown 0.2s ease-out;
            transform-origin: top right;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-4">Teste de Funcionamento do Menu</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-lg font-semibold mb-4"><PERSON><PERSON> do Usuário (Simulação)</h2>
            
            <!-- Simulação do menu -->
            <div class="relative inline-block">
                <button id="menuButton" class="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    <div class="w-8 h-8 bg-blue-800 rounded-full flex items-center justify-center">
                        <span class="text-sm font-bold">A</span>
                    </div>
                    <span>ID: 1</span>
                </button>
                
                <!-- Menu dropdown -->
                <div id="menuDropdown" class="user-menu-dropdown absolute right-0 mt-2 w-44 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-[9999] hidden">
                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                        <span>🔍</span>
                        <span>Buscar</span>
                    </button>
                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2">
                        <span>⚙️</span>
                        <span>Configurações</span>
                    </button>
                    <hr class="my-1">
                    <button class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2">
                        <span>🚪</span>
                        <span>Sair</span>
                    </button>
                </div>
            </div>
            
            <div class="mt-6">
                <h3 class="font-semibold mb-2">Instruções de Teste:</h3>
                <ol class="list-decimal list-inside space-y-1 text-sm">
                    <li>Clique no botão azul "ID: 1" para abrir o menu</li>
                    <li>O menu deve aparecer abaixo do botão</li>
                    <li>Clique em qualquer item do menu - deve funcionar</li>
                    <li>Clique fora do menu - deve fechar</li>
                    <li>Se tudo funcionar, o problema foi corrigido!</li>
                </ol>
            </div>
            
            <div class="mt-4 p-3 bg-green-100 rounded">
                <p class="text-green-800 text-sm">
                    ✅ <strong>Correções aplicadas:</strong><br>
                    • Evento de clique fora corrigido<br>
                    • Z-index aumentado para 9999<br>
                    • Overflow-visible adicionado<br>
                    • Referências de DOM corrigidas
                </p>
            </div>
        </div>
    </div>

    <script>
        const menuButton = document.getElementById('menuButton');
        const menuDropdown = document.getElementById('menuDropdown');
        let isMenuOpen = false;

        // Toggle menu
        menuButton.addEventListener('click', (e) => {
            e.stopPropagation();
            isMenuOpen = !isMenuOpen;
            
            if (isMenuOpen) {
                menuDropdown.classList.remove('hidden');
            } else {
                menuDropdown.classList.add('hidden');
            }
        });

        // Fechar menu quando clicar fora
        document.addEventListener('click', (e) => {
            if (isMenuOpen && !menuDropdown.contains(e.target) && !menuButton.contains(e.target)) {
                isMenuOpen = false;
                menuDropdown.classList.add('hidden');
            }
        });

        // Prevenir fechamento quando clicar dentro do menu
        menuDropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Simular ações dos itens do menu
        const menuItems = menuDropdown.querySelectorAll('button');
        menuItems.forEach((item, index) => {
            item.addEventListener('click', () => {
                const text = item.textContent.trim();
                alert(`Clicou em: ${text}`);
                
                // Fechar menu após clique
                isMenuOpen = false;
                menuDropdown.classList.add('hidden');
            });
        });
    </script>
</body>
</html>
