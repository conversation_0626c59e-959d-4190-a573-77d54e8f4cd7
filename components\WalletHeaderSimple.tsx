'use client'

import { useState, useEffect, useRef } from 'react'
import { ConfigModal } from './ConfigModal'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Plus,
  User,
  Clock,
  DollarSign,
  LogOut,
  Settings,
  ChevronDown,
  CreditCard,
  Receipt,
  UserCircle,
  Minus
} from 'lucide-react'
import { WalletModal } from './WalletModal'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface WalletHeaderProps {
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  onSearchClick?: () => void
  onLogout?: () => void
  onLoginClick?: () => void
  onRegisterClick?: () => void
  onPerfilClick?: () => void
  onPagamentosClick?: () => void
  onBilhetesClick?: () => void
}

export function WalletHeader({ 
  usuario, 
  onSaldoUpdate, 
  onSearchClick, 
  onLogout, 
  onLoginClick, 
  onRegisterClick, 
  onPerfilClick, 
  onPagamentosClick, 
  onBilhetesClick 
}: WalletHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isWalletModalOpen, setIsWalletModalOpen] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showConfigModal, setShowConfigModal] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const menuRef = useRef<HTMLDivElement>(null)
  const mobileMenuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Fechar menu quando clicar fora
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu])

  const formatDateTime = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getInitials = (nome: string) => {
    return nome
      .split(' ')
      .map(n => n.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  return (
    <div className="bg-gray-900 border-b border-slate-700 shadow-lg sticky top-0 z-40">
      <div className="w-full max-w-none mx-auto">
        {/* Desktop Layout */}
        <div className="hidden lg:flex items-center justify-between p-4 gap-4">
          {/* Left Section - Data e Hora */}
          <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
            <Clock className="h-4 w-4 text-yellow-400" />
            <div className="text-white">
              <div className="text-sm font-medium">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-400">
                UTC-03:00
              </div>
            </div>
          </div>

          {/* Right Section - User Actions */}
          <div className="flex items-center space-x-3">
            {usuario ? (
              <div className="flex items-center space-x-3">
                {/* User Info */}
                <div className="text-right">
                  <div className="text-sm font-medium text-white text-truncate-mobile">
                    {usuario.nome}
                  </div>
                  <div className="text-xs text-gray-300">
                    ID: {usuario.id}
                  </div>
                </div>

                {/* User Menu */}
                <div className="relative overflow-visible" ref={menuRef}>
                  <Button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    variant="ghost"
                    className="p-1 hover:bg-slate-700 rounded-full transition-colors flex items-center space-x-1"
                  >
                    <div className="user-avatar w-8 h-8 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-xs">
                        {getInitials(usuario.nome)}
                      </span>
                    </div>
                    <ChevronDown className="h-3 w-3 text-gray-300" />
                  </Button>

                  {/* Dropdown Menu */}
                  {showUserMenu && (
                    <div className="user-menu-dropdown absolute right-0 mt-2 w-52 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-[9999]">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowUserMenu(false)
                          onPerfilClick?.()
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <UserCircle className="h-4 w-4" />
                        <span>Perfil</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowUserMenu(false)
                          setIsWalletModalOpen(true)
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <Plus className="h-4 w-4 text-green-600" />
                        <span>Depositar</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowUserMenu(false)
                          setIsWalletModalOpen(true)
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <Minus className="h-4 w-4 text-orange-600" />
                        <span>Sacar</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowUserMenu(false)
                          onPagamentosClick?.()
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <CreditCard className="h-4 w-4" />
                        <span>Meus Pagamentos</span>
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          setShowUserMenu(false)
                          onBilhetesClick?.()
                        }}
                        className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-sm"
                      >
                        <Receipt className="h-4 w-4" />
                        <span>Meus Bilhetes</span>
                      </button>
                      <hr className="my-1" />
                      <button
                        onClick={() => {
                          console.log('🚀 BOTÃO SAIR CLICADO!')
                          setShowUserMenu(false)
                          if (onLogout) {
                            onLogout()
                          } else {
                            localStorage.clear()
                            window.location.reload()
                          }
                        }}
                        className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2 text-sm transition-colors"
                      >
                        <LogOut className="h-4 w-4" />
                        <span>Sair</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Button
                  onClick={onLoginClick}
                  variant="outline"
                  size="sm"
                  className="text-white border-white hover:bg-white hover:text-slate-800"
                >
                  Faça login
                </Button>
                <Button
                  onClick={onRegisterClick}
                  size="sm"
                  className="bg-yellow-500 hover:bg-yellow-600 text-black"
                >
                  Registre-se
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden flex items-center justify-between p-3">
          {/* Left - Time */}
          <div className="flex items-center space-x-2 min-w-0 flex-shrink-0">
            <Clock className="h-3 w-3 text-yellow-400" />
            <div className="text-white">
              <div className="text-xs font-medium">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-400">UTC-03:00</div>
            </div>
          </div>

          {/* Right - User */}
          {usuario && (
            <div className="flex items-center space-x-2">
              <div className="text-right">
                <div className="text-xs font-medium text-white text-truncate-mobile">
                  {usuario.nome}
                </div>
                <div className="text-xs text-gray-300">ID: {usuario.id}</div>
              </div>
              <div className="relative overflow-visible" ref={mobileMenuRef}>
                <Button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  variant="ghost"
                  size="sm"
                  className="p-1 hover:bg-slate-700 rounded-full"
                >
                  <div className="user-avatar w-7 h-7 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-xs">
                      {getInitials(usuario.nome)}
                    </span>
                  </div>
                  <ChevronDown className="h-3 w-3 text-gray-300 ml-1" />
                </Button>

                {/* Mobile Dropdown */}
                {showUserMenu && (
                  <div className="user-menu-dropdown absolute right-0 mt-2 w-44 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                    <div className="px-3 py-2 border-b border-gray-100">
                      <div className="text-xs font-medium text-gray-900 truncate">
                        {usuario.nome}
                      </div>
                      <div className="text-xs text-gray-500">ID: {usuario.id}</div>
                    </div>
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        onPerfilClick?.()
                      }}
                      className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-xs"
                    >
                      <UserCircle className="h-3 w-3" />
                      <span>Perfil</span>
                    </button>
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        setIsWalletModalOpen(true)
                      }}
                      className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-xs"
                    >
                      <Plus className="h-3 w-3 text-green-600" />
                      <span>Depositar</span>
                    </button>
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        setIsWalletModalOpen(true)
                      }}
                      className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-xs"
                    >
                      <Minus className="h-3 w-3 text-orange-600" />
                      <span>Sacar</span>
                    </button>
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        onPagamentosClick?.()
                      }}
                      className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-xs"
                    >
                      <CreditCard className="h-3 w-3" />
                      <span>Meus Pagamentos</span>
                    </button>
                    <button
                      onClick={() => {
                        setShowUserMenu(false)
                        onBilhetesClick?.()
                      }}
                      className="w-full px-3 py-2 text-left text-gray-700 hover:bg-gray-100 flex items-center space-x-2 text-xs"
                    >
                      <Receipt className="h-3 w-3" />
                      <span>Meus Bilhetes</span>
                    </button>
                    <hr className="my-1" />
                    <button
                      onClick={() => {
                        console.log('🚀 BOTÃO SAIR CLICADO!')
                        setShowUserMenu(false)
                        if (onLogout) {
                          onLogout()
                        } else {
                          localStorage.clear()
                          window.location.reload()
                        }
                      }}
                      className="w-full px-3 py-2 text-left text-red-600 hover:bg-red-50 flex items-center space-x-2 text-xs transition-colors"
                    >
                      <LogOut className="h-3 w-3" />
                      <span>Sair</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {!usuario && (
            <div className="flex items-center space-x-1">
              <Button
                onClick={onLoginClick}
                variant="outline"
                size="sm"
                className="text-white border-white hover:bg-white hover:text-slate-800 text-xs px-2 py-1"
              >
                Login
              </Button>
              <Button
                onClick={onRegisterClick}
                size="sm"
                className="bg-yellow-500 hover:bg-yellow-600 text-black text-xs px-2 py-1"
              >
                Registro
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <WalletModal
        isOpen={isWalletModalOpen}
        onClose={() => setIsWalletModalOpen(false)}
        usuario={usuario}
        onSaldoUpdate={onSaldoUpdate}
      />

      <ConfigModal
        isOpen={showConfigModal}
        onClose={() => setShowConfigModal(false)}
        usuario={usuario}
      />
    </div>
  )
}

// Componente de Status Badge para diferentes estados
export function StatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pendente':
        return { color: 'bg-yellow-500', text: 'Pendente' }
      case 'aprovado':
        return { color: 'bg-green-500', text: 'Aprovado' }
      case 'rejeitado':
        return { color: 'bg-red-500', text: 'Rejeitado' }
      case 'processando':
        return { color: 'bg-blue-500', text: 'Processando' }
      default:
        return { color: 'bg-gray-500', text: status }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge className={`${config.color} text-white text-xs px-2 py-1`}>
      {config.text}
    </Badge>
  )
}
