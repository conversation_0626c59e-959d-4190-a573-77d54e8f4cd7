#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Iniciando servidor Next.js...');

const nextBin = path.join(__dirname, 'node_modules', '.bin', 'next');
const args = ['dev', '-p', '3000'];

const child = spawn('node', [nextBin, ...args], {
  stdio: 'inherit',
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('❌ Erro ao iniciar o servidor:', error);
});

child.on('exit', (code) => {
  console.log(`🔚 Servidor encerrado com código: ${code}`);
});

process.on('SIGINT', () => {
  console.log('\n🛑 Encerrando servidor...');
  child.kill('SIGINT');
});
