import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { user_id, affiliate_code, user_name, user_email } = body

    console.log("🔗 Processando indicação de afiliado:", { user_id, affiliate_code, user_name, user_email })

    // Validações básicas
    if (!user_id || !affiliate_code) {
      return NextResponse.json(
        { error: "user_id e affiliate_code são obrigatórios" },
        { status: 400 }
      )
    }

    // Buscar o afiliado pelo código
    const afiliado = await executeQuerySingle(
      "SELECT * FROM afiliados WHERE codigo_afiliado = ? AND status = 'ativo'",
      [affiliate_code]
    )

    if (!afiliado) {
      console.log("❌ Código de afiliado não encontrado ou inativo:", affiliate_code)
      return NextResponse.json(
        { error: "Código de afiliado inválido ou inativo" },
        { status: 404 }
      )
    }

    console.log("✅ Afiliado encontrado:", afiliado.nome)

    // Verificar se já existe uma indicação para este usuário
    const indicacaoExistente = await executeQuerySingle(
      "SELECT id FROM afiliado_indicacoes WHERE usuario_indicado_id = ?",
      [user_id]
    )

    if (indicacaoExistente) {
      console.log("⚠️ Usuário já foi indicado anteriormente")
      return NextResponse.json(
        { error: "Usuário já foi indicado por outro afiliado" },
        { status: 409 }
      )
    }

    // Calcular valor da comissão baseado no tipo
    let valorComissao = 0
    if (afiliado.tipo_comissao === 'cpa') {
      valorComissao = parseFloat(afiliado.cpa_valor || 0)
    } else {
      // Para percentual, definir um valor base (ex: R$ 25 por indicação)
      const valorBase = 25.00
      valorComissao = (valorBase * parseFloat(afiliado.percentual_comissao || 0)) / 100
    }

    // Registrar a indicação
    const indicacaoResult = await executeQuery(
      `INSERT INTO afiliado_indicacoes (afiliado_id, usuario_indicado_id, valor_comissao, status)
       VALUES (?, ?, ?, 'pendente')`,
      [afiliado.id, user_id, valorComissao]
    )

    // Atualizar estatísticas do afiliado
    await executeQuery(
      `UPDATE afiliados SET 
        total_indicacoes = total_indicacoes + 1,
        comissao_total = comissao_total + ?,
        data_atualizacao = NOW()
       WHERE id = ?`,
      [valorComissao, afiliado.id]
    )

    console.log("✅ Indicação registrada com sucesso:", {
      afiliado: afiliado.nome,
      usuario: user_name,
      comissao: valorComissao,
      tipo: afiliado.tipo_comissao
    })

    return NextResponse.json({
      success: true,
      message: "Indicação processada com sucesso",
      data: {
        afiliado_nome: afiliado.nome,
        valor_comissao: valorComissao,
        tipo_comissao: afiliado.tipo_comissao,
        indicacao_id: indicacaoResult.insertId
      }
    })

  } catch (error: any) {
    console.error("❌ Erro ao processar indicação de afiliado:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível processar a indicação"
      },
      { status: 500 }
    )
  }
}
