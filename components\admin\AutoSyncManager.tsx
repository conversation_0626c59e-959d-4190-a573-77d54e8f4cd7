'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { RefreshCw, Play, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'

interface SyncResult {
  success: boolean
  message: string
  data?: {
    synced?: number
    updated?: number
    errors?: number
    total_processed?: number
  }
}

interface SyncResponse {
  success: boolean
  message: string
  results: {
    campeonatos?: SyncResult
    partidas?: SyncResult
    times?: SyncResult
  }
  summary: any
  timestamp: string
}

export default function AutoSyncManager() {
  const [isLoading, setIsLoading] = useState(false)
  const [lastSync, setLastSync] = useState<SyncResponse | null>(null)
  const [syncType, setSyncType] = useState<'all' | 'campeonatos' | 'partidas' | 'times'>('all')

  const handleSync = async (type: 'all' | 'campeonatos' | 'partidas' | 'times' = 'all') => {
    setIsLoading(true)
    setSyncType(type)

    try {
      const response = await fetch(`/api/admin/auto-sync?type=${type}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data: SyncResponse = await response.json()
      setLastSync(data)

      if (data.success) {
        toast.success(data.message)
      } else {
        toast.error(data.message)
      }

    } catch (error) {
      console.error('Erro na sincronização:', error)
      toast.error('Erro ao executar sincronização')
    } finally {
      setIsLoading(false)
    }
  }

  const getSyncStatusIcon = (result?: SyncResult) => {
    if (!result) return <Clock className="h-4 w-4 text-gray-400" />
    if (result.success) return <CheckCircle className="h-4 w-4 text-green-500" />
    return <XCircle className="h-4 w-4 text-red-500" />
  }

  const getSyncStatusBadge = (result?: SyncResult) => {
    if (!result) return <Badge variant="secondary">Pendente</Badge>
    if (result.success) return <Badge variant="default" className="bg-green-500">Sucesso</Badge>
    return <Badge variant="destructive">Erro</Badge>
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Sincronização Automática
          </CardTitle>
          <CardDescription>
            Gerencie a sincronização automática de dados com a API Football-Data.org
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Botões de Sincronização */}
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => handleSync('all')}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading && syncType === 'all' ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Sincronizar Tudo
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleSync('campeonatos')}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading && syncType === 'campeonatos' ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Campeonatos
            </Button>
            
            <Button
              variant="outline"
              onClick={() => handleSync('partidas')}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading && syncType === 'partidas' ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Partidas
            </Button>
          </div>

          {/* Status da Última Sincronização */}
          {lastSync && (
            <>
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Última Sincronização</h3>
                  <div className="flex items-center gap-2">
                    {lastSync.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="text-sm text-muted-foreground">
                      {new Date(lastSync.timestamp).toLocaleString('pt-BR')}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Campeonatos */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center justify-between">
                        <span>Campeonatos</span>
                        {getSyncStatusIcon(lastSync.results.campeonatos)}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        {getSyncStatusBadge(lastSync.results.campeonatos)}
                        {lastSync.results.campeonatos?.data && (
                          <div className="text-xs text-muted-foreground space-y-1">
                            <div>Criados: {lastSync.results.campeonatos.data.synced || 0}</div>
                            <div>Atualizados: {lastSync.results.campeonatos.data.updated || 0}</div>
                            <div>Erros: {lastSync.results.campeonatos.data.errors || 0}</div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Partidas */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center justify-between">
                        <span>Partidas</span>
                        {getSyncStatusIcon(lastSync.results.partidas)}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        {getSyncStatusBadge(lastSync.results.partidas)}
                        {lastSync.results.partidas?.data && (
                          <div className="text-xs text-muted-foreground space-y-1">
                            <div>Criadas: {lastSync.results.partidas.data.synced || 0}</div>
                            <div>Atualizadas: {lastSync.results.partidas.data.updated || 0}</div>
                            <div>Erros: {lastSync.results.partidas.data.errors || 0}</div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Times */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center justify-between">
                        <span>Times</span>
                        {getSyncStatusIcon(lastSync.results.times)}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        {getSyncStatusBadge(lastSync.results.times)}
                        {lastSync.results.times?.data && (
                          <div className="text-xs text-muted-foreground space-y-1">
                            <div>Criados: {lastSync.results.times.data.synced || 0}</div>
                            <div>Atualizados: {lastSync.results.times.data.updated || 0}</div>
                            <div>Erros: {lastSync.results.times.data.errors || 0}</div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </>
          )}

          {/* Instruções */}
          <Separator />
          <div className="space-y-2">
            <h4 className="font-medium">Instruções para Cron Jobs</h4>
            <div className="text-sm text-muted-foreground space-y-1">
              <div><code>0 6 * * *</code> - Sincronização completa diária às 6h</div>
              <div><code>0 */2 * * *</code> - Sincronização de partidas a cada 2 horas</div>
              <div><code>POST /api/admin/auto-sync</code> - Endpoint para automação</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
