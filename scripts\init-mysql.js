#!/usr/bin/env node

/**
 * Script para inicializar o banco de dados MySQL do Sistema Bolão
 * Execute: node scripts/init-mysql.js
 */

import mysql from 'mysql2/promise'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { config } from 'dotenv'

// Load environment variables
config({ path: '.env.local' })

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Configuração do banco de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  charset: 'utf8mb4',
  multipleStatements: true
}

async function initializeMySQL() {
  let connection = null
  
  try {
    console.log('🔄 Conectando ao MySQL...')
    
    // Conectar ao MySQL sem especificar o banco
    connection = await mysql.createConnection(dbConfig)
    
    console.log('✅ Conectado ao MySQL com sucesso!')
    
    // Ler o script SQL
    const sqlPath = path.join(__dirname, 'mysql-setup.sql')
    
    if (!fs.existsSync(sqlPath)) {
      throw new Error(`Arquivo SQL não encontrado: ${sqlPath}`)
    }
    
    const sqlScript = fs.readFileSync(sqlPath, 'utf8')
    
    console.log('🔄 Executando script de criação do banco...')
    
    // Executar o script SQL
    await connection.query(sqlScript)
    
    console.log('✅ Banco de dados criado com sucesso!')
    
    // Verificar se as tabelas foram criadas
    await connection.query(`USE \`${process.env.DB_NAME || 'sistema-bolao-top'}\``)

    const [tables] = await connection.query('SHOW TABLES')
    
    console.log('📋 Tabelas criadas:')
    tables.forEach(table => {
      const tableName = Object.values(table)[0]
      console.log(`  - ${tableName}`)
    })
    
    // Verificar dados iniciais
    const [userCount] = await connection.query('SELECT COUNT(*) as count FROM usuarios')
    const [championshipCount] = await connection.query('SELECT COUNT(*) as count FROM campeonatos')
    const [teamCount] = await connection.query('SELECT COUNT(*) as count FROM times')
    
    console.log('\n📊 Dados iniciais inseridos:')
    console.log(`  - Usuários: ${userCount[0].count}`)
    console.log(`  - Campeonatos: ${championshipCount[0].count}`)
    console.log(`  - Times: ${teamCount[0].count}`)
    
    console.log('\n🎉 Inicialização do banco de dados concluída com sucesso!')
    console.log('\n📝 Próximos passos:')
    console.log('  1. Configure as variáveis de ambiente no arquivo .env.local')
    console.log('  2. Execute: npm run dev')
    console.log('  3. Acesse: http://localhost:3000')
    console.log('\n👤 Usuário admin padrão:')
    console.log('  Email: <EMAIL>')
    console.log('  Senha: admin123 (altere após o primeiro login)')
    
  } catch (error) {
    console.error('❌ Erro ao inicializar banco de dados:', error.message)
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('\n💡 Dica: Verifique as credenciais do MySQL no arquivo .env.local')
      console.error('   - DB_HOST, DB_PORT, DB_USER, DB_PASSWORD')
    } else if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Dica: Verifique se o MySQL está rodando')
      console.error('   - Windows: Verifique os serviços do Windows')
      console.error('   - Linux/Mac: sudo service mysql start')
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// Verificar se as variáveis de ambiente estão configuradas
function checkEnvironment() {
  const requiredVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
  const missingVars = requiredVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    console.warn('⚠️  Variáveis de ambiente não configuradas:')
    missingVars.forEach(varName => {
      console.warn(`   - ${varName}`)
    })
    console.warn('\n💡 Usando valores padrão. Configure o arquivo .env.local para personalizar.')
  }
}

// Executar o script
async function main() {
  console.log('🚀 Inicializando Sistema Bolão - MySQL Setup')
  console.log('=' .repeat(50))
  
  checkEnvironment()
  await initializeMySQL()
}

main().catch(console.error)
