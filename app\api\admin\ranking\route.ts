import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    console.log('🏆 Buscando ranking de usuários...')

    // Buscar ranking baseado em bilhetes e apostas reais
    const ranking = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        u.email,
        COUNT(DISTINCT b.id) as total_bilhetes,
        COUNT(ba.id) as apostas_totais,
        COUNT(CASE 
          WHEN j.status = 'finalizado' AND 
               ((ba.resultado = 'casa' AND j.resultado_casa > j.resultado_fora) OR
                (ba.resultado = 'empate' AND j.resultado_casa = j.resultado_fora) OR
                (ba.resultado = 'fora' AND j.resultado_fora > j.resultado_casa))
          THEN 1 
        END) as apostas_certas,
        ROUND(
          CASE 
            WHEN COUNT(ba.id) > 0 THEN
              (COUNT(CASE 
                WHEN j.status = 'finalizado' AND 
                     ((ba.resultado = 'casa' AND j.resultado_casa > j.resultado_fora) OR
                      (ba.resultado = 'empate' AND j.resultado_casa = j.resultado_fora) OR
                      (ba.resultado = 'fora' AND j.resultado_fora > j.resultado_casa))
                THEN 1 
              END) * 100.0 / COUNT(ba.id))
            ELSE 0
          END, 2
        ) as percentual_acerto
      FROM usuarios u
      LEFT JOIN bilhetes b ON u.id = b.usuario_id AND b.status IN ('pago', 'pendente')
      LEFT JOIN bilhete_apostas ba ON b.id = ba.bilhete_id
      LEFT JOIN jogos j ON ba.match_id = j.id
      WHERE u.tipo = 'usuario' AND u.status = 'ativo'
      GROUP BY u.id, u.nome, u.email
      HAVING COUNT(ba.id) > 0
      ORDER BY apostas_certas DESC, percentual_acerto DESC, total_bilhetes DESC
      LIMIT 50
    `)

    console.log(`📊 ${ranking?.length || 0} usuários encontrados no ranking`)

    // Formatar dados para o frontend
    const rankingFormatado = (ranking || []).map((user: any, index: number) => ({
      id: user.id,
      nome: user.nome,
      email: user.email,
      pontos_totais: user.apostas_certas, // 1 ponto por acerto
      apostas_certas: user.apostas_certas,
      apostas_totais: user.apostas_totais,
      percentual_acerto: parseFloat(user.percentual_acerto) || 0,
      posicao: index + 1,
      total_bilhetes: user.total_bilhetes
    }))

    console.log('✅ Ranking formatado com sucesso')

    return NextResponse.json({
      success: true,
      ranking: rankingFormatado,
      total: rankingFormatado.length,
      source: 'database'
    })

  } catch (error) {
    console.error('❌ Erro ao buscar ranking:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor',
      ranking: [],
      total: 0
    }, { status: 500 })
  }
}
