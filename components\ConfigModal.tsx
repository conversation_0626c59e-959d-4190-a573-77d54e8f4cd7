'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import {
  Settings,
  User,
  Bell,
  Shield,
  Palette,
  Volume2,
  X,
  Save,
  Eye,
  EyeOff
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface ConfigModalProps {
  isOpen: boolean
  onClose: () => void
  usuario?: Usuario | null
}

interface ConfiguracaoUsuario {
  notificacoes: {
    email: boolean
    push: boolean
    som: boolean
  }
  privacidade: {
    mostrarSaldo: boolean
    mostrarHistorico: boolean
  }
  aparencia: {
    tema: 'claro' | 'escuro' | 'auto'
    animacoes: boolean
  }
  som: {
    volume: number
    efeitos: boolean
  }
}

export function ConfigModal({ isOpen, onClose, usuario }: ConfigModalProps) {
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [senhaAtual, setSenhaAtual] = useState('')
  const [novaSenha, setNovaSenha] = useState('')
  const [confirmarSenha, setConfirmarSenha] = useState('')
  
  const [config, setConfig] = useState<ConfiguracaoUsuario>({
    notificacoes: {
      email: true,
      push: true,
      som: true
    },
    privacidade: {
      mostrarSaldo: true,
      mostrarHistorico: true
    },
    aparencia: {
      tema: 'auto',
      animacoes: true
    },
    som: {
      volume: 50,
      efeitos: true
    }
  })

  useEffect(() => {
    if (isOpen && usuario) {
      carregarConfiguracoes()
    }
  }, [isOpen, usuario])

  const carregarConfiguracoes = async () => {
    try {
      // Carregar configurações do localStorage ou API
      const configSalva = localStorage.getItem(`config_user_${usuario?.id}`)
      if (configSalva) {
        setConfig(JSON.parse(configSalva))
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error)
    }
  }

  const salvarConfiguracoes = async () => {
    try {
      setLoading(true)
      
      // Salvar no localStorage (em produção, salvar na API)
      localStorage.setItem(`config_user_${usuario?.id}`, JSON.stringify(config))
      
      toast.success('Configurações salvas com sucesso!')
      
      // Aplicar configurações imediatamente
      aplicarConfiguracoes()
      
    } catch (error) {
      console.error('Erro ao salvar configurações:', error)
      toast.error('Erro ao salvar configurações')
    } finally {
      setLoading(false)
    }
  }

  const aplicarConfiguracoes = () => {
    // Aplicar tema
    if (config.aparencia.tema === 'escuro') {
      document.documentElement.classList.add('dark')
    } else if (config.aparencia.tema === 'claro') {
      document.documentElement.classList.remove('dark')
    }
    
    // Aplicar volume
    const audioElements = document.querySelectorAll('audio')
    audioElements.forEach(audio => {
      audio.volume = config.som.volume / 100
    })
  }

  const alterarSenha = async () => {
    if (!senhaAtual || !novaSenha || !confirmarSenha) {
      toast.error('Preencha todos os campos de senha')
      return
    }

    if (novaSenha !== confirmarSenha) {
      toast.error('Nova senha e confirmação não coincidem')
      return
    }

    if (novaSenha.length < 6) {
      toast.error('Nova senha deve ter pelo menos 6 caracteres')
      return
    }

    try {
      setLoading(true)
      
      // Em produção, fazer requisição para API
      // Por enquanto, simular sucesso
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('Senha alterada com sucesso!')
      setSenhaAtual('')
      setNovaSenha('')
      setConfirmarSenha('')
      
    } catch (error) {
      console.error('Erro ao alterar senha:', error)
      toast.error('Erro ao alterar senha')
    } finally {
      setLoading(false)
    }
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4"
      onClick={handleBackdropClick}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-full max-w-xs sm:max-w-md md:max-w-2xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden mx-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b bg-slate-800 text-white">
          <div className="flex items-center space-x-3">
            <Settings className="h-6 w-6 text-blue-400" />
            <div>
              <h2 className="text-lg sm:text-xl font-bold">Configurações</h2>
              <p className="text-sm text-gray-300">{usuario?.nome}</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-white hover:bg-slate-700"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Conteúdo */}
        <div className="p-4 sm:p-6 max-h-[60vh] overflow-y-auto space-y-6">
          
          {/* Notificações */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base sm:text-lg">
                <Bell className="h-5 w-5 mr-2" />
                Notificações
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="email-notifications" className="text-sm">Notificações por email</Label>
                <Switch
                  id="email-notifications"
                  checked={config.notificacoes.email}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({
                      ...prev,
                      notificacoes: { ...prev.notificacoes, email: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="push-notifications" className="text-sm">Notificações push</Label>
                <Switch
                  id="push-notifications"
                  checked={config.notificacoes.push}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({
                      ...prev,
                      notificacoes: { ...prev.notificacoes, push: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="sound-notifications" className="text-sm">Sons de notificação</Label>
                <Switch
                  id="sound-notifications"
                  checked={config.notificacoes.som}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({
                      ...prev,
                      notificacoes: { ...prev.notificacoes, som: checked }
                    }))
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Privacidade */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base sm:text-lg">
                <Shield className="h-5 w-5 mr-2" />
                Privacidade
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="show-balance" className="text-sm">Mostrar saldo no header</Label>
                <Switch
                  id="show-balance"
                  checked={config.privacidade.mostrarSaldo}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({
                      ...prev,
                      privacidade: { ...prev.privacidade, mostrarSaldo: checked }
                    }))
                  }
                />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="show-history" className="text-sm">Mostrar histórico público</Label>
                <Switch
                  id="show-history"
                  checked={config.privacidade.mostrarHistorico}
                  onCheckedChange={(checked) => 
                    setConfig(prev => ({
                      ...prev,
                      privacidade: { ...prev.privacidade, mostrarHistorico: checked }
                    }))
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Alterar Senha */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base sm:text-lg">
                <Shield className="h-5 w-5 mr-2" />
                Alterar Senha
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="current-password" className="text-sm">Senha atual</Label>
                <div className="relative">
                  <Input
                    id="current-password"
                    type={showPassword ? "text" : "password"}
                    value={senhaAtual}
                    onChange={(e) => setSenhaAtual(e.target.value)}
                    className="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-password" className="text-sm">Nova senha</Label>
                <Input
                  id="new-password"
                  type={showPassword ? "text" : "password"}
                  value={novaSenha}
                  onChange={(e) => setNovaSenha(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confirm-password" className="text-sm">Confirmar nova senha</Label>
                <Input
                  id="confirm-password"
                  type={showPassword ? "text" : "password"}
                  value={confirmarSenha}
                  onChange={(e) => setConfirmarSenha(e.target.value)}
                />
              </div>
              <Button
                onClick={alterarSenha}
                disabled={loading || !senhaAtual || !novaSenha || !confirmarSenha}
                className="w-full"
              >
                {loading ? 'Alterando...' : 'Alterar Senha'}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-2 p-4 sm:p-6 border-t bg-gray-50">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={salvarConfiguracoes} disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Salvando...' : 'Salvar'}
          </Button>
        </div>
      </div>
    </div>
  )
}
