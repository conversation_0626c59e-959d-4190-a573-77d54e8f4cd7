import { type NextRequest, NextResponse } from "next/server"
import { executeQuerySingle } from "@/lib/db"
import bcrypt from 'bcryptjs'

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {


    const body = await request.json()
    const { email, senha } = body

    console.log("🔐 Tentativa de login:", { email })

    // Validações básicas
    if (!email || !senha) {
      return NextResponse.json(
        { error: "Email e senha são obrigatórios" },
        { status: 400 }
      )
    }

    // Buscar usuário no banco
    const user = await executeQuerySingle(
      "SELECT * FROM usuarios WHERE email = ? AND status = 'ativo'",
      [email]
    )

    if (!user) {
      return NextResponse.json(
        { error: "Email ou senha incorretos" },
        { status: 401 }
      )
    }

    // Verificar senha
    const senhaValida = await bcrypt.compare(senha, user.senha_hash)

    if (!senhaValida) {
      return NextResponse.json(
        { error: "Email ou senha incorretos" },
        { status: 401 }
      )
    }

    // Retornar dados do usuário (sem senha)
    const userData = {
      id: user.id,
      nome: user.nome,
      email: user.email,
      telefone: user.telefone,
      cpf: user.cpf_cnpj,
      tipo: user.tipo,
      afiliado_id: user.afiliado_id
    }

    console.log("✅ Login realizado com sucesso:", userData)

    return NextResponse.json({
      success: true,
      message: "Login realizado com sucesso",
      user: userData
    })

  } catch (error: any) {
    console.error("❌ Erro no login:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível realizar o login"
      },
      { status: 500 }
    )
  }
}
