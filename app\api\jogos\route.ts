import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const campeonato = searchParams.get("campeonato")
    const status = searchParams.get("status") || "agendado"
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 999
    const dias = searchParams.get("dias") ? parseInt(searchParams.get("dias")!) : 14

    // Query simplificada para MySQL
    let query = `
      SELECT
        j.*,
        c.nome as campeonato_nome,
        c.descricao as campeonato_descricao,
        c.logo_url as campeonato_logo,
        c.pais as campeonato_pais,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tc.pais as time_casa_pais,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        tf.pais as time_fora_pais
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      WHERE 1=1
    `

    const params = []

    if (status && status !== 'todos') {
      query += ' AND j.status = ?'
      params.push(status)
    }

    if (campeonato && campeonato !== 'todos') {
      query += ' AND c.id = ?'
      params.push(parseInt(campeonato))
    }

    if (status === 'agendado') {
      query += ' AND j.data_jogo >= NOW()'
      if (dias) {
        query += ' AND j.data_jogo <= DATE_ADD(NOW(), INTERVAL ? DAY)'
        params.push(dias)
      }
    } else if (status === 'finalizado') {
      query += ' AND j.data_jogo <= NOW()'
    }

    query += ' ORDER BY j.data_jogo ASC'

    if (limit && limit > 0) {
      query += ' LIMIT ?'
      params.push(limit)
    }

    const jogos = await executeQuery(query, params)

    // Estatísticas simplificadas
    const stats = await executeQuery(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN j.status = 'agendado' AND j.data_jogo >= NOW() THEN 1 END) as agendados,
        COUNT(CASE WHEN j.status = 'ao_vivo' THEN 1 END) as ao_vivo,
        COUNT(CASE WHEN j.status = 'finalizado' THEN 1 END) as finalizados,
        COUNT(CASE WHEN DATE(j.data_jogo) = CURDATE() THEN 1 END) as hoje
      FROM jogos j
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE c.status = 'ativo'
    `)

    // Buscar campeonatos disponíveis
    const campeonatos = await executeQuery(`
      SELECT DISTINCT c.id, c.nome, c.logo_url, c.pais, COUNT(j.id) as total_jogos
      FROM campeonatos c
      LEFT JOIN jogos j ON c.id = j.campeonato_id AND j.status = 'agendado' AND j.data_jogo >= NOW()
      WHERE c.status = 'ativo'
      GROUP BY c.id, c.nome, c.logo_url, c.pais
      HAVING total_jogos > 0
      ORDER BY total_jogos DESC, c.nome ASC
    `)

    return NextResponse.json({
      success: true,
      jogos: jogos || [],
      total: jogos?.length || 0,
      stats: stats[0] || { total: 0, agendados: 0, ao_vivo: 0, finalizados: 0, hoje: 0 },
      campeonatos: campeonatos || [],
      filters: {
        campeonato,
        status,
        limit,
        dias
      }
    })

  } catch (error) {
    console.error("❌ Erro ao buscar jogos:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        jogos: [],
        total: 0,
        stats: { total: 0, agendados: 0, ao_vivo: 0, finalizados: 0, hoje: 0 },
        campeonatos: []
      },
      { status: 500 }
    )
  }
}
