import mysql from "mysql2/promise"

// Configuração do banco de dados MySQL
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4',
  timezone: '+00:00',
}

// Função para criar uma nova conexão
export async function createConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig)
    console.log("✅ Conexão MySQL criada com sucesso")
    return connection
  } catch (error) {
    console.error("❌ Erro ao criar conexão MySQL:", error)
    throw error
  }
}

// Função para executar queries com tratamento de erro
export async function executeQuery(query: string, params: any[] = []) {
  const connection = await createConnection()
  try {
    const [results] = await connection.execute(query, params)
    return results
  } catch (error) {
    console.error("❌ Erro ao executar query:", error)
    console.error("Query:", query)
    console.error("Params:", params)
    throw error
  } finally {
    await connection.end()
  }
}

// Função para executar queries que retornam uma única linha
export async function executeQuerySingle(query: string, params: any[] = []) {
  try {
    const results = await executeQuery(query, params) as any[]
    return results.length > 0 ? results[0] : null
  } catch (error) {
    console.error("❌ Erro ao executar query single:", error)
    throw error
  }
}
