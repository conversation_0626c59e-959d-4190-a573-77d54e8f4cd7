import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await initializeDatabase()

    const bolaoId = parseInt(params.id)

    // Buscar ranking de apostadores
    const ranking = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) as acertos,
        COUNT(ad.id) as total_apostas,
        ROUND((COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) / COUNT(ad.id)) * 100, 2) as percentual_acertos,
        a.data_aposta
      FROM usuarios u
      JOIN apostas a ON u.id = a.usuario_id
      JOIN aposta_detalhes ad ON a.id = ad.aposta_id
      JOIN jogos j ON ad.jogo_id = j.id
      WHERE a.bolao_id = ? AND a.status = 'paga'
      GROUP BY u.id, u.nome, a.data_aposta
      ORDER BY acertos DESC, percentual_acertos DESC, a.data_aposta ASC
    `, [bolaoId])

    return NextResponse.json({
      success: true,
      ranking: ranking
    })

  } catch (error) {
    console.error("❌ Erro ao buscar ranking:", error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
