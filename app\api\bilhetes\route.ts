import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    // Por enquanto, vamos buscar todos os bilhetes
    // Em produção, isso deveria ser filtrado por usuário logado
    const bilhetes = await executeQuery(`
      SELECT 
        a.id,
        a.codigo_bilhete as codigo,
        b.nome as bolao,
        a.valor_total as valor,
        a.data_aposta as data,
        a.status,
        (SELECT COUNT(*) FROM aposta_detalhes WHERE aposta_id = a.id) as jogos,
        p.valor_premio as premio
      FROM apostas a
      JOIN boloes b ON a.bolao_id = b.id
      LEFT JOIN premios p ON a.id = p.aposta_id
      ORDER BY a.data_aposta DESC
      LIMIT 50
    `)

    // Formatar dados para o frontend
    const bilhetesFormatted = bilhetes.map((bilhete: any) => ({
      id: bilhete.id,
      codigo: bilhete.codigo || `BIL${bilhete.id.toString().padStart(3, '0')}`,
      bolao: bilhete.bolao,
      jogos: bilhete.jogos || 0,
      valor: parseFloat(bilhete.valor),
      data: new Date(bilhete.data).toISOString().split('T')[0],
      status: mapStatus(bilhete.status),
      premio: bilhete.premio ? parseFloat(bilhete.premio) : undefined
    }))

    return NextResponse.json(bilhetesFormatted)

  } catch (error) {
    console.error('❌ Erro ao buscar bilhetes:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function mapStatus(status: string) {
  switch (status) {
    case 'pendente':
      return 'pendente'
    case 'paga':
      return 'em_andamento'
    case 'cancelada':
      return 'perdida'
    default:
      return 'pendente'
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { bolao_id, apostas, valor_total } = body

    if (!bolao_id || !apostas || !valor_total) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Por enquanto, vamos usar um usuário padrão
    // Em produção, isso deveria vir da sessão do usuário
    const usuario_id = 1

    // Gerar código do bilhete
    const codigo_bilhete = `BIL${Date.now().toString().slice(-6)}`

    // Inserir aposta
    const result = await executeQuery(`
      INSERT INTO apostas (usuario_id, bolao_id, valor_total, codigo_bilhete, status)
      VALUES (?, ?, ?, ?, 'pendente')
    `, [usuario_id, bolao_id, valor_total, codigo_bilhete])

    const aposta_id = (result as any).insertId

    // Inserir detalhes das apostas
    for (const aposta of apostas) {
      await executeQuery(`
        INSERT INTO aposta_detalhes (aposta_id, jogo_id, resultado_apostado)
        VALUES (?, ?, ?)
      `, [aposta_id, aposta.jogo_id, aposta.resultado])
    }

    return NextResponse.json({
      success: true,
      aposta_id,
      codigo_bilhete,
      message: 'Bilhete criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar bilhete:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
