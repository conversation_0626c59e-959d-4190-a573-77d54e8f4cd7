-- Ta<PERSON><PERSON> para logs de webhook <PERSON><PERSON>
CREATE TABLE IF NOT EXISTS webhook_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_id VARCHAR(255),
  order_id VARCHAR(255),
  qr_code_payment_id VARCHAR(255),
  amount DECIMAL(10,2),
  description TEXT,
  status VARCHAR(50),
  end_to_end_id VARCHAR(255),
  last_updated_at TIMESTAMP NULL,
  error_message TEXT,
  webhook_data TEXT,
  processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_transaction_id (transaction_id),
  INDEX idx_order_id (order_id),
  INDEX idx_status (status),
  INDEX idx_processed_at (processed_at)
);

-- Adicionar colunas na tabela bilhetes se não existirem
ALTER TABLE bilhetes 
ADD COLUMN IF NOT EXISTS end_to_end_id VARCHAR(255) AFTER transaction_id;

-- Adicionar índices para melhor performance
ALTER TABLE bilhetes 
ADD INDEX IF NOT EXISTS idx_transaction_id (transaction_id),
ADD INDEX IF NOT EXISTS idx_status (status),
ADD INDEX IF NOT EXISTS idx_end_to_end_id (end_to_end_id);

-- Verificar estrutura das tabelas
DESCRIBE bilhetes;
DESCRIBE webhook_logs;
