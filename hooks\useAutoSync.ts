'use client'

import { useEffect, useRef } from 'react'

export function useAutoSync() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // Função para executar a sincronização
    const executarSincronizacao = async () => {
      try {
        console.log('🔄 Executando sincronização automática...')

        const response = await fetch('/api/sync', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ action: 'sync-now' })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Sincronização automática concluída:', result)
        } else {
          console.error('❌ Erro na sincronização automática:', response.status)
        }
      } catch (error) {
        console.error('❌ Erro na sincronização automática:', error)
      }
    }

    // Executar sincronização imediatamente ao carregar
    executarSincronizacao()

    // Configurar intervalo de 2 horas (2 * 60 * 60 * 1000 = 7200000ms)
    intervalRef.current = setInterval(executarSincronizacao, 2 * 60 * 60 * 1000)

    // Cleanup ao desmontar
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])

  return null
}
