import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Redirecionar favicon.ico para o endpoint da API
  if (request.nextUrl.pathname === '/favicon.ico') {
    return NextResponse.redirect(new URL('/api/favicon', request.url))
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: ['/favicon.ico']
}
