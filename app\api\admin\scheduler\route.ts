import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

export async function GET() {
  try {
    await initializeDatabase()

    // Buscar estatísticas básicas
    const [competitions, teams, matches] = await Promise.all([
      executeQuery("SELECT COUNT(*) as count FROM campeonatos WHERE status = 'ativo'"),
      executeQuery("SELECT COUNT(*) as count FROM times"),
      executeQuery("SELECT COUNT(*) as count FROM jogos WHERE status = 'agendado'")
    ])

    return NextResponse.json({
      success: true,
      scheduler: {
        status: 'ready',
        last_run: null,
        next_run: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // +3 dias
        error_count: 0,
        interval_days: 3,
        min_interval_hours: 72 // 3 dias em horas
      },
      stats: {
        competitions: competitions[0]?.count || 0,
        teams: teams[0]?.count || 0,
        matches: matches[0]?.count || 0
      },
      message: "Sistema de sincronização pronto. Use POST para executar ações."
    })

  } catch (error) {
    console.error("❌ Erro ao buscar status do scheduler:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const { action } = await request.json()
    
    await initializeDatabase()
    
    switch (action) {
      case 'force_sync':
        // Verificar throttling antes de sincronizar
        try {
          // Verificar última sincronização
          const lastSyncCheck = await executeQuery(`
            SELECT MAX(data_atualizacao) as last_sync
            FROM campeonatos
            WHERE api_id IS NOT NULL
          `)

          const lastSync = lastSyncCheck[0]?.last_sync
          if (lastSync) {
            const now = new Date()
            const lastSyncDate = new Date(lastSync)
            const hoursSinceLastSync = (now.getTime() - lastSyncDate.getTime()) / (1000 * 60 * 60)

            if (hoursSinceLastSync < 72) { // 3 dias = 72 horas
              return NextResponse.json({
                success: false,
                error: "Throttling ativo",
                message: `Aguarde ${Math.ceil(72 - hoursSinceLastSync)} horas para próxima sincronização`,
                next_sync_available: new Date(lastSyncDate.getTime() + (72 * 60 * 60 * 1000)).toISOString()
              }, { status: 429 })
            }
          }

          // Importar e executar sincronização MySQL
          const { syncFootballData } = await import('@/lib/sync-football-data.js')

          // Executar em background
          syncFootballData().then(() => {
            console.log('✅ Sincronização forçada concluída')
          }).catch(error => {
            console.error('❌ Erro na sincronização forçada:', error)
          })

          return NextResponse.json({
            success: true,
            message: "Sincronização iniciada em background (respeitando limite de 3 dias)"
          })

        } catch (error) {
          return NextResponse.json(
            {
              success: false,
              error: "Erro ao iniciar sincronização",
              message: (error as Error)?.message
            },
            { status: 500 }
          )
        }
        
      case 'reset_errors':
        // Resetar contador de erros
        await executeQuery(`
          UPDATE scheduler_status 
          SET error_count = 0, last_error = NULL, updated_at = CURRENT_TIMESTAMP
          WHERE id = 1
        `)
        
        return NextResponse.json({
          success: true,
          message: "Contador de erros resetado"
        })
        
      case 'reschedule':
        // Reagendar para próxima execução
        const nextRun = new Date()
        nextRun.setTime(nextRun.getTime() + (3 * 24 * 60 * 60 * 1000)) // +3 dias

        await executeQuery(`
          UPDATE scheduler_status
          SET next_run = ?, status = 'idle', updated_at = CURRENT_TIMESTAMP
          WHERE id = 1
        `, [nextRun.toISOString()])

        return NextResponse.json({
          success: true,
          message: `Reagendado para ${nextRun.toLocaleString('pt-BR')} (intervalo de 3 dias)`
        })
        
      case 'get_logs':
        // Buscar logs detalhados
        const { type, limit = 50 } = await request.json()
        
        let query = `
          SELECT * FROM sync_logs 
          WHERE 1=1
        `
        const params = []
        
        if (type && type !== 'all') {
          query += ' AND type = ?'
          params.push(type)
        }
        
        query += ' ORDER BY timestamp DESC LIMIT ?'
        params.push(limit)
        
        const logs = await executeQuery(query, params)
        
        return NextResponse.json({
          success: true,
          logs: logs || []
        })

      case 'check_throttle':
        // Verificar status de throttling
        const throttleCheck = await executeQuery(`
          SELECT MAX(data_atualizacao) as last_sync
          FROM campeonatos
          WHERE api_id IS NOT NULL
        `)

        const lastSyncTime = throttleCheck[0]?.last_sync
        let throttleStatus: {
          can_sync: boolean,
          hours_remaining: number,
          next_available: string | null,
          last_sync: any
        } = {
          can_sync: true,
          hours_remaining: 0,
          next_available: null,
          last_sync: lastSyncTime
        }

        if (lastSyncTime) {
          const now = new Date()
          const lastSyncDate = new Date(lastSyncTime)
          const hoursSinceLastSync = (now.getTime() - lastSyncDate.getTime()) / (1000 * 60 * 60)

          if (hoursSinceLastSync < 72) { // 3 dias = 72 horas
            throttleStatus = {
              can_sync: false,
              hours_remaining: Math.ceil(72 - hoursSinceLastSync),
              next_available: new Date(lastSyncDate.getTime() + (72 * 60 * 60 * 1000)).toISOString(),
              last_sync: lastSyncTime
            }
          }
        }

        return NextResponse.json({
          success: true,
          throttle: throttleStatus
        })

      default:
        return NextResponse.json(
          { success: false, error: "Ação inválida" },
          { status: 400 }
        )
    }
    
  } catch (error) {
    console.error("❌ Erro na ação do scheduler:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
