import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function addPixOrderIdColumn() {
  try {
    console.log('🔧 Adicionando coluna pix_order_id na tabela bilhetes...')
    
    await initializeDatabase()
    
    // Verificar se a coluna já existe
    const columns = await executeQuery(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'sistema-bolao-top' 
      AND TABLE_NAME = 'bilhetes' 
      AND COLUMN_NAME = 'pix_order_id'
    `)
    
    if (columns.length > 0) {
      console.log('✅ Coluna pix_order_id já existe')
      return
    }
    
    // Adicionar a coluna
    await executeQuery(`
      ALTER TABLE bilhetes 
      ADD COLUMN pix_order_id VARCHAR(100) NULL 
      AFTER transaction_id
    `)
    
    console.log('✅ Coluna pix_order_id adicionada com sucesso')
    
    // Adicionar índice para performance
    await executeQuery(`
      ALTER TABLE bilhetes 
      ADD INDEX idx_pix_order_id (pix_order_id)
    `)
    
    console.log('✅ Índice idx_pix_order_id criado com sucesso')
    
    // Verificar estrutura da tabela
    const tableStructure = await executeQuery(`
      DESCRIBE bilhetes
    `)
    
    console.log('📊 Estrutura atual da tabela bilhetes:')
    tableStructure.forEach(column => {
      console.log(`  - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'}`)
    })
    
  } catch (error) {
    console.error('❌ Erro ao adicionar coluna pix_order_id:', error)
    throw error
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  addPixOrderIdColumn()
    .then(() => {
      console.log('🎉 Script concluído com sucesso!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Script falhou:', error)
      process.exit(1)
    })
}

export { addPixOrderIdColumn }
