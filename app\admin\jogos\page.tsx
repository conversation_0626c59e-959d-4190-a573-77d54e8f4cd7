"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Calendar, 
  Clock, 
  Trophy, 
  Target, 
  Loader2, 
  Search,
  Filter,
  Play,
  CheckCircle,
  XCircle
} from "lucide-react"

interface Jogo {
  id: number
  campeonato_id: number
  campeonato_nome: string
  time_casa_id: number
  time_casa_nome: string
  time_casa_curto: string
  time_casa_logo: string
  time_fora_id: number
  time_fora_nome: string
  time_fora_curto: string
  time_fora_logo: string
  data_jogo: string
  local_jogo: string
  rodada: number
  resultado_casa: number | null
  resultado_fora: number | null
  status: "agendado" | "ao_vivo" | "finalizado" | "cancelado"
}

interface JogosStats {
  hoje: number
  semana: number
  total: number
  aoVivo: number
}

interface Campeonato {
  id: number
  nome: string
  descricao: string
  pais: string
  temporada: string
  status: string
}

export default function JogosPage() {
  const [jogos, setJogos] = useState<Jogo[]>([])
  const [campeonatos, setCampeonatos] = useState<Campeonato[]>([])
  const [stats, setStats] = useState<JogosStats>({ hoje: 0, semana: 0, total: 0, aoVivo: 0 })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCampeonato, setSelectedCampeonato] = useState<string>("todos")
  const [selectedStatus, setSelectedStatus] = useState<string>("todos")

  const fetchData = async () => {
    try {
      setLoading(true)
      
      // Buscar jogos com filtros
      const jogosParams = new URLSearchParams()
      if (selectedCampeonato !== "todos") jogosParams.append("campeonato_id", selectedCampeonato)
      if (selectedStatus !== "todos") jogosParams.append("status", selectedStatus)
      
      const [jogosResponse, campeonatosResponse] = await Promise.all([
        fetch(`/api/admin/jogos?${jogosParams}`),
        fetch("/api/admin/campeonatos")
      ])

      if (!jogosResponse.ok || !campeonatosResponse.ok) {
        throw new Error("Erro ao carregar dados")
      }

      const [jogosData, campeonatosData] = await Promise.all([
        jogosResponse.json(),
        campeonatosResponse.json()
      ])

      setJogos(jogosData.jogos || [])
      setStats(jogosData.stats || { hoje: 0, semana: 0, total: 0, aoVivo: 0 })
      setCampeonatos(campeonatosData.campeonatos || [])
    } catch (error) {
      console.error("Erro ao carregar dados:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [selectedCampeonato, selectedStatus])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "agendado":
        return <Clock className="h-4 w-4 text-blue-500" />
      case "ao_vivo":
        return <Play className="h-4 w-4 text-red-500" />
      case "finalizado":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "cancelado":
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "agendado":
        return "bg-blue-100 text-blue-800"
      case "ao_vivo":
        return "bg-red-100 text-red-800"
      case "finalizado":
        return "bg-green-100 text-green-800"
      case "cancelado":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString("pt-BR"),
      time: date.toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" })
    }
  }

  // Filtrar jogos por termo de busca
  const filteredJogos = jogos.filter(jogo => 
    jogo.time_casa_nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    jogo.time_fora_nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    jogo.campeonato_nome.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Agrupar jogos por campeonato
  const jogosPorCampeonato = filteredJogos.reduce((acc, jogo) => {
    const campeonato = jogo.campeonato_nome
    if (!acc[campeonato]) {
      acc[campeonato] = []
    }
    acc[campeonato].push(jogo)
    return acc
  }, {} as Record<string, Jogo[]>)

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Jogos</h1>
        <p className="text-gray-600 mt-2">Gerencie todos os jogos e partidas do sistema</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Jogos Hoje</p>
                <p className="text-3xl font-bold text-gray-900">{stats.hoje}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Esta Semana</p>
                <p className="text-3xl font-bold text-gray-900">{stats.semana}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Trophy className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Ao Vivo</p>
                <p className="text-3xl font-bold text-gray-900">{stats.aoVivo}</p>
              </div>
              <div className="bg-red-100 p-3 rounded-full">
                <Play className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <Target className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar times ou campeonatos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedCampeonato} onValueChange={setSelectedCampeonato}>
              <SelectTrigger>
                <SelectValue placeholder="Selecionar campeonato" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os campeonatos</SelectItem>
                {campeonatos.map((campeonato) => (
                  <SelectItem key={campeonato.id} value={campeonato.id.toString()}>
                    {campeonato.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="todos">Todos os status</SelectItem>
                <SelectItem value="agendado">Agendado</SelectItem>
                <SelectItem value="ao_vivo">Ao Vivo</SelectItem>
                <SelectItem value="finalizado">Finalizado</SelectItem>
                <SelectItem value="cancelado">Cancelado</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Jogos por Campeonato */}
      {Object.keys(jogosPorCampeonato).length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nenhum jogo encontrado</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(jogosPorCampeonato).map(([campeonato, jogosGrupo]) => (
            <Card key={campeonato}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Trophy className="h-5 w-5 mr-2 text-yellow-600" />
                  {campeonato}
                </CardTitle>
                <CardDescription>
                  {jogosGrupo.length} {jogosGrupo.length === 1 ? 'jogo' : 'jogos'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {jogosGrupo.map((jogo) => {
                    const { date, time } = formatDateTime(jogo.data_jogo)

                    return (
                      <div
                        key={jogo.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center space-x-4">
                          {/* Status */}
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(jogo.status)}
                            <Badge className={getStatusColor(jogo.status)}>
                              {jogo.status.replace("_", " ")}
                            </Badge>
                          </div>

                          {/* Times */}
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <p className="font-medium">{jogo.time_casa_nome}</p>
                              <p className="text-sm text-gray-500">{jogo.time_casa_curto}</p>
                            </div>

                            <div className="flex items-center space-x-2 px-3">
                              {jogo.status === "finalizado" && jogo.resultado_casa !== null && jogo.resultado_fora !== null ? (
                                <div className="text-center">
                                  <p className="font-bold text-lg">
                                    {jogo.resultado_casa} - {jogo.resultado_fora}
                                  </p>
                                </div>
                              ) : (
                                <div className="text-center">
                                  <p className="text-gray-400 font-medium">VS</p>
                                </div>
                              )}
                            </div>

                            <div className="text-left">
                              <p className="font-medium">{jogo.time_fora_nome}</p>
                              <p className="text-sm text-gray-500">{jogo.time_fora_curto}</p>
                            </div>
                          </div>
                        </div>

                        {/* Data e Hora */}
                        <div className="text-right">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Calendar className="h-4 w-4" />
                            <span>{date}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                            <Clock className="h-4 w-4" />
                            <span>{time}</span>
                          </div>
                          {jogo.rodada && (
                            <p className="text-xs text-gray-500 mt-1">
                              Rodada {jogo.rodada}
                            </p>
                          )}
                          {jogo.local_jogo && (
                            <p className="text-xs text-gray-500">
                              {jogo.local_jogo}
                            </p>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
