import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`🔍 Buscando status do bolão ID: ${params.id}`)

    const bolaoId = parseInt(params.id)

    try {
      await initializeDatabase()

      // Buscar informações do bolão com timeout
      const bolao = await Promise.race([
        executeQuery(`SELECT * FROM boloes WHERE id = ?`, [bolaoId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na consulta do bolão')), 3000)
        )
      ])

      if (!bolao || bolao.length === 0) {
        return NextResponse.json(
          { success: false, error: "Bolão não encontrado" },
          { status: 404 }
        )
      }

      const bolaoData = bolao[0]

    // Buscar partidas do bolão
    let partidas = []
    try {
      const partidasSelecionadas = JSON.parse(bolaoData.partidas_selecionadas || '[]')
      const partidasIds = partidasSelecionadas.map((p: any) => p.id || p)
      
      if (partidasIds.length > 0) {
        const placeholders = partidasIds.map(() => '?').join(',')
        partidas = await executeQuery(`
          SELECT 
            j.*,
            tc.nome as time_casa_nome,
            tf.nome as time_fora_nome
          FROM jogos j
          LEFT JOIN times tc ON j.time_casa_id = tc.id
          LEFT JOIN times tf ON j.time_fora_id = tf.id
          WHERE j.id IN (${placeholders})
          ORDER BY j.data_jogo ASC
        `, partidasIds)
      }
    } catch (error) {
      console.error('Erro ao buscar partidas:', error)
    }

    // Verificar se algum jogo já começou
    const agora = new Date()
    const jogosIniciados = partidas.filter((partida: any) => {
      const dataJogo = new Date(partida.data_jogo)
      return dataJogo <= agora
    })

    // Verificar se todos os jogos terminaram
    const jogosFinalizados = partidas.filter((partida: any) => {
      return partida.status === 'finalizado'
    })

    const apostasEncerradas = jogosIniciados.length > 0
    const bolaoFinalizado = jogosFinalizados.length === partidas.length && partidas.length > 0

    // Se o bolão finalizou, buscar ranking
    let ranking = []
    if (bolaoFinalizado) {
      ranking = await executeQuery(`
        SELECT 
          u.id,
          u.nome,
          COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) as acertos,
          COUNT(ad.id) as total_apostas,
          ROUND((COUNT(CASE WHEN ad.acertou = 1 THEN 1 END) / COUNT(ad.id)) * 100, 2) as percentual_acertos
        FROM usuarios u
        JOIN apostas a ON u.id = a.usuario_id
        JOIN aposta_detalhes ad ON a.id = ad.aposta_id
        JOIN jogos j ON ad.jogo_id = j.id
        WHERE a.bolao_id = ? AND a.status = 'paga'
        GROUP BY u.id, u.nome
        ORDER BY acertos DESC, percentual_acertos DESC
        LIMIT 10
      `, [bolaoId])
    }

      console.log(`✅ Status do bolão ${bolaoId} carregado do banco`)

      return NextResponse.json({
        success: true,
        bolao: {
          id: bolaoData.id,
          nome: bolaoData.nome,
          status: bolaoData.status
        },
        apostas_encerradas: apostasEncerradas,
        bolao_finalizado: bolaoFinalizado,
        total_partidas: partidas.length,
        jogos_iniciados: jogosIniciados.length,
        jogos_finalizados: jogosFinalizados.length,
        ranking: ranking,
        proxima_partida: jogosIniciados.length === 0 && partidas.length > 0 ? partidas[0] : null,
        source: 'database'
      })

    } catch (dbError) {
      console.warn(`⚠️ Erro ao acessar banco, retornando dados básicos:`, (dbError as Error).message)

      // Retornar dados básicos sem erro
      return NextResponse.json({
        success: true,
        bolao: {
          id: bolaoId,
          nome: `Bolão ${bolaoId}`,
          status: 'ativo'
        },
        apostas_encerradas: false,
        bolao_finalizado: false,
        total_partidas: 0,
        jogos_iniciados: 0,
        jogos_finalizados: 0,
        ranking: [],
        proxima_partida: null,
        source: 'fallback'
      })
    }

  } catch (error) {
    console.error("❌ Erro ao verificar status do bolão:", error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
