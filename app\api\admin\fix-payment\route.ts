import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: NextRequest) {
  try {
    const { action, bilhete_codigo, order_id } = await request.json()

    if (!action) {
      return NextResponse.json({
        success: false,
        error: "action é obrigatório (fix_payment, check_status)"
      }, { status: 400 })
    }

    await initializeDatabase()

    if (action === 'fix_payment') {
      // Buscar bilhete pendente mais recente
      const bilhetesPendentes = await executeQuery(`
        SELECT id, codigo, status, transaction_id, pix_order_id, valor_total, usuario_nome, created_at
        FROM bilhetes
        WHERE status = 'pendente'
        ORDER BY created_at DESC
        LIMIT 5
      `)

      console.log("📋 Bilhetes pendentes encontrados:", bilhetesPendentes)

      if (!Array.isArray(bilhetesPendentes) || bilhetesPendentes.length === 0) {
        return NextResponse.json({
          success: false,
          error: "Nenhum bilhete pendente encontrado"
        })
      }

      // Se foi especificado um código, buscar esse bilhete
      let bilheteParaAtualizar = bilhetesPendentes[0]
      if (bilhete_codigo) {
        const bilheteEspecifico = bilhetesPendentes.find(b => b.codigo === bilhete_codigo)
        if (bilheteEspecifico) {
          bilheteParaAtualizar = bilheteEspecifico
        }
      }

      console.log("🎯 Bilhete selecionado para atualização:", bilheteParaAtualizar)

      // Atualizar bilhete para pago
      const updateResult = await executeQuery(`
        UPDATE bilhetes
        SET status = 'pago', pix_order_id = ?, updated_at = NOW()
        WHERE id = ?
      `, [order_id || '5f238dd289044a8bb90ddb98d5e6e418', bilheteParaAtualizar.id])

      console.log("✅ Resultado da atualização:", updateResult)

      // Buscar bilhete atualizado
      const bilheteAtualizado = await executeQuery(`
        SELECT * FROM bilhetes WHERE id = ?
      `, [bilheteParaAtualizar.id])

      return NextResponse.json({
        success: true,
        message: "Bilhete atualizado para pago com sucesso",
        bilhete_original: bilheteParaAtualizar,
        bilhete_atualizado: bilheteAtualizado[0],
        linhas_afetadas: (updateResult as any)?.affectedRows || 0,
        order_id_usado: order_id || '5f238dd289044a8bb90ddb98d5e6e418'
      })

    } else if (action === 'check_status') {
      // Verificar status atual dos bilhetes
      const bilhetes = await executeQuery(`
        SELECT id, codigo, status, transaction_id, pix_order_id, valor_total, created_at
        FROM bilhetes
        ORDER BY created_at DESC
        LIMIT 10
      `)

      const webhookLogs = await executeQuery(`
        SELECT id, order_id, status, processed_at, webhook_data
        FROM webhook_logs
        ORDER BY processed_at DESC
        LIMIT 5
      `)

      return NextResponse.json({
        success: true,
        bilhetes_recentes: bilhetes,
        webhook_logs_recentes: webhookLogs,
        timestamp: new Date().toISOString()
      })

    } else {
      return NextResponse.json({
        success: false,
        error: "Ação não reconhecida. Use 'fix_payment' ou 'check_status'"
      }, { status: 400 })
    }

  } catch (error) {
    console.error("❌ Erro no endpoint de correção:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Endpoint para correção de pagamentos",
    usage: {
      POST: {
        fix_payment: "Corrige o pagamento do bilhete pendente mais recente",
        check_status: "Verifica status dos bilhetes e logs de webhook"
      }
    },
    examples: {
      fix_payment: {
        action: "fix_payment",
        bilhete_codigo: "BLT1753151047192278GHFMX", // opcional
        order_id: "5f238dd289044a8bb90ddb98d5e6e418" // opcional
      },
      check_status: {
        action: "check_status"
      }
    }
  })
}
