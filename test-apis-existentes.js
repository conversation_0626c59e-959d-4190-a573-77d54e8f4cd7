#!/usr/bin/env node

/**
 * Teste das APIs existentes no sistema
 */

const BASE_URL = 'http://localhost:3000'

async function testarAPIsExistentes() {
  console.log('🧪 Testando APIs existentes do sistema...\n')

  try {
    // 1. Testar API de campeonatos
    console.log('1️⃣ Testando API de campeonatos...')
    const campeonatosResponse = await fetch(`${BASE_URL}/api/campeonatos`)
    if (campeonatosResponse.ok) {
      const campeonatos = await campeonatosResponse.json()
      console.log('✅ API campeonatos funcionando:', campeonatos.length || 0, 'campeonatos')
    } else {
      console.log('❌ Erro API campeonatos:', campeonatosResponse.status)
    }

    // 2. Testar API de times
    console.log('\n2️⃣ Testando API de times...')
    const timesResponse = await fetch(`${BASE_URL}/api/times`)
    if (timesResponse.ok) {
      const times = await timesResponse.json()
      console.log('✅ API times funcionando:', times.length || 0, 'times')
    } else {
      console.log('❌ Erro API times:', timesResponse.status)
    }

    // 3. Testar API de jogos
    console.log('\n3️⃣ Testando API de jogos...')
    const jogosResponse = await fetch(`${BASE_URL}/api/jogos`)
    if (jogosResponse.ok) {
      const jogos = await jogosResponse.json()
      console.log('✅ API jogos funcionando:', jogos.length || 0, 'jogos')
    } else {
      console.log('❌ Erro API jogos:', jogosResponse.status)
    }

    // 4. Testar API de bolões
    console.log('\n4️⃣ Testando API de bolões...')
    const boloesResponse = await fetch(`${BASE_URL}/api/boloes`)
    if (boloesResponse.ok) {
      const boloes = await boloesResponse.json()
      console.log('✅ API bolões funcionando:', boloes.length || 0, 'bolões')
    } else {
      console.log('❌ Erro API bolões:', boloesResponse.status)
    }

    // 5. Testar API de bilhetes
    console.log('\n5️⃣ Testando API de bilhetes...')
    const bilhetesResponse = await fetch(`${BASE_URL}/api/bilhetes`)
    if (bilhetesResponse.ok) {
      const bilhetes = await bilhetesResponse.json()
      console.log('✅ API bilhetes funcionando:', bilhetes.length || 0, 'bilhetes')
    } else {
      console.log('❌ Erro API bilhetes:', bilhetesResponse.status)
    }

    // 6. Testar API de football
    console.log('\n6️⃣ Testando API de football...')
    const footballResponse = await fetch(`${BASE_URL}/api/football/competitions`)
    if (footballResponse.ok) {
      const football = await footballResponse.json()
      console.log('✅ API football funcionando')
    } else {
      console.log('❌ Erro API football:', footballResponse.status)
    }

    // 7. Testar sistema PIX com parâmetros corretos
    console.log('\n7️⃣ Testando sistema PIX (depósito)...')
    const pixResponse = await fetch(`${BASE_URL}/api/wallet/deposit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_id: 585,
        valor: 10.00,
        client_name: 'Teste API'
      })
    })
    
    if (pixResponse.ok) {
      const pixData = await pixResponse.json()
      console.log('✅ Sistema PIX funcionando:', pixData.success ? 'OK' : 'ERRO')
      if (pixData.deposito) {
        console.log('💰 Depósito criado:', pixData.deposito.transaction_id)
      }
    } else {
      console.log('❌ Erro no sistema PIX:', pixResponse.status)
    }

    console.log('\n✅ Teste de APIs finalizado!')
    console.log('🎯 Sistema está funcionando!')
    
  } catch (error) {
    console.error('❌ Erro no teste de APIs:', error)
  }
}

// Executar teste
testarAPIsExistentes()
