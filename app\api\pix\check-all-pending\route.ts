import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    console.log("🔄 Iniciando verificação automática de todos os bilhetes pendentes...")

    await initializeDatabase()

    // Buscar todos os bilhetes pendentes
    const bilhetesPendentes = await executeQuery(`
      SELECT 
        id, codigo, transaction_id, status, valor_total, 
        created_at, updated_at, usuario_id, usuario_nome
      FROM bilhetes 
      WHERE status = 'pendente'
      ORDER BY created_at DESC
      LIMIT 50
    `)

    console.log(`📊 Encontrados ${bilhetesPendentes.length} bilhetes pendentes`)

    if (bilhetesPendentes.length === 0) {
      return NextResponse.json({
        success: true,
        message: "Nenhum bilhete pendente encontrado",
        processed: 0,
        updated: 0
      })
    }

    const pixApiUrl = process.env.PIX_API_URL || 'https://api.meiodepagamento.com/api/V1'
    const pixApiToken = process.env.PIX_API_TOKEN || ''

    let processedCount = 0
    let updatedCount = 0
    const results = []

    // Processar cada bilhete pendente
    for (const bilhete of bilhetesPendentes) {
      try {
        processedCount++
        console.log(`🔍 Verificando bilhete ${bilhete.codigo} (${processedCount}/${bilhetesPendentes.length})`)

        let statusAtualizado = bilhete.status
        let shouldUpdate = false
        let providerResponse = null

        // Verificar tempo decorrido
        const agora = new Date()
        const criadoEm = new Date(bilhete.created_at)
        const tempoDecorrido = (agora.getTime() - criadoEm.getTime()) / 1000

        // Tentar consultar API PIX
        if (bilhete.transaction_id && pixApiToken) {
          try {
            const statusResponse = await fetch(`${pixApiUrl}/pix/status/${bilhete.transaction_id}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${pixApiToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              },
              timeout: 5000 // 5 segundos de timeout
            })
            
            if (statusResponse.ok) {
              providerResponse = await statusResponse.json()
              console.log(`💳 API PIX resposta para ${bilhete.codigo}:`, providerResponse.status)
              
              // Verificar se foi pago
              if (providerResponse.status === 'PAID' || 
                  providerResponse.status === 'paid' || 
                  providerResponse.status === 'approved' ||
                  providerResponse.status === 'APPROVED' ||
                  providerResponse.payment_status === 'PAID') {
                
                statusAtualizado = 'pago'
                shouldUpdate = true
                console.log(`✅ Bilhete ${bilhete.codigo} aprovado via API PIX!`)
              }
            } else {
              console.log(`⚠️ API PIX erro para ${bilhete.codigo}:`, statusResponse.status)
            }
          } catch (apiError) {
            console.log(`⚠️ Erro na API PIX para ${bilhete.codigo}:`, apiError.message)
          }
        }

        // FALLBACK AUTOMÁTICO DESABILITADO - Aguardando webhook oficial
        if (!shouldUpdate && tempoDecorrido > 30) {
          console.log(`🔒 FALLBACK DESABILITADO: ${bilhete.codigo} permanece pendente (${Math.round(tempoDecorrido)}s)`)
          console.log(`⚠️ Aguardando webhook oficial em: https://ouroemu.site/api/v1/MP/webhookruntransation`)
        }

        // Atualizar no banco se necessário
        if (shouldUpdate) {
          await executeQuery(`
            UPDATE bilhetes 
            SET status = ?, updated_at = NOW() 
            WHERE id = ?
          `, [statusAtualizado, bilhete.id])

          updatedCount++
          console.log(`💾 Bilhete ${bilhete.codigo} atualizado para: ${statusAtualizado}`)

          // Disparar webhook interno para processar o pagamento
          try {
            await fetch(`${request.nextUrl.origin}/api/v1/MP/webhookruntransation`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                order_id: bilhete.codigo,
                status: 'PAID',
                type: 'PIXOUT',
                message: 'Payment approved via automatic check'
              })
            })
            console.log(`📡 Webhook disparado para ${bilhete.codigo}`)
          } catch (webhookError) {
            console.log(`⚠️ Erro no webhook para ${bilhete.codigo}:`, webhookError.message)
          }
        }

        results.push({
          bilhete_id: bilhete.id,
          codigo: bilhete.codigo,
          status_anterior: bilhete.status,
          status_atual: statusAtualizado,
          updated: shouldUpdate,
          tempo_decorrido: Math.round(tempoDecorrido),
          provider_response: providerResponse
        })

        // Pequena pausa entre requisições para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (bilheteError) {
        console.error(`❌ Erro ao processar bilhete ${bilhete.codigo}:`, bilheteError)
        results.push({
          bilhete_id: bilhete.id,
          codigo: bilhete.codigo,
          error: bilheteError.message,
          updated: false
        })
      }
    }

    console.log(`✅ Verificação concluída: ${processedCount} processados, ${updatedCount} atualizados`)

    return NextResponse.json({
      success: true,
      message: `Verificação automática concluída`,
      processed: processedCount,
      updated: updatedCount,
      results: results,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro na verificação automática:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
