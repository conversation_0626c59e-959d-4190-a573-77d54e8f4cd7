import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; userId: string } }
) {
  try {
    await initializeDatabase()

    const bolaoId = parseInt(params.id)
    const userId = parseInt(params.userId)

    // Buscar apostas do usuário no bolão
    const apostas = await executeQuery(`
      SELECT 
        ad.*,
        j.data_jogo,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        j.resultado_casa,
        j.resultado_fora,
        j.status as jogo_status,
        c.nome as campeonato_nome
      FROM apostas a
      JOIN aposta_detalhes ad ON a.id = ad.aposta_id
      JOIN jogos j ON ad.jogo_id = j.id
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE a.bolao_id = ? AND a.usuario_id = ? AND a.status = 'paga'
      ORDER BY j.data_jogo ASC
    `, [bolaoId, userId])

    // Buscar informações do usuário
    const usuario = await executeQuery(`
      SELECT id, nome FROM usuarios WHERE id = ?
    `, [userId])

    if (!usuario || usuario.length === 0) {
      return NextResponse.json(
        { success: false, error: "Usuário não encontrado" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      usuario: usuario[0],
      apostas: apostas
    })

  } catch (error) {
    console.error("❌ Erro ao buscar apostas do usuário:", error)
    return NextResponse.json(
      { success: false, error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}
