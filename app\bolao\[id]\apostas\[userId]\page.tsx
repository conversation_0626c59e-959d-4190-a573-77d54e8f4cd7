"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, User, Calendar, Trophy, Target } from "lucide-react"
import Link from "next/link"

interface Aposta {
  id: number
  jogo_id: number
  resultado_apostado: string
  acertou: boolean
  data_jogo: string
  time_casa_nome: string
  time_casa_curto: string
  time_casa_logo: string
  time_fora_nome: string
  time_fora_curto: string
  time_fora_logo: string
  resultado_casa: number | null
  resultado_fora: number | null
  jogo_status: string
  campeonato_nome: string
}

interface Usuario {
  id: number
  nome: string
}

export default function ApostasUsuarioPage() {
  const params = useParams()
  const bolaoId = params.id as string
  const userId = params.userId as string

  const [apostas, setApostas] = useState<Aposta[]>([])
  const [usuario, setUsuario] = useState<Usuario | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadApostas()
  }, [bolaoId, userId])

  const loadApostas = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/boloes/${bolaoId}/apostas/${userId}`)
      const data = await response.json()

      if (data.success) {
        setApostas(data.apostas)
        setUsuario(data.usuario)
      }
    } catch (error) {
      console.error("Erro ao carregar apostas:", error)
    } finally {
      setLoading(false)
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getResultadoTexto = (resultado: string) => {
    switch (resultado) {
      case "casa":
        return "Casa"
      case "empate":
        return "Empate"
      case "fora":
        return "Fora"
      default:
        return resultado
    }
  }

  const getResultadoReal = (resultadoCasa: number | null, resultadoFora: number | null) => {
    if (resultadoCasa === null || resultadoFora === null) {
      return "Aguardando"
    }
    
    if (resultadoCasa > resultadoFora) {
      return "Casa"
    } else if (resultadoFora > resultadoCasa) {
      return "Fora"
    } else {
      return "Empate"
    }
  }

  const acertos = apostas.filter(a => a.acertou).length
  const percentualAcertos = apostas.length > 0 ? (acertos / apostas.length) * 100 : 0

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-white">Carregando apostas...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <header className="bg-slate-800 border-b border-slate-700 p-4">
        <div className="max-w-4xl mx-auto flex items-center gap-4">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Voltar
            </Button>
          </Link>
          <div className="flex items-center gap-2">
            <User className="h-5 w-5 text-green-500" />
            <h1 className="text-xl font-bold">Apostas de {usuario?.nome}</h1>
          </div>
        </div>
      </header>

      {/* Stats */}
      <div className="max-w-4xl mx-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4 text-center">
              <Trophy className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-500">{acertos}</div>
              <div className="text-sm text-slate-400">Acertos</div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4 text-center">
              <Target className="h-8 w-8 text-blue-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white">{apostas.length}</div>
              <div className="text-sm text-slate-400">Total de Apostas</div>
            </CardContent>
          </Card>
          
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-4 text-center">
              <Calendar className="h-8 w-8 text-purple-500 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-500">{percentualAcertos.toFixed(1)}%</div>
              <div className="text-sm text-slate-400">Aproveitamento</div>
            </CardContent>
          </Card>
        </div>

        {/* Apostas */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold mb-4">Detalhes das Apostas</h2>
          
          {apostas.map((aposta) => (
            <Card key={aposta.id} className="bg-slate-800 border-slate-700">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <img
                        src={aposta.time_casa_logo || "/placeholder.svg"}
                        alt={aposta.time_casa_nome}
                        className="w-6 h-6 object-contain"
                        onError={(e) => {
                          const target = e.currentTarget as HTMLImageElement
                          target.src = "/placeholder.svg"
                        }}
                      />
                      <span className="font-medium">{aposta.time_casa_curto}</span>
                    </div>
                    <span className="text-slate-400">vs</span>
                    <div className="flex items-center gap-2">
                      <img
                        src={aposta.time_fora_logo || "/placeholder.svg"}
                        alt={aposta.time_fora_nome}
                        className="w-6 h-6 object-contain"
                        onError={(e) => {
                          const target = e.currentTarget as HTMLImageElement
                          target.src = "/placeholder.svg"
                        }}
                      />
                      <span className="font-medium">{aposta.time_fora_curto}</span>
                    </div>
                  </div>
                  
                  <Badge variant={aposta.acertou ? "default" : "destructive"}>
                    {aposta.acertou ? "✓ Acertou" : "✗ Errou"}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-slate-400">Apostou:</span>
                    <div className="font-medium text-blue-400">
                      {getResultadoTexto(aposta.resultado_apostado)}
                    </div>
                  </div>
                  
                  <div>
                    <span className="text-slate-400">Resultado:</span>
                    <div className="font-medium">
                      {aposta.resultado_casa !== null && aposta.resultado_fora !== null ? (
                        <span className={aposta.acertou ? "text-green-400" : "text-red-400"}>
                          {aposta.resultado_casa} x {aposta.resultado_fora} ({getResultadoReal(aposta.resultado_casa, aposta.resultado_fora)})
                        </span>
                      ) : (
                        <span className="text-yellow-400">Aguardando</span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <span className="text-slate-400">Data:</span>
                    <div className="font-medium">{formatDateTime(aposta.data_jogo)}</div>
                  </div>
                </div>
                
                <div className="mt-2 text-xs text-slate-500">
                  {aposta.campeonato_nome}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
