// Teste do sistema PIX - Depósito na Carteira
// Execute este arquivo com: node test-pix.js

const BASE_URL = 'http://localhost:3000'

async function testarSistemaPIX() {
  console.log('🧪 Iniciando teste do sistema PIX...\n')

  try {
    // 1. Criar um depósito PIX
    console.log('1️⃣ Criando depósito PIX...')
    const depositoResponse = await fetch(`${BASE_URL}/api/wallet/deposit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: 585,
        valor: 50.00,
        client_name: '<PERSON><PERSON><PERSON><PERSON> Teste',
        client_email: '<EMAIL>',
        client_document: '12345678901'
      })
    })

    const depositoData = await depositoResponse.json()
    console.log('✅ Depósito criado:', depositoData)

    if (!depositoData.success) {
      throw new Error('<PERSON>alha ao criar depósito: ' + depositoData.error)
    }

    const transactionId = depositoData.deposito.transaction_id
    console.log(`📋 Transaction ID: ${transactionId}\n`)

    // 2. Verificar status inicial
    console.log('2️⃣ Verificando status inicial...')
    const statusResponse = await fetch(`${BASE_URL}/api/wallet/webhook?transaction_id=${transactionId}`)
    const statusData = await statusResponse.json()
    console.log('📊 Status inicial:', statusData)
    console.log('')

    // 3. Simular webhook de confirmação
    console.log('3️⃣ Simulando confirmação de pagamento via webhook...')
    const webhookResponse = await fetch(`${BASE_URL}/api/wallet/webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        transaction_id: transactionId,
        order_id: depositoData.deposito.order_id,
        status: 'PAID',
        value: 50.00,
        end_to_end_id: `E${Date.now()}${Math.random().toString(36).substr(2, 9)}`
      })
    })

    const webhookData = await webhookResponse.json()
    console.log('🎉 Webhook processado:', webhookData)
    console.log('')

    // 4. Verificar status final
    console.log('4️⃣ Verificando status final...')
    const statusFinalResponse = await fetch(`${BASE_URL}/api/wallet/webhook?transaction_id=${transactionId}`)
    const statusFinalData = await statusFinalResponse.json()
    console.log('📊 Status final:', statusFinalData)
    console.log('')

    // 5. Verificar saldo do usuário
    console.log('5️⃣ Verificando saldo do usuário...')
    const balanceResponse = await fetch(`${BASE_URL}/api/wallet/balance?user_id=585`)
    const balanceData = await balanceResponse.json()
    console.log('💰 Saldo atualizado:', balanceData)

    console.log('\n✅ Teste concluído com sucesso!')
    console.log('🎯 O sistema PIX está funcionando corretamente!')

  } catch (error) {
    console.error('❌ Erro no teste:', error.message)
    console.log('\n🔧 Verifique se o servidor está rodando em http://localhost:3000')
  }
}

// Executar teste
testarSistemaPIX()
