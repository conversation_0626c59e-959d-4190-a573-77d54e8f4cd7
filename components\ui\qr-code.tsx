'use client'

import { useEffect, useState } from 'react'
import QRCode from 'qrcode'

interface QRCodeProps {
  value: string
  size?: number
  className?: string
}

export function QRCodeComponent({ value, size = 200, className = '' }: QRCodeProps) {
  const [qrDataUrl, setQrDataUrl] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(true)

  useEffect(() => {
    if (!value) {
      setLoading(false)
      return
    }

    console.log('🔍 Gerando QR Code para:', value.substring(0, 50) + '...')
    console.log('🔍 Valor completo do QR Code:', value)
    console.log('🔍 Validações PIX:', {
      length: value.length,
      startsCorrect: value.startsWith('00020126'),
      containsPix: value.includes('br.gov.bcb.pix'),
      containsCurrency: value.includes('5303986'),
      containsCountry: value.includes('5802BR'),
      isValidPix: value.startsWith('00020126') && value.includes('br.gov.bcb.pix')
    })

    setLoading(true)
    setError('')

    // Primeiro tentar com serviço online (mais confiável)
    try {
      const onlineUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(value)}&format=png&margin=0&color=000000&bgcolor=FFFFFF`
      console.log('🔄 Usando QR Server online:', onlineUrl)
      setQrDataUrl(onlineUrl)
      setLoading(false)
      return
    } catch (onlineErr) {
      console.error('❌ Erro no serviço online:', onlineErr)
    }

    // Fallback: Gerar QR Code usando a biblioteca qrcode
    QRCode.toDataURL(value, {
      width: size,
      margin: 0,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    })
    .then(dataUrl => {
      console.log('✅ QR Code local gerado com sucesso!')
      setQrDataUrl(dataUrl)
      setLoading(false)
    })
    .catch(err => {
      console.error('❌ Erro ao gerar QR Code local:', err)
      setError('Erro ao gerar QR Code')
      setLoading(false)

      // Último fallback: Google Charts
      try {
        const googleUrl = `https://chart.googleapis.com/chart?chs=${size}x${size}&cht=qr&chl=${encodeURIComponent(value)}&choe=UTF-8`
        setQrDataUrl(googleUrl)
        console.log('🔄 Usando Google Charts como último fallback')
      } catch (fallbackErr) {
        console.error('❌ Todos os fallbacks falharam:', fallbackErr)
      }
    })
  }, [value, size])

  if (!value) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={{ width: size, height: size }}
      >
        <span className="text-gray-500 text-sm">QR Code indisponível</span>
      </div>
    )
  }

  if (loading) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={{ width: size, height: size }}
      >
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <span className="text-gray-500 text-xs">Gerando QR Code...</span>
        </div>
      </div>
    )
  }

  if (error && !qrDataUrl) {
    return (
      <div
        className={`flex items-center justify-center bg-red-100 rounded-lg ${className}`}
        style={{ width: size, height: size }}
      >
        <div className="text-center p-2">
          <span className="text-red-500 text-xs">Erro ao gerar QR Code</span>
          <br />
          <button
            onClick={() => window.location.reload()}
            className="text-blue-500 text-xs underline mt-1"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <img
        src={qrDataUrl}
        alt="QR Code PIX"
        className=""
        style={{ width: size, height: size, maxWidth: '100%' }}
        onLoad={() => console.log('✅ QR Code carregado na tela')}
        onError={(e) => {
          console.error('❌ Erro ao carregar imagem QR Code')
          setError('Erro ao carregar QR Code')
        }}
      />
    </div>
  )
}
