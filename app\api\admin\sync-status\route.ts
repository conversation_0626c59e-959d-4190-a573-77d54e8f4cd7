import { NextRequest, NextResponse } from "next/server"
import { executeQuery } from "@/lib/db"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { bilhete_id, status } = body

    console.log("🔄 Sincronização de status do admin:", { bilhete_id, status })

    // Validações
    if (!bilhete_id) {
      return NextResponse.json({ error: "bilhete_id é obrigatório" }, { status: 400 })
    }

    if (!status) {
      return NextResponse.json({ error: "status é obrigatório" }, { status: 400 })
    }

    // Atualizar status do bilhete
    const updateResult = await executeQuery(`
      UPDATE bilhetes 
      SET status = ?, updated_at = NOW() 
      WHERE id = ? OR codigo = ?
    `, [status, bilhete_id, bilhete_id])

    console.log("✅ Status atualizado pelo admin:", updateResult)

    // Buscar bilhete atualizado
    const bilhete = await executeQuery(`
      SELECT * FROM bilhetes 
      WHERE id = ? OR codigo = ? 
      LIMIT 1
    `, [bilhete_id, bilhete_id])

    if (Array.isArray(bilhete) && bilhete.length > 0) {
      return NextResponse.json({
        success: true,
        message: "Status atualizado com sucesso",
        bilhete: bilhete[0]
      })
    } else {
      return NextResponse.json({
        success: false,
        message: "Bilhete não encontrado"
      }, { status: 404 })
    }

  } catch (error) {
    console.error("❌ Erro na sincronização de status:", error)
    
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const user_id = searchParams.get('user_id')

    if (!user_id) {
      return NextResponse.json({ error: "user_id é obrigatório" }, { status: 400 })
    }

    // Buscar bilhetes atualizados do usuário
    const bilhetes = await executeQuery(`
      SELECT id, codigo, status, valor_total, created_at, updated_at
      FROM bilhetes 
      WHERE usuario_id = ? 
      ORDER BY updated_at DESC
    `, [user_id])

    return NextResponse.json({
      success: true,
      bilhetes: bilhetes
    })

  } catch (error) {
    console.error("❌ Erro ao buscar status atualizados:", error)
    
    return NextResponse.json({
      error: "Erro interno do servidor",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
