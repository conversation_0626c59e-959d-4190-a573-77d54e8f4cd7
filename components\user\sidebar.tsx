"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  LayoutDashboard,
  Receipt,
  CreditCard,
  User,
  LogOut,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  UserPlus,
  Settings,
  Target,
} from "lucide-react"
import { toast } from "sonner"

interface SidebarProps {
  collapsed?: boolean
  onCollapsedChange?: (collapsed: boolean) => void
}

const menuItems = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    description: "Visão geral",
  },
  {
    title: "Apostar",
    href: "/",
    icon: Target,
    description: "Fazer apostas",
    external: true,
  },
  {
    title: "Meus Bilhetes",
    href: "/dashboard/bilhetes",
    icon: Receipt,
    description: "Meus bilhetes de apostas",
  },
  {
    title: "Meus Pagamentos",
    href: "/dashboard/pagamentos",
    icon: CreditCard,
    description: "Histórico de pagamentos",
  },
  {
    title: "Meu Afiliado",
    href: "/dashboard/afiliado",
    icon: UserPlus,
    description: "Painel de afiliado",
  },
  {
    title: "Meu Perfil",
    href: "/dashboard/perfil",
    icon: User,
    description: "Configurações do perfil",
  },
]

export function UserSidebar({ collapsed = false, onCollapsedChange }: SidebarProps) {
  const [mobileOpen, setMobileOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()

  const handleLogout = () => {
    localStorage.removeItem("user")
    toast.success("Logout realizado com sucesso!")
    router.push("/")
  }

  const toggleCollapsed = () => {
    onCollapsedChange?.(!collapsed)
  }

  // Obter dados do usuário
  const getUserData = () => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem("user")
      return userData ? JSON.parse(userData) : null
    }
    return null
  }

  const user = getUserData()

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden bg-white shadow-md"
        onClick={() => setMobileOpen(!mobileOpen)}
      >
        {mobileOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Mobile Overlay */}
      {mobileOpen && <div className="fixed inset-0 bg-black/50 z-40 md:hidden" onClick={() => setMobileOpen(false)} />}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 z-40 h-full bg-slate-900 border-r border-slate-800 transition-all duration-300",
          collapsed ? "w-16" : "w-64",
          mobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-800">
          {!collapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {user?.nome?.charAt(0)?.toUpperCase() || "U"}
                </span>
              </div>
              <div>
                <h1 className="text-sm font-bold text-white">{user?.nome || "Usuário"}</h1>
                <p className="text-xs text-gray-400">Minha Conta</p>
              </div>
            </div>
          )}

          {collapsed && (
            <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mx-auto">
              <span className="text-white font-bold text-sm">
                {user?.nome?.charAt(0)?.toUpperCase() || "U"}
              </span>
            </div>
          )}

          {/* Collapse Button - Desktop only */}
          <Button
            variant="ghost"
            size="icon"
            className="hidden md:flex text-gray-400 hover:text-white"
            onClick={toggleCollapsed}
          >
            {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href

              if (item.external) {
                return (
                  <a key={item.href} href={item.href} onClick={() => setMobileOpen(false)}>
                    <div
                      className={cn(
                        "flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors group",
                        "text-gray-300 hover:bg-slate-800 hover:text-white",
                      )}
                    >
                      <Icon className="h-5 w-5 flex-shrink-0" />
                      {!collapsed && (
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{item.title}</p>
                        </div>
                      )}
                      {collapsed && (
                        <div className="absolute left-16 bg-slate-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                          {item.title}
                        </div>
                      )}
                    </div>
                  </a>
                )
              }

              return (
                <Link key={item.href} href={item.href} onClick={() => setMobileOpen(false)}>
                  <div
                    className={cn(
                      "flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors group",
                      isActive ? "bg-green-600 text-white" : "text-gray-300 hover:bg-slate-800 hover:text-white",
                    )}
                  >
                    <Icon className="h-5 w-5 flex-shrink-0" />
                    {!collapsed && (
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{item.title}</p>
                      </div>
                    )}
                    {collapsed && (
                      <div className="absolute left-16 bg-slate-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                        {item.title}
                      </div>
                    )}
                  </div>
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-slate-800 space-y-2">
          <Button
            variant="ghost"
            size={collapsed ? "icon" : "sm"}
            className="w-full text-gray-300 hover:text-white hover:bg-red-600/20 group relative"
            onClick={handleLogout}
          >
            <LogOut className="h-4 w-4" />
            {!collapsed && <span className="ml-2">Sair</span>}
            {collapsed && (
              <div className="absolute left-16 bg-slate-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                Sair
              </div>
            )}
          </Button>
        </div>
      </div>
    </>
  )
}
