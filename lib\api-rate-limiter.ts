// Rate limiter para APIs externas

interface RateLimiterConfig {
  maxRequests: number
  windowMs: number
  delayMs: number
}

class APIRateLimiter {
  private requests: Map<string, number[]> = new Map()
  private config: RateLimiterConfig

  constructor(config: RateLimiterConfig) {
    this.config = config
  }

  async checkLimit(key: string): Promise<boolean> {
    const now = Date.now()
    const windowStart = now - this.config.windowMs
    
    // Obter requisições dentro da janela de tempo
    const requests = this.requests.get(key) || []
    const validRequests = requests.filter(time => time > windowStart)
    
    // Atualizar lista de requisições
    this.requests.set(key, validRequests)
    
    // Verificar se excedeu o limite
    if (validRequests.length >= this.config.maxRequests) {
      console.log(`⏳ Rate limit atingido para ${key}`)
      return false
    }
    
    // Adicionar nova requisição
    validRequests.push(now)
    this.requests.set(key, validRequests)
    
    return true
  }

  async delay(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, this.config.delayMs))
  }

  getRemainingRequests(key: string): number {
    const now = Date.now()
    const windowStart = now - this.config.windowMs
    const requests = this.requests.get(key) || []
    const validRequests = requests.filter(time => time > windowStart)
    
    return Math.max(0, this.config.maxRequests - validRequests.length)
  }

  getResetTime(key: string): number {
    const requests = this.requests.get(key) || []
    if (requests.length === 0) return 0
    
    const oldestRequest = Math.min(...requests)
    return oldestRequest + this.config.windowMs
  }
}

// Configuração para Football Data API
export const footballAPILimiter = new APIRateLimiter({
  maxRequests: 10, // 10 requisições por minuto
  windowMs: 60 * 1000, // 1 minuto
  delayMs: 1000 // 1 segundo entre requisições
})

// Configuração mais restritiva para desenvolvimento
export const devAPILimiter = new APIRateLimiter({
  maxRequests: 5, // 5 requisições por minuto
  windowMs: 60 * 1000, // 1 minuto  
  delayMs: 2000 // 2 segundos entre requisições
})

export default APIRateLimiter
