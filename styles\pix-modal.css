/* Estilos específicos para o modal PIX */
.pix-modal-overlay {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

.pix-modal-content {
  background: #1e293b;
  border: 1px solid #475569;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

.pix-qr-container {
  background: white;
  padding: 16px;
  border-radius: 16px;
  border: 4px solid #3b82f6;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.pix-close-button {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.pix-close-button:hover {
  background: #f3f4f6;
}

.pix-loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #10b981;
  border-top: 4px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.pix-code-container {
  background: #374151;
  padding: 16px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #d1d5db;
  word-break: break-all;
  line-height: 1.5;
}

.pix-copy-button {
  background: #2563eb;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  width: 100%;
  margin-top: 8px;
  font-size: 14px;
}

.pix-copy-button:hover {
  background: #1d4ed8;
}

.pix-status-indicator {
  background: rgba(251, 191, 36, 0.2);
  border: 1px solid rgba(251, 191, 36, 0.5);
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.pix-status-dot {
  width: 8px;
  height: 8px;
  background: #fbbf24;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.pix-status-text {
  color: #fbbf24;
  font-size: 14px;
  font-weight: 500;
}
