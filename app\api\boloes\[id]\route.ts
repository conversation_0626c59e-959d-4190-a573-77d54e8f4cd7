import { NextRequest, NextResponse } from 'next/server'
import { executeQuery, executeQuery<PERSON>ingle } from '@/lib/database-config'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const bolaoId = parseInt(params.id)
    
    if (isNaN(bolaoId)) {
      return NextResponse.json(
        { error: 'ID do bolão inválido' },
        { status: 400 }
      )
    }

    // Buscar dados do bolão
    const bolao = await executeQuerySingle(`
      SELECT 
        b.*,
        u.nome as criador_nome,
        (SELECT COUNT(*) FROM apostas WHERE bolao_id = b.id AND status = 'paga') as participantes
      FROM boloes b
      LEFT JOIN usuarios u ON b.criado_por = u.id
      WHERE b.id = ?
    `, [bolaoId])

    if (!bolao) {
      return NextResponse.json(
        { error: 'Bolão não encontrado' },
        { status: 404 }
      )
    }

    // Buscar jogos do bolão
    const jogos = await executeQuery(`
      SELECT 
        j.*,
        tc.nome as time_casa,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        c.nome as campeonato
      FROM bolao_jogos bj
      JOIN jogos j ON bj.jogo_id = j.id
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE bj.bolao_id = ?
      ORDER BY j.data_jogo ASC
    `, [bolaoId])

    // Buscar campeonatos do bolão
    const campeonatos = await executeQuery(`
      SELECT DISTINCT c.nome
      FROM bolao_jogos bj
      JOIN jogos j ON bj.jogo_id = j.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE bj.bolao_id = ?
    `, [bolaoId])

    // Formatar dados para o frontend
    const bolaoFormatted = {
      id: bolao.id,
      nome: bolao.nome,
      descricao: bolao.descricao,
      valor_aposta: parseFloat(bolao.valor_aposta),
      premio_total: parseFloat(bolao.premio_total),
      data_inicio: bolao.data_inicio,
      data_fim: bolao.data_fim,
      status: bolao.status,
      participantes: bolao.participantes || 0,
      max_participantes: bolao.max_participantes,
      campeonatos: campeonatos.map((c: any) => c.nome),
      regras: bolao.regras ? JSON.parse(bolao.regras) : [
        "Acerte pelo menos 3 resultados para ganhar prêmios",
        "3 acertos: 10% do prêmio total",
        "4 acertos: 25% do prêmio total", 
        "5 acertos: 65% do prêmio total",
        "Apostas devem ser feitas até 1 hora antes do primeiro jogo",
        "Resultados são baseados no tempo regulamentar (90 minutos)"
      ],
      jogos: jogos.map((jogo: any) => ({
        id: jogo.id,
        time_casa: jogo.time_casa,
        time_fora: jogo.time_fora,
        data_jogo: jogo.data_jogo,
        campeonato: jogo.campeonato,
        resultado_casa: jogo.resultado_casa,
        resultado_fora: jogo.resultado_fora,
        status: jogo.status,
        time_casa_logo: jogo.time_casa_logo,
        time_fora_logo: jogo.time_fora_logo
      }))
    }

    return NextResponse.json(bolaoFormatted)

  } catch (error) {
    console.error('❌ Erro ao buscar bolão:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
