#!/usr/bin/env node

/**
 * Sistema de agendamento automático para sincronização
 * Executa a sincronização a cada 2 dias automaticamente
 */

import { config } from 'dotenv'
import { syncAll } from './sync-football-data.js'
import { initializeDatabase, executeQuery, executeQuerySingle } from '../lib/database.js'

// Carregar variáveis de ambiente
config({ path: '.env.local' })

// Configurações do scheduler
const SCHEDULER_CONFIG = {
  intervalDays: 3, // A cada 3 dias (reduzido para economizar API calls)
  intervalMs: 3 * 24 * 60 * 60 * 1000, // 3 dias em millisegundos
  maxRetries: 3,
  retryDelayMs: 30 * 60 * 1000, // 30 minutos entre tentativas
  minIntervalMs: 3 * 24 * 60 * 60 * 1000, // Mínimo de 3 dias entre sincronizações
}

class FootballScheduler {
  constructor() {
    this.isRunning = false
    this.nextRun = null
    this.intervalId = null
  }

  async init() {
    try {
      await initializeDatabase()
      
      // Criar tabela de agendamento se não existir
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS scheduler_status (
          id INTEGER PRIMARY KEY,
          last_run DATETIME,
          next_run DATETIME,
          status TEXT DEFAULT 'idle',
          error_count INTEGER DEFAULT 0,
          last_error TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // Inserir registro inicial se não existir
      const existing = await executeQuerySingle('SELECT id FROM scheduler_status WHERE id = 1')
      if (!existing) {
        await executeQuery(`
          INSERT INTO scheduler_status (id, status, next_run)
          VALUES (1, 'idle', DATE_ADD(NOW(), INTERVAL ${SCHEDULER_CONFIG.intervalDays} DAY))
        `)
      }

      console.log('✅ Scheduler inicializado com sucesso')
      return true
    } catch (error) {
      console.error('❌ Erro ao inicializar scheduler:', error)
      throw error
    }
  }

  async getStatus() {
    try {
      const status = await executeQuerySingle('SELECT * FROM scheduler_status WHERE id = 1')
      return status || null
    } catch (error) {
      console.error('❌ Erro ao buscar status:', error)
      return null
    }
  }

  async updateStatus(data) {
    try {
      const updates = Object.keys(data).map(key => `${key} = ?`).join(', ')
      const values = Object.values(data)
      values.push(1) // WHERE id = 1

      await executeQuery(`
        UPDATE scheduler_status 
        SET ${updates}, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, values)
    } catch (error) {
      console.error('❌ Erro ao atualizar status:', error)
    }
  }

  async shouldRun() {
    try {
      const status = await this.getStatus()
      if (!status) return true

      const now = new Date()
      const nextRun = new Date(status.next_run)

      return now >= nextRun && status.status !== 'running'
    } catch (error) {
      console.error('❌ Erro ao verificar se deve executar:', error)
      return false
    }
  }

  async canSyncNow() {
    try {
      const status = await this.getStatus()
      if (!status || !status.last_run) return true

      const now = new Date()
      const lastRun = new Date(status.last_run)
      const timeSinceLastRun = now.getTime() - lastRun.getTime()

      return timeSinceLastRun >= SCHEDULER_CONFIG.minIntervalMs
    } catch (error) {
      console.error('❌ Erro ao verificar throttling:', error)
      return false
    }
  }

  async runSync() {
    if (this.isRunning) {
      console.log('⚠️ Sincronização já está em execução')
      return false
    }

    try {
      this.isRunning = true
      console.log('🚀 Iniciando sincronização agendada...')
      
      await this.updateStatus({
        status: 'running',
        last_run: new Date().toISOString()
      })

      // Executar sincronização
      const result = await syncAll()

      // Calcular próxima execução
      const nextRun = new Date()
      nextRun.setTime(nextRun.getTime() + SCHEDULER_CONFIG.intervalMs)

      await this.updateStatus({
        status: 'idle',
        next_run: nextRun.toISOString(),
        error_count: 0,
        last_error: null
      })

      console.log('✅ Sincronização agendada concluída com sucesso')
      console.log(`📅 Próxima execução: ${nextRun.toLocaleString('pt-BR')}`)
      
      return true

    } catch (error) {
      console.error('❌ Erro na sincronização agendada:', error)
      
      const status = await this.getStatus()
      const errorCount = (status?.error_count || 0) + 1
      
      // Se ainda há tentativas, reagendar para breve
      let nextRun = new Date()
      if (errorCount < SCHEDULER_CONFIG.maxRetries) {
        nextRun.setTime(nextRun.getTime() + SCHEDULER_CONFIG.retryDelayMs)
        console.log(`⚠️ Tentativa ${errorCount}/${SCHEDULER_CONFIG.maxRetries} falhou. Reagendando para ${nextRun.toLocaleString('pt-BR')}`)
      } else {
        // Máximo de tentativas atingido, aguardar próximo ciclo normal
        nextRun.setTime(nextRun.getTime() + SCHEDULER_CONFIG.intervalMs)
        console.log(`❌ Máximo de tentativas atingido. Próxima tentativa: ${nextRun.toLocaleString('pt-BR')}`)
      }

      await this.updateStatus({
        status: 'error',
        next_run: nextRun.toISOString(),
        error_count: errorCount,
        last_error: error.message
      })

      return false
    } finally {
      this.isRunning = false
    }
  }

  async start() {
    try {
      await this.init()
      
      console.log('🕐 Scheduler iniciado - verificando a cada 1 hora')
      console.log(`📋 Configuração: sincronização a cada ${SCHEDULER_CONFIG.intervalDays} dias`)
      
      const status = await this.getStatus()
      if (status?.next_run) {
        console.log(`📅 Próxima execução agendada: ${new Date(status.next_run).toLocaleString('pt-BR')}`)
      }

      // Verificar imediatamente se deve executar
      if (await this.shouldRun()) {
        console.log('🔄 Executando sincronização inicial...')
        await this.runSync()
      }

      // Configurar verificação a cada hora
      this.intervalId = setInterval(async () => {
        try {
          if (await this.shouldRun()) {
            await this.runSync()
          }
        } catch (error) {
          console.error('❌ Erro na verificação do scheduler:', error)
        }
      }, 60 * 60 * 1000) // 1 hora

      console.log('✅ Scheduler em execução')
      
    } catch (error) {
      console.error('❌ Erro ao iniciar scheduler:', error)
      throw error
    }
  }

  async stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    
    await this.updateStatus({ status: 'stopped' })
    console.log('🛑 Scheduler parado')
  }

  async getNextRun() {
    const status = await this.getStatus()
    return status?.next_run ? new Date(status.next_run) : null
  }

  async forceRun() {
    console.log('🔧 Forçando execução da sincronização...')
    return await this.runSync()
  }
}

// Função para executar uma vez
async function runOnce() {
  try {
    console.log('🔄 Executando sincronização única...')
    const scheduler = new FootballScheduler()
    await scheduler.init()
    const result = await scheduler.runSync()
    
    if (result) {
      console.log('✅ Sincronização única concluída com sucesso')
      process.exit(0)
    } else {
      console.log('❌ Sincronização única falhou')
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Erro na execução única:', error)
    process.exit(1)
  }
}

// Função para executar o scheduler contínuo
async function runScheduler() {
  try {
    const scheduler = new FootballScheduler()
    await scheduler.start()
    
    // Manter o processo vivo
    process.on('SIGINT', async () => {
      console.log('\n🛑 Recebido sinal de parada...')
      await scheduler.stop()
      process.exit(0)
    })
    
    process.on('SIGTERM', async () => {
      console.log('\n🛑 Recebido sinal de término...')
      await scheduler.stop()
      process.exit(0)
    })
    
  } catch (error) {
    console.error('❌ Erro no scheduler:', error)
    process.exit(1)
  }
}

// Verificar argumentos da linha de comando
const args = process.argv.slice(2)

if (args.includes('--once') || args.includes('-o')) {
  runOnce()
} else if (args.includes('--help') || args.includes('-h')) {
  console.log(`
🕐 Football Data Scheduler

Uso:
  node scheduler.js              # Executar scheduler contínuo
  node scheduler.js --once       # Executar sincronização uma vez
  node scheduler.js --help       # Mostrar esta ajuda

Configuração:
  - Sincronização a cada ${SCHEDULER_CONFIG.intervalDays} dias
  - Verificação a cada 1 hora
  - Máximo de ${SCHEDULER_CONFIG.maxRetries} tentativas em caso de erro
  `)
} else {
  runScheduler()
}

export { FootballScheduler, runOnce, runScheduler }
