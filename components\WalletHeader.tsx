'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Clock, Wallet, Plus, Minus, User, LogOut } from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface WalletHeaderProps {
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  onSearchClick?: () => void
  onLogout?: () => void
  onLoginClick?: () => void
  onRegisterClick?: () => void
  onDepositClick?: () => void
  onWithdrawClick?: () => void
}

export function WalletHeader({ usuario, onSaldoUpdate, onSearchClick, onLogout, onLoginClick, onRegisterClick, onDepositClick, onWithdrawClick }: WalletHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showDepositModal, setShowDepositModal] = useState(false)
  const [showWithdrawModal, setShowWithdrawModal] = useState(false)
  const [depositAmount, setDepositAmount] = useState('')
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatDateTime = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getInitials = (nome: string) => {
    return nome
      .split(' ')
      .map(n => n.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  const handleDeposit = async () => {
    if (!depositAmount || parseFloat(depositAmount) <= 0) {
      toast.error('Digite um valor válido para depósito')
      return
    }

    setIsProcessing(true)
    try {
      // Aqui você pode chamar a API de depósito
      if (onDepositClick) {
        onDepositClick()
      }

      // Simular processamento
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast.success(`Depósito de ${formatCurrency(parseFloat(depositAmount))} solicitado com sucesso!`)
      setDepositAmount('')
      setShowDepositModal(false)

      // Atualizar saldo se callback fornecido
      if (onSaldoUpdate && usuario) {
        onSaldoUpdate(usuario.saldo + parseFloat(depositAmount))
      }
    } catch (error) {
      toast.error('Erro ao processar depósito')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleWithdraw = async () => {
    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      toast.error('Digite um valor válido para saque')
      return
    }

    if (usuario && parseFloat(withdrawAmount) > usuario.saldo) {
      toast.error('Saldo insuficiente')
      return
    }

    setIsProcessing(true)
    try {
      // Aqui você pode chamar a API de saque
      if (onWithdrawClick) {
        onWithdrawClick()
      }

      // Simular processamento
      await new Promise(resolve => setTimeout(resolve, 1000))

      toast.success(`Saque de ${formatCurrency(parseFloat(withdrawAmount))} solicitado com sucesso!`)
      setWithdrawAmount('')
      setShowWithdrawModal(false)

      // Atualizar saldo se callback fornecido
      if (onSaldoUpdate && usuario) {
        onSaldoUpdate(usuario.saldo - parseFloat(withdrawAmount))
      }
    } catch (error) {
      toast.error('Erro ao processar saque')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <header className="wallet-header border-b border-slate-700 shadow-lg sticky top-0 z-40 overflow-visible bg-gray-900">
      <div className="w-full max-w-none mx-auto">
        {/* Desktop Layout */}
        <div className="hidden lg:flex items-center justify-between p-4 gap-4">
          {/* Left Section - Data e Hora */}
          <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
            <Clock className="h-4 w-4 text-yellow-400" />
            <div className="text-white">
              <div className="text-sm font-medium">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-400">
                UTC-03:00
              </div>
            </div>
          </div>

          {/* Right Section - User Actions */}
          <div className="flex items-center space-x-3">
            {usuario ? (
              <>
                {/* Wallet Section */}
                <div className="flex items-center space-x-3 bg-green-800 rounded-lg px-4 py-2">
                  {/* Deposit Button */}
                  <Dialog open={showDepositModal} onOpenChange={setShowDepositModal}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white border-green-600 flex items-center gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        Depósito
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                      <DialogHeader>
                        <DialogTitle>Fazer Depósito</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="deposit-amount">Valor do Depósito</Label>
                          <Input
                            id="deposit-amount"
                            type="number"
                            placeholder="0,00"
                            value={depositAmount}
                            onChange={(e) => setDepositAmount(e.target.value)}
                            min="0"
                            step="0.01"
                          />
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={handleDeposit}
                            disabled={isProcessing}
                            className="flex-1"
                          >
                            {isProcessing ? 'Processando...' : 'Confirmar Depósito'}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowDepositModal(false)}
                            disabled={isProcessing}
                          >
                            Cancelar
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* User Profile with Avatar */}
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                      {getInitials(usuario.nome)}
                    </div>
                    <div className="text-white">
                      <div className="text-sm font-medium">{usuario.nome}</div>
                      <div className="text-xs text-green-400">
                        ID: {usuario.id}
                      </div>
                    </div>
                  </div>

                  {/* Withdraw Button */}
                  <Dialog open={showWithdrawModal} onOpenChange={setShowWithdrawModal}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-orange-600 hover:bg-orange-700 text-white border-orange-600 flex items-center gap-2"
                      >
                        <Minus className="h-4 w-4" />
                        Sacar
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                      <DialogHeader>
                        <DialogTitle>Fazer Saque</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="withdraw-amount">Valor do Saque</Label>
                          <Input
                            id="withdraw-amount"
                            type="number"
                            placeholder="0,00"
                            value={withdrawAmount}
                            onChange={(e) => setWithdrawAmount(e.target.value)}
                            min="0"
                            max={usuario.saldo}
                            step="0.01"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Saldo disponível: {formatCurrency(usuario.saldo)}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={handleWithdraw}
                            disabled={isProcessing}
                            className="flex-1"
                          >
                            {isProcessing ? 'Processando...' : 'Confirmar Saque'}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowWithdrawModal(false)}
                            disabled={isProcessing}
                          >
                            Cancelar
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                {/* Logout Button */}
                <Button
                  onClick={onLogout}
                  variant="outline"
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white border-red-600 flex items-center gap-2"
                >
                  <LogOut className="h-4 w-4" />
                  Sair
                </Button>
              </>
            ) : (
              <>
                {/* Login Button */}
                <Button
                  onClick={onLoginClick}
                  variant="outline"
                  size="sm"
                  className="bg-yellow-500 hover:bg-yellow-600 text-black border-yellow-500 font-medium"
                >
                  Login
                </Button>
                
                {/* Register Button */}
                <Button
                  onClick={onRegisterClick}
                  variant="outline"
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white border-green-600 font-medium"
                >
                  Registro
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden flex items-center justify-between p-3 gap-3">
          {/* Left Section - Data e Hora (Mobile) */}
          <div className="flex items-center space-x-2 min-w-0 flex-shrink-0">
            <Clock className="h-3 w-3 text-yellow-400" />
            <div className="text-white">
              <div className="text-xs font-medium">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-400">
                UTC-03:00
              </div>
            </div>
          </div>

          {/* Right Section - User Actions (Mobile) */}
          <div className="flex items-center space-x-2">
            {usuario ? (
              <>
                {/* Mobile Wallet Section */}
                <div className="flex items-center space-x-2 bg-green-800 rounded-lg px-2 py-1">
                  {/* Deposit Button (Mobile) */}
                  <Dialog open={showDepositModal} onOpenChange={setShowDepositModal}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white border-green-600 text-xs px-2 py-1"
                      >
                        <Plus className="h-3 w-3" />
                      </Button>
                    </DialogTrigger>
                  </Dialog>

                  {/* User Profile (Mobile) */}
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
                      {getInitials(usuario.nome)}
                    </div>
                    <div className="text-white">
                      <div className="text-xs font-medium">{usuario.nome}</div>
                      <div className="text-xs text-green-400">
                        ID: {usuario.id}
                      </div>
                    </div>
                  </div>

                  {/* Withdraw Button (Mobile) */}
                  <Dialog open={showWithdrawModal} onOpenChange={setShowWithdrawModal}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-orange-600 hover:bg-orange-700 text-white border-orange-600 text-xs px-2 py-1"
                      >
                        <Minus className="h-3 w-3" />
                      </Button>
                    </DialogTrigger>
                  </Dialog>
                </div>

                {/* Logout Button (Mobile) */}
                <Button
                  onClick={onLogout}
                  variant="outline"
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white border-red-600 text-xs px-2 py-1"
                >
                  <LogOut className="h-3 w-3" />
                </Button>
              </>
            ) : (
              <>
                {/* Login Button (Mobile) */}
                <Button
                  onClick={onLoginClick}
                  variant="outline"
                  size="sm"
                  className="bg-yellow-500 hover:bg-yellow-600 text-black border-yellow-500 font-medium text-xs px-2 py-1"
                >
                  Login
                </Button>
                
                {/* Register Button (Mobile) */}
                <Button
                  onClick={onRegisterClick}
                  variant="outline"
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white border-green-600 font-medium text-xs px-2 py-1"
                >
                  Registro
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

// Componente de Status Badge para diferentes estados
export function StatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pago':
        return { color: 'bg-green-500', text: 'Pago' }
      case 'pendente':
        return { color: 'bg-yellow-500', text: 'Pendente' }
      case 'expirado':
        return { color: 'bg-red-500', text: 'Expirado' }
      case 'cancelado':
        return { color: 'bg-gray-500', text: 'Cancelado' }
      default:
        return { color: 'bg-gray-500', text: status }
    }
  }

  const config = getStatusConfig(status)

  return (
    <div className={`${config.color} text-white text-xs px-2 py-1 rounded`}>
      {config.text}
    </div>
  )
}
