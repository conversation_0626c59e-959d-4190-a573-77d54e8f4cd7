'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Clock } from 'lucide-react'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface WalletHeaderProps {
  usuario?: Usuario | null
  onSaldoUpdate?: (novoSaldo: number) => void
  onSearchClick?: () => void
  onLogout?: () => void
  onLoginClick?: () => void
  onRegisterClick?: () => void
}

export function WalletHeader({ usuario, onSaldoUpdate, onSearchClick, onLogout, onLoginClick, onRegisterClick }: WalletHeaderProps) {
  const [currentTime, setCurrentTime] = useState(new Date())

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatDateTime = (date: Date) => {
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    const seconds = date.getSeconds().toString().padStart(2, '0')
    
    return `${day}/${month}/${year}, ${hours}:${minutes}:${seconds}`
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  return (
    <header className="wallet-header border-b border-slate-700 shadow-lg sticky top-0 z-40 overflow-visible bg-gray-900">
      <div className="w-full max-w-none mx-auto">
        {/* Desktop Layout */}
        <div className="hidden lg:flex items-center justify-between p-4 gap-4">
          {/* Left Section - Data e Hora */}
          <div className="flex items-center space-x-3 min-w-0 flex-shrink-0">
            <Clock className="h-4 w-4 text-yellow-400" />
            <div className="text-white">
              <div className="text-sm font-medium">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-400">
                UTC-03:00
              </div>
            </div>
          </div>

          {/* Right Section - User Actions */}
          <div className="flex items-center space-x-3">
            {usuario ? (
              <>
                {/* User Info */}
                <div className="text-white text-right">
                  <div className="text-sm font-medium">{usuario.nome}</div>
                  <div className="text-xs text-green-400">
                    {formatCurrency(usuario.saldo)}
                  </div>
                </div>
                
                {/* Logout Button */}
                <Button
                  onClick={onLogout}
                  variant="outline"
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white border-red-600"
                >
                  Sair
                </Button>
              </>
            ) : (
              <>
                {/* Login Button */}
                <Button
                  onClick={onLoginClick}
                  variant="outline"
                  size="sm"
                  className="bg-yellow-500 hover:bg-yellow-600 text-black border-yellow-500 font-medium"
                >
                  Login
                </Button>
                
                {/* Register Button */}
                <Button
                  onClick={onRegisterClick}
                  variant="outline"
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white border-green-600 font-medium"
                >
                  Registro
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="lg:hidden flex items-center justify-between p-3 gap-3">
          {/* Left Section - Data e Hora (Mobile) */}
          <div className="flex items-center space-x-2 min-w-0 flex-shrink-0">
            <Clock className="h-3 w-3 text-yellow-400" />
            <div className="text-white">
              <div className="text-xs font-medium">
                {formatDateTime(currentTime)}
              </div>
              <div className="text-xs text-gray-400">
                UTC-03:00
              </div>
            </div>
          </div>

          {/* Right Section - User Actions (Mobile) */}
          <div className="flex items-center space-x-2">
            {usuario ? (
              <>
                {/* User Info (Mobile) */}
                <div className="text-white text-right">
                  <div className="text-xs font-medium">{usuario.nome}</div>
                  <div className="text-xs text-green-400">
                    {formatCurrency(usuario.saldo)}
                  </div>
                </div>
                
                {/* Logout Button (Mobile) */}
                <Button
                  onClick={onLogout}
                  variant="outline"
                  size="sm"
                  className="bg-red-600 hover:bg-red-700 text-white border-red-600 text-xs px-2 py-1"
                >
                  Sair
                </Button>
              </>
            ) : (
              <>
                {/* Login Button (Mobile) */}
                <Button
                  onClick={onLoginClick}
                  variant="outline"
                  size="sm"
                  className="bg-yellow-500 hover:bg-yellow-600 text-black border-yellow-500 font-medium text-xs px-2 py-1"
                >
                  Login
                </Button>
                
                {/* Register Button (Mobile) */}
                <Button
                  onClick={onRegisterClick}
                  variant="outline"
                  size="sm"
                  className="bg-green-600 hover:bg-green-700 text-white border-green-600 font-medium text-xs px-2 py-1"
                >
                  Registro
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

// Componente de Status Badge para diferentes estados
export function StatusBadge({ status }: { status: string }) {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pago':
        return { color: 'bg-green-500', text: 'Pago' }
      case 'pendente':
        return { color: 'bg-yellow-500', text: 'Pendente' }
      case 'expirado':
        return { color: 'bg-red-500', text: 'Expirado' }
      case 'cancelado':
        return { color: 'bg-gray-500', text: 'Cancelado' }
      default:
        return { color: 'bg-gray-500', text: status }
    }
  }

  const config = getStatusConfig(status)

  return (
    <div className={`${config.color} text-white text-xs px-2 py-1 rounded`}>
      {config.text}
    </div>
  )
}
