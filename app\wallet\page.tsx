'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { WalletHeader, StatusBadge } from '@/components/WalletHeader'
import {
  Wallet,
  Plus,
  Minus,
  CreditCard,
  History,
  DollarSign,
  ArrowUpCircle,
  ArrowDownCircle,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Transacao {
  id: number
  tipo: string
  valor: number
  saldoAnterior: number
  saldoPosterior: number
  descricao: string
  bilheteId?: number
  transactionId?: string
  status: string
  data: string
}

interface Deposito {
  id: number
  valor: number
  transaction_id: string
  status: string
  data: string
  expiracao?: string
}

export default function WalletPage() {
  const [usuario, setUsuario] = useState<Usuario | null>(null)
  const [transaco<PERSON>, setTransacoes] = useState<Transacao[]>([])
  const [depositos, setDepositos] = useState<Deposito[]>([])
  const [loading, setLoading] = useState(true)
  const [depositoLoading, setDepositoLoading] = useState(false)
  const [valorDeposito, setValorDeposito] = useState('')
  const [qrCodePix, setQrCodePix] = useState<string | null>(null)
  const [showDepositModal, setShowDepositModal] = useState(false)

  // Para teste, vamos usar usuário ID 1
  const userId = 1

  useEffect(() => {
    carregarDados()
  }, [])

  const carregarDados = async () => {
    try {
      setLoading(true)
      
      // Carregar saldo e transações
      const balanceResponse = await fetch(`/api/wallet/balance?user_id=${userId}`)
      if (balanceResponse.ok) {
        const balanceData = await balanceResponse.json()
        if (balanceData.success) {
          setUsuario(balanceData.usuario)
          setTransacoes(balanceData.transacoes)
        }
      }

      // Carregar depósitos
      const depositosResponse = await fetch(`/api/wallet/deposit?user_id=${userId}`)
      if (depositosResponse.ok) {
        const depositosData = await depositosResponse.json()
        if (depositosData.success) {
          setDepositos(depositosData.depositos)
        }
      }

    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      toast.error('Erro ao carregar dados da carteira')
    } finally {
      setLoading(false)
    }
  }

  const criarDeposito = async () => {
    if (!valorDeposito || parseFloat(valorDeposito) <= 0) {
      toast.error('Digite um valor válido para depósito')
      return
    }

    try {
      setDepositoLoading(true)

      const response = await fetch('/api/wallet/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: userId,
          valor: parseFloat(valorDeposito),
          client_name: usuario?.nome || 'Usuário Teste',
          client_email: usuario?.email || '<EMAIL>',
          client_document: '12345678901'
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Depósito PIX criado com sucesso!')
        setQrCodePix(data.deposito.qrcode_image)
        setValorDeposito('')
        carregarDados()
      } else {
        toast.error(data.error || 'Erro ao criar depósito')
      }

    } catch (error) {
      console.error('Erro ao criar depósito:', error)
      toast.error('Erro ao criar depósito PIX')
    } finally {
      setDepositoLoading(false)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getTransactionIcon = (tipo: string) => {
    switch (tipo) {
      case 'deposito':
        return <ArrowUpCircle className="h-4 w-4 text-green-600" />
      case 'compra_bilhete':
        return <ArrowDownCircle className="h-4 w-4 text-red-600" />
      case 'premio':
        return <ArrowUpCircle className="h-4 w-4 text-yellow-600" />
      case 'estorno':
        return <ArrowUpCircle className="h-4 w-4 text-blue-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pago':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'pendente':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'expirado':
      case 'cancelado':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando carteira...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header com padrão do sistema */}
      <WalletHeader
        usuario={usuario}
        onSearchClick={() => toast.info('Função de busca em desenvolvimento')}
      />

      <div className="space-y-6 p-6">
        {/* Título da Página */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Wallet className="h-8 w-8 mr-3 text-blue-600" />
              Carteira Digital
            </h1>
            <p className="text-gray-600 mt-2">Gerencie seu saldo e histórico de transações</p>
          </div>
        </div>

        {/* Modal de Depósito */}
        {showDepositModal && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Plus className="h-5 w-5 mr-2 text-green-600" />
                  Adicionar Saldo
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDepositModal(false)}
                >
                  ✕
                </Button>
              </CardTitle>
              <CardDescription>
                Faça um depósito via PIX para adicionar saldo à sua carteira
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-4">
                <Input
                  type="number"
                  placeholder="Valor do depósito (R$)"
                  value={valorDeposito}
                  onChange={(e) => setValorDeposito(e.target.value)}
                  min="0.01"
                  step="0.01"
                />
                <Button
                  onClick={criarDeposito}
                  disabled={depositoLoading}
                  className="flex items-center space-x-2"
                >
                  <CreditCard className="h-4 w-4" />
                  <span>{depositoLoading ? 'Gerando...' : 'Gerar PIX'}</span>
                </Button>
              </div>

              {qrCodePix && (
                <div className="mt-4 p-4 bg-gray-50 rounded-lg text-center">
                  <p className="text-sm text-gray-600 mb-2">QR Code PIX gerado:</p>
                  <img
                    src={qrCodePix}
                    alt="QR Code PIX"
                    className="mx-auto max-w-xs"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Escaneie o código ou copie o código PIX para fazer o pagamento
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

      {/* Histórico de Transações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <History className="h-5 w-5 mr-2 text-gray-600" />
            Histórico de Transações
          </CardTitle>
        </CardHeader>
        <CardContent>
          {transacoes.length === 0 ? (
            <p className="text-gray-500 text-center py-4">Nenhuma transação encontrada</p>
          ) : (
            <div className="space-y-3">
              {transacoes.map((transacao) => (
                <div key={transacao.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transacao.tipo)}
                    <div>
                      <p className="font-medium text-gray-900">{transacao.descricao}</p>
                      <p className="text-sm text-gray-500">{transacao.data}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-bold ${
                      transacao.tipo === 'compra_bilhete' ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {transacao.tipo === 'compra_bilhete' ? '-' : '+'}
                      {formatCurrency(transacao.valor)}
                    </p>
                    <p className="text-xs text-gray-500">
                      Saldo: {formatCurrency(transacao.saldoPosterior)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Histórico de Depósitos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2 text-blue-600" />
            Depósitos PIX
          </CardTitle>
        </CardHeader>
        <CardContent>
          {depositos.length === 0 ? (
            <p className="text-gray-500 text-center py-4">Nenhum depósito encontrado</p>
          ) : (
            <div className="space-y-3">
              {depositos.map((deposito) => (
                <div key={deposito.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(deposito.status)}
                    <div>
                      <p className="font-medium text-gray-900">
                        Depósito PIX - {formatCurrency(deposito.valor)}
                      </p>
                      <p className="text-sm text-gray-500">{deposito.data}</p>
                      <p className="text-xs text-gray-400">ID: {deposito.transaction_id}</p>
                    </div>
                  </div>
                  <StatusBadge status={deposito.status} />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </div>
  )
}
