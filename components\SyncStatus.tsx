'use client'

import { useState, useEffect } from 'react'

interface ThrottleStatus {
  can_sync: boolean
  hours_remaining: number
  next_available: string | null
  last_sync: string | null
}

interface SyncStatusProps {
  onSyncClick?: () => void
  className?: string
}

export default function SyncStatus({ onSyncClick, className = '' }: SyncStatusProps) {
  const [throttleStatus, setThrottleStatus] = useState<ThrottleStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [syncing, setSyncing] = useState(false)

  const checkThrottleStatus = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'check_throttle' })
      })
      
      const data = await response.json()
      if (data.success) {
        setThrottleStatus(data.throttle)
      }
    } catch (error) {
      console.error('Erro ao verificar status:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSync = async () => {
    if (!throttleStatus?.can_sync) return
    
    try {
      setSyncing(true)
      const response = await fetch('/api/admin/scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'force_sync' })
      })
      
      const data = await response.json()
      
      if (data.success) {
        alert('Sincronização iniciada! Os dados serão atualizados em background.')
        // Recheck status after sync
        setTimeout(checkThrottleStatus, 2000)
      } else {
        alert(`Erro: ${data.message || data.error}`)
      }
      
      if (onSyncClick) onSyncClick()
    } catch (error) {
      console.error('Erro ao sincronizar:', error)
      alert('Erro ao iniciar sincronização')
    } finally {
      setSyncing(false)
    }
  }

  useEffect(() => {
    checkThrottleStatus()
    // Check status every 5 minutes
    const interval = setInterval(checkThrottleStatus, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  if (loading && !throttleStatus) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
        <span className="text-sm text-gray-600">Verificando status...</span>
      </div>
    )
  }

  const formatNextAvailable = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatLastSync = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffHours < 1) return 'Há menos de 1 hora'
    if (diffHours < 24) return `Há ${diffHours} horas`
    
    const diffDays = Math.floor(diffHours / 24)
    return `Há ${diffDays} dias`
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Status Card */}
      <div className="bg-white rounded-lg border p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`w-3 h-3 rounded-full ${
              throttleStatus?.can_sync ? 'bg-green-500' : 'bg-yellow-500'
            }`}></div>
            <div>
              <h3 className="font-medium text-gray-900">
                Status de Sincronização
              </h3>
              <p className="text-sm text-gray-600">
                {throttleStatus?.can_sync 
                  ? 'Pronto para sincronizar' 
                  : `Aguarde ${throttleStatus?.hours_remaining}h para próxima sincronização`
                }
              </p>
            </div>
          </div>
          
          <button
            onClick={handleSync}
            disabled={!throttleStatus?.can_sync || syncing}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              throttleStatus?.can_sync && !syncing
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {syncing ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Sincronizando...
              </div>
            ) : (
              'Sincronizar Agora'
            )}
          </button>
        </div>
      </div>

      {/* Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        {throttleStatus?.last_sync && (
          <div className="bg-gray-50 rounded-lg p-3">
            <span className="font-medium text-gray-700">Última Sincronização:</span>
            <p className="text-gray-600 mt-1">
              {formatLastSync(throttleStatus.last_sync)}
            </p>
          </div>
        )}
        
        {!throttleStatus?.can_sync && throttleStatus?.next_available && (
          <div className="bg-yellow-50 rounded-lg p-3">
            <span className="font-medium text-yellow-800">Próxima Disponível:</span>
            <p className="text-yellow-700 mt-1">
              {formatNextAvailable(throttleStatus.next_available)}
            </p>
          </div>
        )}
      </div>

      {/* Info */}
      <div className="bg-blue-50 rounded-lg p-3">
        <div className="flex items-start gap-2">
          <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
            <span className="text-blue-600 text-xs">ℹ</span>
          </div>
          <div className="text-sm">
            <p className="text-blue-800 font-medium">Limite de Sincronização</p>
            <p className="text-blue-700 mt-1">
              Para economizar recursos da API, a sincronização é limitada a uma vez a cada 3 dias.
              Isso garante que sempre tenhamos dados atualizados sem exceder os limites da API externa.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
