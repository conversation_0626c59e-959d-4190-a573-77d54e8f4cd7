"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { BarChart3, Download, TrendingUp, Users, DollarSign, Calendar, Loader2 } from "lucide-react"
import { toast } from "sonner"

export default function RelatoriosPage() {
  const [loading, setLoading] = useState(false)
  const [relatorioData, setRelatorioData] = useState(null)
  const [tipoRelatorio, setTipoRelatorio] = useState("vendas")
  const [periodo, setPeriodo] = useState("mes")

  const fetchRelatorio = async (tipo: string, periodoSelecionado: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/relatorios?tipo=${tipo}&periodo=${periodoSelecionado}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        setRelatorioData(data.data)
        toast.success("Relatório gerado com sucesso!")
      } else {
        throw new Error(data.message || "Erro ao gerar relatório")
      }
    } catch (error) {
      console.error("Erro ao buscar relatório:", error)
      toast.error("Erro ao gerar relatório")
    } finally {
      setLoading(false)
    }
  }

  const handleDownloadRelatorio = async (tipo: string) => {
    await fetchRelatorio(tipo, periodo)
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value || 0)
  }
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Relatórios</h1>
          <p className="text-gray-600 mt-2">Analytics e relatórios detalhados do sistema</p>
        </div>
        <div className="flex gap-4">
          <Select value={periodo} onValueChange={setPeriodo}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Período" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hoje">Hoje</SelectItem>
              <SelectItem value="semana">Esta Semana</SelectItem>
              <SelectItem value="mes">Este Mês</SelectItem>
              <SelectItem value="trimestre">Trimestre</SelectItem>
              <SelectItem value="ano">Este Ano</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Dados do Relatório */}
      {relatorioData && (
        <Card>
          <CardHeader>
            <CardTitle>Dados do Relatório - {tipoRelatorio}</CardTitle>
            <CardDescription>Período: {periodo}</CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
              {JSON.stringify(relatorioData, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-blue-100 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownloadRelatorio("vendas")}
                disabled={loading}
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>
            </div>
            <h3 className="font-semibold text-lg mb-2">Relatório de Vendas</h3>
            <p className="text-gray-600 text-sm">Análise completa das vendas por período</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-green-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownloadRelatorio("usuarios")}
                disabled={loading}
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>
            </div>
            <h3 className="font-semibold text-lg mb-2">Relatório de Usuários</h3>
            <p className="text-gray-600 text-sm">Estatísticas de usuários e engajamento</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-orange-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-orange-600" />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownloadRelatorio("financeiro")}
                disabled={loading}
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>
            </div>
            <h3 className="font-semibold text-lg mb-2">Relatório Financeiro</h3>
            <p className="text-gray-600 text-sm">Análise financeira e faturamento</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-purple-100 p-3 rounded-full">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownloadRelatorio("boloes")}
                disabled={loading}
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>
            </div>
            <h3 className="font-semibold text-lg mb-2">Performance de Bolões</h3>
            <p className="text-gray-600 text-sm">Análise de performance dos bolões</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-red-100 p-3 rounded-full">
                <Calendar className="h-6 w-6 text-red-600" />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownloadRelatorio("mensal")}
                disabled={loading}
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Download className="h-4 w-4" />}
              </Button>
            </div>
            <h3 className="font-semibold text-lg mb-2">Relatório Mensal</h3>
            <p className="text-gray-600 text-sm">Resumo mensal de atividades</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="bg-indigo-100 p-3 rounded-full">
                <BarChart3 className="h-6 w-6 text-indigo-600" />
              </div>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            </div>
            <h3 className="font-semibold text-lg mb-2">Analytics Avançado</h3>
            <p className="text-gray-600 text-sm">Análises avançadas e insights</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Relatórios Personalizados</CardTitle>
          <CardDescription>Funcionalidade em desenvolvimento</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Em breve: geração de relatórios personalizados</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
