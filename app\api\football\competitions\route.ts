import { NextRequest, NextResponse } from 'next/server'

// Forçar rota dinâmica
export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'cbeb9f19b15e4252b3f9d3375fefcfcc'

async function fetchFootballData(endpoint: string) {
  try {
    console.log(`🌐 Buscando dados: ${FOOTBALL_API_URL}${endpoint}`)
    
    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

export async function GET(request: NextRequest) {
  try {
    // Evitar execução durante build
    if (process.env.NEXT_PHASE === 'phase-production-build' ||
        process.env.NODE_ENV === 'production' && !request.headers.get('host') ||
        !request.url) {
      return NextResponse.json({ competitions: [], message: "Build mode" })
    }

    console.log("🔍 Buscando competições...")

    // Proteção adicional para URL parsing
    let searchParams
    try {
      searchParams = new URL(request.url).searchParams
    } catch (urlError) {
      console.log("⚠️ Erro ao processar URL durante build, retornando dados vazios")
      return NextResponse.json({ competitions: [], message: "URL parsing error during build" })
    }
    const areas = searchParams.get('areas') // Ex: 2032 para Brasil
    const plan = searchParams.get('plan') || 'TIER_ONE' // TIER_ONE, TIER_TWO, TIER_THREE, TIER_FOUR

    console.log('🏆 Buscando competições da Football Data API:', { areas, plan })

    let endpoint = '/competitions'
    const params = new URLSearchParams()
    
    if (areas) {
      params.append('areas', areas)
    }
    if (plan) {
      params.append('plan', plan)
    }

    if (params.toString()) {
      endpoint += `?${params.toString()}`
    }

    const data = await fetchFootballData(endpoint)

    // Filtrar e formatar competições principais
    const priorityCompetitions = [
      'PL', 'PD', 'SA', 'BL1', 'FL1', // Ligas europeias principais
      'CL', 'EL', 'ECL', // Competições europeias
      'WC', 'EC', 'CLI', // Competições internacionais
      'BSA', 'BSB', 'CSB' // Brasileiras (se disponíveis)
    ]

    const competitions = data.competitions
      .filter((comp: any) => comp.currentSeason)
      .map((comp: any) => ({
        id: comp.id,
        code: comp.code,
        name: comp.name,
        type: comp.type,
        emblem: comp.emblem,
        area: {
          id: comp.area.id,
          name: comp.area.name,
          code: comp.area.code,
          flag: comp.area.flag
        },
        currentSeason: {
          id: comp.currentSeason.id,
          startDate: comp.currentSeason.startDate,
          endDate: comp.currentSeason.endDate,
          currentMatchday: comp.currentSeason.currentMatchday,
          winner: comp.currentSeason.winner
        },
        numberOfAvailableSeasons: comp.numberOfAvailableSeasons,
        lastUpdated: comp.lastUpdated,
        priority: priorityCompetitions.indexOf(comp.code)
      }))
      .sort((a: any, b: any) => {
        // Ordenar por prioridade (competições principais primeiro)
        if (a.priority !== -1 && b.priority !== -1) {
          return a.priority - b.priority
        }
        if (a.priority !== -1) return -1
        if (b.priority !== -1) return 1
        return a.name.localeCompare(b.name)
      })

    console.log(`✅ ${competitions.length} competições encontradas`)

    return NextResponse.json({
      success: true,
      competitions: competitions,
      total: competitions.length,
      filters: { areas, plan }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar competições:', error)
    
    // Retornar erro em caso de falha


    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar dados da API',
      competitions: [],
      total: 0
    })
  }
}
