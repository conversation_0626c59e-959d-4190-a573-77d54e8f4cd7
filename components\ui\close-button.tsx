'use client'

import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface CloseButtonProps {
  onClick: () => void
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

export function CloseButton({ onClick, className = '', size = 'md' }: CloseButtonProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8', 
    lg: 'w-10 h-10'
  }

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  }

  return (
    <button
      onClick={onClick}
      className={`${sizeClasses[size]} bg-white hover:bg-gray-100 rounded-full flex items-center justify-center transition-colors shadow-md border border-gray-200 ${className}`}
    >
      <X className={`${iconSizes[size]} text-gray-800`} />
    </button>
  )
}
