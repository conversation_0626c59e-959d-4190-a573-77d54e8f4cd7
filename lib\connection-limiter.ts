/**
 * Middleware para limitar conexões simultâneas e evitar "Too many connections"
 */

import { NextRequest, NextResponse } from 'next/server'

// Map para rastrear conexões ativas por IP
const activeConnections = new Map<string, number>()
const connectionTimestamps = new Map<string, number[]>()

// Configurações
const MAX_CONNECTIONS_PER_IP = 10
const TIME_WINDOW = 60000 // 1 minuto
const MAX_REQUESTS_PER_MINUTE = 30

/**
 * Middleware para limitar conexões por IP
 */
export function connectionLimiter(request: NextRequest): NextResponse | null {
  const ip = getClientIP(request)
  
  // Verificar limite de conexões simultâneas
  const currentConnections = activeConnections.get(ip) || 0
  if (currentConnections >= MAX_CONNECTIONS_PER_IP) {
    console.warn(`⚠️ Limite de conexões excedido para IP ${ip}: ${currentConnections}`)
    return NextResponse.json(
      { error: 'Muitas conexões simultâneas. Tente novamente em alguns segundos.' },
      { status: 429 }
    )
  }
  
  // Verificar limite de requisições por minuto
  const now = Date.now()
  const timestamps = connectionTimestamps.get(ip) || []
  
  // Remover timestamps antigos
  const recentTimestamps = timestamps.filter(timestamp => now - timestamp < TIME_WINDOW)
  
  if (recentTimestamps.length >= MAX_REQUESTS_PER_MINUTE) {
    console.warn(`⚠️ Limite de requisições por minuto excedido para IP ${ip}: ${recentTimestamps.length}`)
    return NextResponse.json(
      { error: 'Muitas requisições. Tente novamente em alguns segundos.' },
      { status: 429 }
    )
  }
  
  // Registrar nova conexão
  activeConnections.set(ip, currentConnections + 1)
  recentTimestamps.push(now)
  connectionTimestamps.set(ip, recentTimestamps)
  
  return null // Permitir requisição
}

/**
 * Função para liberar conexão quando a requisição terminar
 */
export function releaseConnection(request: NextRequest): void {
  const ip = getClientIP(request)
  const currentConnections = activeConnections.get(ip) || 0
  
  if (currentConnections > 0) {
    activeConnections.set(ip, currentConnections - 1)
  }
  
  // Limpar IPs sem conexões ativas
  if (currentConnections <= 1) {
    activeConnections.delete(ip)
  }
}

/**
 * Obter IP do cliente
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}

/**
 * Limpar conexões antigas periodicamente
 */
export function cleanupOldConnections(): void {
  const now = Date.now()
  
  for (const [ip, timestamps] of connectionTimestamps.entries()) {
    const recentTimestamps = timestamps.filter(timestamp => now - timestamp < TIME_WINDOW)
    
    if (recentTimestamps.length === 0) {
      connectionTimestamps.delete(ip)
      activeConnections.delete(ip)
    } else {
      connectionTimestamps.set(ip, recentTimestamps)
    }
  }
}

/**
 * Obter estatísticas de conexões
 */
export function getConnectionStats(): {
  totalActiveConnections: number
  connectionsByIP: Record<string, number>
  requestsByIP: Record<string, number>
} {
  const connectionsByIP: Record<string, number> = {}
  const requestsByIP: Record<string, number> = {}
  
  let totalActiveConnections = 0
  
  for (const [ip, connections] of activeConnections.entries()) {
    connectionsByIP[ip] = connections
    totalActiveConnections += connections
  }
  
  const now = Date.now()
  for (const [ip, timestamps] of connectionTimestamps.entries()) {
    const recentTimestamps = timestamps.filter(timestamp => now - timestamp < TIME_WINDOW)
    requestsByIP[ip] = recentTimestamps.length
  }
  
  return {
    totalActiveConnections,
    connectionsByIP,
    requestsByIP
  }
}

// Limpar conexões antigas a cada 5 minutos
setInterval(cleanupOldConnections, 5 * 60 * 1000)

/**
 * Wrapper para APIs que usa o limitador de conexões
 */
export function withConnectionLimit<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return async (...args: T): Promise<NextResponse> => {
    const request = args[0] as NextRequest
    
    // Verificar limite de conexões
    const limitResponse = connectionLimiter(request)
    if (limitResponse) {
      return limitResponse
    }
    
    try {
      // Executar handler original
      const response = await handler(...args)
      return response
    } finally {
      // Liberar conexão
      releaseConnection(request)
    }
  }
}
