-- Criação do banco de dados do Sistema Bolão para MySQL
-- Execute este script para criar todas as tabelas necessárias

-- Configurar modo SQL para compatibilidade
SET sql_mode = '';

-- Criar banco de dados se não existir
CREATE DATABASE IF NOT EXISTS `sistema-bolao-top` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `sistema-bolao-top`;

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    telefone VARCHAR(20),
    endereco TEXT,
    cpf_cnpj VARCHAR(20),
    senha_hash VARCHAR(255) NOT NULL,
    tipo ENUM('admin', 'usuario', 'cambista') DEFAULT 'usuario',
    status ENUM('ativo', 'inativo', 'bloqueado') DEFAULT 'ativo',
    saldo DECIMAL(10,2) DEFAULT 0.00,
    data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ultimo_acesso TIMESTAMP NULL,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_tipo (tipo),
    INDEX idx_status (status)
);

-- Tabela de campeonatos
CREATE TABLE IF NOT EXISTS campeonatos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    pais VARCHAR(100),
    temporada VARCHAR(50),
    status ENUM('ativo', 'encerrado', 'pausado') DEFAULT 'ativo',
    data_inicio DATE NULL,
    data_fim DATE NULL,
    api_id VARCHAR(50),
    external_id VARCHAR(50),
    logo_url VARCHAR(500),
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_api_id (api_id),
    INDEX idx_external_id (external_id)
);

-- Tabela de times
CREATE TABLE IF NOT EXISTS times (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    nome_curto VARCHAR(50),
    cidade VARCHAR(100),
    estado VARCHAR(100),
    pais VARCHAR(100),
    logo_url VARCHAR(500),
    api_id VARCHAR(50),
    external_id VARCHAR(50),
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_nome (nome),
    INDEX idx_api_id (api_id),
    INDEX idx_external_id (external_id)
);

-- Tabela de jogos
CREATE TABLE IF NOT EXISTS jogos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    campeonato_id INT NOT NULL,
    time_casa_id INT NOT NULL,
    time_fora_id INT NOT NULL,
    data_jogo TIMESTAMP NOT NULL,
    local_jogo VARCHAR(255),
    rodada INT,
    resultado_casa INT DEFAULT NULL,
    resultado_fora INT DEFAULT NULL,
    placar_casa INT DEFAULT NULL,
    placar_fora INT DEFAULT NULL,
    status ENUM('agendado', 'ao_vivo', 'finalizado', 'cancelado') DEFAULT 'agendado',
    api_id VARCHAR(50),
    external_id VARCHAR(50),
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (campeonato_id) REFERENCES campeonatos(id) ON DELETE CASCADE,
    FOREIGN KEY (time_casa_id) REFERENCES times(id),
    FOREIGN KEY (time_fora_id) REFERENCES times(id),
    INDEX idx_data_jogo (data_jogo),
    INDEX idx_status (status),
    INDEX idx_campeonato (campeonato_id),
    INDEX idx_api_id (api_id),
    INDEX idx_external_id (external_id)
);

-- Tabela de bolões
CREATE TABLE IF NOT EXISTS boloes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    valor_aposta DECIMAL(10,2) NOT NULL,
    premio_total DECIMAL(10,2) NOT NULL,
    max_participantes INT,
    min_acertos INT DEFAULT 3,
    data_inicio TIMESTAMP NOT NULL,
    data_fim TIMESTAMP NOT NULL,
    status ENUM('ativo', 'encerrado', 'em_breve') DEFAULT 'em_breve',
    criado_por INT NOT NULL,
    regras TEXT,
    campeonatos_selecionados JSON,
    partidas_selecionadas JSON,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (criado_por) REFERENCES usuarios(id),
    INDEX idx_status (status),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_data_fim (data_fim)
);

-- Tabela de jogos do bolão (relaciona bolões com jogos)
CREATE TABLE IF NOT EXISTS bolao_jogos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bolao_id INT NOT NULL,
    jogo_id INT NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bolao_id) REFERENCES boloes(id) ON DELETE CASCADE,
    FOREIGN KEY (jogo_id) REFERENCES jogos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_bolao_jogo (bolao_id, jogo_id),
    INDEX idx_bolao (bolao_id),
    INDEX idx_jogo (jogo_id)
);

-- Tabela de apostas
CREATE TABLE IF NOT EXISTS apostas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    bolao_id INT NOT NULL,
    valor_total DECIMAL(10,2) NOT NULL,
    status ENUM('pendente', 'paga', 'cancelada') DEFAULT 'pendente',
    data_aposta TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_pagamento TIMESTAMP NULL,
    codigo_bilhete VARCHAR(50) UNIQUE,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (bolao_id) REFERENCES boloes(id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_bolao (bolao_id),
    INDEX idx_status (status),
    INDEX idx_codigo_bilhete (codigo_bilhete)
);

-- Tabela de detalhes das apostas (palpites por jogo)
CREATE TABLE IF NOT EXISTS aposta_detalhes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    aposta_id INT NOT NULL,
    jogo_id INT NOT NULL,
    resultado_apostado ENUM('casa', 'empate', 'fora') NOT NULL,
    acertou BOOLEAN DEFAULT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (aposta_id) REFERENCES apostas(id) ON DELETE CASCADE,
    FOREIGN KEY (jogo_id) REFERENCES jogos(id),
    UNIQUE KEY unique_aposta_jogo (aposta_id, jogo_id),
    INDEX idx_aposta (aposta_id),
    INDEX idx_jogo (jogo_id)
);

-- Tabela de pagamentos
CREATE TABLE IF NOT EXISTS pagamentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    aposta_id INT NOT NULL,
    usuario_id INT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    metodo_pagamento ENUM('pix', 'cartao', 'dinheiro') NOT NULL,
    status ENUM('pendente', 'aprovado', 'rejeitado', 'cancelado') DEFAULT 'pendente',
    chave_pix VARCHAR(255),
    codigo_transacao VARCHAR(255),
    qr_code TEXT,
    txid VARCHAR(255),
    webhook_data JSON,
    data_pagamento TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_confirmacao TIMESTAMP NULL,
    data_vencimento TIMESTAMP NULL,
    FOREIGN KEY (aposta_id) REFERENCES apostas(id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    INDEX idx_status (status),
    INDEX idx_txid (txid),
    INDEX idx_usuario (usuario_id)
);

-- Tabela de prêmios
CREATE TABLE IF NOT EXISTS premios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bolao_id INT NOT NULL,
    usuario_id INT NOT NULL,
    aposta_id INT NOT NULL,
    acertos INT NOT NULL,
    valor_premio DECIMAL(10,2) NOT NULL,
    status ENUM('pendente', 'pago', 'cancelado') DEFAULT 'pendente',
    data_premio TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data_pagamento TIMESTAMP NULL,
    FOREIGN KEY (bolao_id) REFERENCES boloes(id),
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (aposta_id) REFERENCES apostas(id),
    INDEX idx_bolao (bolao_id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_status (status)
);

-- Tabela de configurações do sistema
CREATE TABLE IF NOT EXISTS configuracoes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chave VARCHAR(100) UNIQUE NOT NULL,
    valor TEXT,
    descricao TEXT,
    tipo ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_chave (chave)
);

-- Tabela de logs do sistema
CREATE TABLE IF NOT EXISTS logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT,
    acao VARCHAR(255) NOT NULL,
    tabela_afetada VARCHAR(100),
    registro_id INT,
    dados_anteriores JSON,
    dados_novos JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    data_log TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_data_log (data_log),
    INDEX idx_acao (acao)
);

-- Inserir dados iniciais

-- Usuário administrador padrão
INSERT IGNORE INTO usuarios (nome, email, senha_hash, tipo) VALUES
('Administrador', '<EMAIL>', '$2b$10$hash_da_senha_admin123', 'admin');

-- Configurações iniciais do sistema
INSERT IGNORE INTO configuracoes (chave, valor, descricao, tipo) VALUES
('sistema_nome', 'Sistema Bolão', 'Nome do sistema', 'string'),
('pix_chave', '00000000000', 'Chave PIX para recebimentos', 'string'),
('taxa_sistema', '10', 'Taxa do sistema em porcentagem', 'number'),
('max_apostas_usuario', '50', 'Máximo de apostas por usuário por bolão', 'number'),
('tempo_limite_pagamento', '30', 'Tempo limite para pagamento em minutos', 'number'),
('email_suporte', '<EMAIL>', 'Email de suporte', 'string'),
('whatsapp_suporte', '11999999999', 'WhatsApp de suporte', 'string');

-- Campeonatos iniciais
INSERT IGNORE INTO campeonatos (nome, descricao, pais, temporada, status, data_inicio, data_fim) VALUES
('Brasileirão Série A', 'Campeonato Brasileiro de Futebol - Série A', 'Brasil', '2024', 'ativo', '2024-04-13', '2024-12-08'),
('Copa do Brasil', 'Copa do Brasil de Futebol', 'Brasil', '2024', 'ativo', '2024-02-28', '2024-11-10'),
('Copa Libertadores', 'Copa Libertadores da América', 'América do Sul', '2024', 'ativo', '2024-02-06', '2024-11-30'),
('Copa Sul-Americana', 'Copa Sul-Americana', 'América do Sul', '2024', 'ativo', '2024-03-05', '2024-11-23');

-- Times principais do Brasileirão
INSERT IGNORE INTO times (nome, nome_curto, cidade, estado, pais) VALUES
('Clube de Regatas do Flamengo', 'Flamengo', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Sociedade Esportiva Palmeiras', 'Palmeiras', 'São Paulo', 'SP', 'Brasil'),
('São Paulo Futebol Clube', 'São Paulo', 'São Paulo', 'SP', 'Brasil'),
('Sport Club Corinthians Paulista', 'Corinthians', 'São Paulo', 'SP', 'Brasil'),
('Clube Atlético Mineiro', 'Atlético-MG', 'Belo Horizonte', 'MG', 'Brasil'),
('Cruzeiro Esporte Clube', 'Cruzeiro', 'Belo Horizonte', 'MG', 'Brasil'),
('Grêmio Foot-Ball Porto Alegrense', 'Grêmio', 'Porto Alegre', 'RS', 'Brasil'),
('Sport Club Internacional', 'Internacional', 'Porto Alegre', 'RS', 'Brasil'),
('Botafogo de Futebol e Regatas', 'Botafogo', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Club de Regatas Vasco da Gama', 'Vasco', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Fluminense Football Club', 'Fluminense', 'Rio de Janeiro', 'RJ', 'Brasil'),
('Santos Futebol Clube', 'Santos', 'Santos', 'SP', 'Brasil'),
('Athletico Paranaense', 'Athletico-PR', 'Curitiba', 'PR', 'Brasil'),
('Coritiba Foot Ball Club', 'Coritiba', 'Curitiba', 'PR', 'Brasil'),
('Fortaleza Esporte Clube', 'Fortaleza', 'Fortaleza', 'CE', 'Brasil'),
('Ceará Sporting Club', 'Ceará', 'Fortaleza', 'CE', 'Brasil'),
('Bahia Esporte Clube', 'Bahia', 'Salvador', 'BA', 'Brasil'),
('Vitória Esporte Clube', 'Vitória', 'Salvador', 'BA', 'Brasil'),
('Goiás Esporte Clube', 'Goiás', 'Goiânia', 'GO', 'Brasil'),
('Atlético Clube Goianiense', 'Atlético-GO', 'Goiânia', 'GO', 'Brasil');

COMMIT;
