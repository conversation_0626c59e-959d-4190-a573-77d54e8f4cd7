import { executeQuery } from './database-config.js'

// Configuração da API Football-data.org
const FOOTBALL_API_BASE = 'https://api.football-data.org/v4'
const FOOTBALL_API_KEY = process.env.FOOTBALL_API_TOKEN || 'YOUR_API_KEY_HERE'

const headers = {
  'X-Auth-Token': FOOTBALL_API_KEY,
  'Content-Type': 'application/json'
}

// Mapeamento de competições importantes
const COMPETITIONS_MAP = {
  // Brasil
  'BSA': { id: 2013, name: 'Brasileirão Série A', country: 'Brasil' },
  'BSB': { id: 2014, name: 'Brasileirão Série B', country: 'Brasil' },
  
  // Europa
  'PL': { id: 2021, name: 'Premier League', country: 'Inglaterra' },
  'PD': { id: 2014, name: 'La Liga', country: 'Espanha' },
  'SA': { id: 2019, name: 'Serie A', country: 'Itália' },
  'BL1': { id: 2002, name: 'Bundesliga', country: 'Alemanha' },
  'FL1': { id: 2015, name: 'Ligue 1', country: 'França' },
  
  // Champions League
  'CL': { id: 2001, name: 'UEFA Champions League', country: 'Europa' },
  'EL': { id: 2018, name: 'UEFA Europa League', country: 'Europa' },
  
  // Copa do Mundo
  'WC': { id: 2000, name: 'FIFA World Cup', country: 'Mundial' },
  
  // Copa América
  'CLI': { id: 2152, name: 'Copa América', country: 'América do Sul' }
}

/**
 * Busca dados da API Football-data.org
 */
async function fetchFootballData(endpoint) {
  try {
    console.log(`🌐 Buscando dados: ${FOOTBALL_API_BASE}${endpoint}`)
    
    const response = await fetch(`${FOOTBALL_API_BASE}${endpoint}`, {
      headers,
      timeout: 10000
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error.message)
    throw error
  }
}

/**
 * Sincroniza competições
 */
async function syncCompetitions() {
  try {
    console.log('🏆 Iniciando sincronização de competições...')
    
    const data = await fetchFootballData('/competitions')
    const competitions = data.competitions || []
    
    let syncCount = 0
    
    for (const comp of competitions) {
      try {
        // Verificar se a competição já existe
        const existing = await executeQuery(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [comp.id]
        )
        
        const competitionData = {
          nome: comp.name,
          descricao: comp.name,
          pais: comp.area?.name || 'Internacional',
          temporada: new Date().getFullYear(),
          status: 'ativo',
          data_inicio: comp.currentSeason?.startDate || new Date().toISOString().split('T')[0],
          data_fim: comp.currentSeason?.endDate || new Date(Date.now() + 365*24*60*60*1000).toISOString().split('T')[0],
          logo_url: comp.emblem || null,
          api_id: comp.id,
          codigo: comp.code
        }
        
        if (existing.length === 0) {
          // Inserir nova competição
          await executeQuery(`
            INSERT INTO campeonatos (
              nome, descricao, pais, temporada, status, data_inicio, data_fim,
              logo_url, api_id, codigo, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.temporada,
            competitionData.status,
            competitionData.data_inicio,
            competitionData.data_fim,
            competitionData.logo_url,
            competitionData.api_id,
            competitionData.codigo
          ])
          
          console.log(`✅ Competição criada: ${comp.name}`)
          syncCount++
        } else {
          // Atualizar competição existente
          await executeQuery(`
            UPDATE campeonatos SET
              nome = ?, descricao = ?, pais = ?, logo_url = ?, codigo = ?
            WHERE api_id = ?
          `, [
            competitionData.nome,
            competitionData.descricao,
            competitionData.pais,
            competitionData.logo_url,
            competitionData.codigo,
            comp.id
          ])
          
          console.log(`🔄 Competição atualizada: ${comp.name}`)
        }
        
        // Aguardar um pouco para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Erro ao processar competição ${comp.name}:`, error.message)
      }
    }
    
    console.log(`🏆 Sincronização de competições concluída: ${syncCount} competições processadas`)
    return { success: true, count: syncCount }
    
  } catch (error) {
    console.error('❌ Erro na sincronização de competições:', error.message)
    throw error
  }
}

/**
 * Sincroniza times de uma competição
 */
async function syncTeams(competitionId) {
  try {
    console.log(`👥 Sincronizando times da competição ${competitionId}...`)

    const data = await fetchFootballData(`/competitions/${competitionId}/teams`)
    const teams = data.teams || []

    let syncCount = 0

    for (const team of teams) {
      try {
        // Verificar se o time já existe
        const existing = await executeQuery(
          'SELECT id FROM times WHERE api_id = ?',
          [team.id]
        )

        const teamData = {
          nome: team.name,
          nome_curto: team.shortName || team.tla || team.name,
          pais: team.area?.name || 'Desconhecido',
          logo_url: team.crest || null,
          api_id: team.id,
          codigo: team.tla
        }
        
        if (existing.length === 0) {
          // Inserir novo time
          await executeQuery(`
            INSERT INTO times (
              nome, nome_curto, pais, logo_url, api_id, codigo, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, NOW())
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.pais,
            teamData.logo_url,
            teamData.api_id,
            teamData.codigo
          ])
          
          console.log(`✅ Time criado: ${team.name}`)
          syncCount++
        } else {
          // Atualizar time existente
          await executeQuery(`
            UPDATE times SET
              nome = ?, nome_curto = ?, pais = ?, logo_url = ?, codigo = ?
            WHERE api_id = ?
          `, [
            teamData.nome,
            teamData.nome_curto,
            teamData.pais,
            teamData.logo_url,
            teamData.codigo,
            team.id
          ])
          
          console.log(`🔄 Time atualizado: ${team.name}`)
        }
        
        // Aguardar um pouco para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Erro ao processar time ${team.name}:`, error.message)
      }
    }
    
    console.log(`👥 Sincronização de times concluída: ${syncCount} times processados`)

    // 🛡️ PROTEÇÃO: Logos dos times são FIXOS e não são alterados automaticamente
    console.log('🛡️ Logos dos times mantidos como estão no banco (SOMENTE LEITURA)')

    return { success: true, count: syncCount }

  } catch (error) {
    console.error(`❌ Erro na sincronização de times da competição ${competitionId}:`, error.message)
    throw error
  }
}

/**
 * Sincroniza partidas de uma competição
 */
async function syncMatches(competitionId, matchday = null) {
  try {
    console.log(`⚽ Sincronizando partidas da competição ${competitionId}...`)
    
    let endpoint = `/competitions/${competitionId}/matches`
    if (matchday) {
      endpoint += `?matchday=${matchday}`
    }
    
    const data = await fetchFootballData(endpoint)
    const matches = data.matches || []
    
    let syncCount = 0
    
    for (const match of matches) {
      try {
        // Verificar se a partida já existe
        const existing = await executeQuery(
          'SELECT id FROM jogos WHERE api_id = ?',
          [match.id]
        )
        
        // Buscar IDs dos times no banco local
        const homeTeam = await executeQuery(
          'SELECT id FROM times WHERE api_id = ?',
          [match.homeTeam.id]
        )
        
        const awayTeam = await executeQuery(
          'SELECT id FROM times WHERE api_id = ?',
          [match.awayTeam.id]
        )
        
        // Buscar ID da competição no banco local
        const competition = await executeQuery(
          'SELECT id FROM campeonatos WHERE api_id = ?',
          [competitionId]
        )
        
        if (homeTeam.length === 0 || awayTeam.length === 0 || competition.length === 0) {
          console.log(`⚠️ Dados incompletos para partida ${match.id}, pulando...`)
          continue
        }
        
        const matchData = {
          campeonato_id: competition[0].id,
          time_casa_id: homeTeam[0].id,
          time_fora_id: awayTeam[0].id,
          data_jogo: match.utcDate,
          local_jogo: match.venue || null,
          rodada: match.matchday || null,
          resultado_casa: match.score?.fullTime?.home || null,
          resultado_fora: match.score?.fullTime?.away || null,
          status: match.status === 'FINISHED' ? 'finalizado' : 
                  match.status === 'IN_PLAY' ? 'ao_vivo' : 'agendado',
          api_id: match.id
        }
        
        if (existing.length === 0) {
          // Inserir nova partida
          await executeQuery(`
            INSERT INTO jogos (
              campeonato_id, time_casa_id, time_fora_id, data_jogo, local_jogo,
              rodada, resultado_casa, resultado_fora, status, api_id, data_criacao
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
          `, [
            matchData.campeonato_id,
            matchData.time_casa_id,
            matchData.time_fora_id,
            matchData.data_jogo,
            matchData.local_jogo,
            matchData.rodada,
            matchData.resultado_casa,
            matchData.resultado_fora,
            matchData.status,
            matchData.api_id
          ])
          
          console.log(`✅ Partida criada: ${match.homeTeam.shortName} vs ${match.awayTeam.shortName}`)
          syncCount++
        } else {
          // Atualizar partida existente
          await executeQuery(`
            UPDATE jogos SET
              data_jogo = ?, local_jogo = ?, rodada = ?,
              resultado_casa = ?, resultado_fora = ?, status = ?
            WHERE api_id = ?
          `, [
            matchData.data_jogo,
            matchData.local_jogo,
            matchData.rodada,
            matchData.resultado_casa,
            matchData.resultado_fora,
            matchData.status,
            match.id
          ])
          
          console.log(`🔄 Partida atualizada: ${match.homeTeam.shortName} vs ${match.awayTeam.shortName}`)
        }
        
        // Aguardar um pouco para não sobrecarregar a API
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`❌ Erro ao processar partida ${match.id}:`, error.message)
      }
    }
    
    console.log(`⚽ Sincronização de partidas concluída: ${syncCount} partidas processadas`)
    return { success: true, count: syncCount }
    
  } catch (error) {
    console.error(`❌ Erro na sincronização de partidas da competição ${competitionId}:`, error.message)
    throw error
  }
}

export {
  syncCompetitions,
  syncTeams,
  syncMatches,
  COMPETITIONS_MAP,
  fetchFootballData
}
