import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    console.log(`💳 Buscando pagamentos do usuário: ${userId}`)

    try {
      await initializeDatabase()

      // Buscar pagamentos do usuário com timeout
      const pagamentos = await Promise.race([
        executeQuery(`
          SELECT
            b.id,
            b.codigo,
            b.valor_total as valor,
            b.status,
            'PIX' as metodo,
            b.created_at,
            b.codigo as bilhete_codigo
          FROM bilhetes b
          WHERE b.usuario_id = ?
          ORDER BY b.created_at DESC
          LIMIT 50
        `, [userId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na consulta de pagamentos')), 5000)
        )
      ])

      console.log(`✅ ${pagamentos?.length || 0} pagamentos encontrados para usuário ${userId}`)

      // Formatar os dados para o frontend
      const pagamentosFormatados = (pagamentos || []).map((pagamento: any) => ({
        id: "PAG" + pagamento.id,
        data: new Date(pagamento.created_at).toLocaleString('pt-BR'),
        valor: parseFloat(pagamento.valor),
        status: pagamento.status === 'pendente' ? 'pendente' : 'aprovado',
        metodo: pagamento.metodo,
        bilhete_id: pagamento.bilhete_codigo
      }))

      return NextResponse.json({
        success: true,
        pagamentos: pagamentosFormatados,
        total: pagamentosFormatados.length,
        source: 'database'
      })

    } catch (dbError) {
      console.warn(`⚠️ Erro ao acessar banco, retornando dados básicos:`, dbError.message)

      // Retornar dados básicos sem erro
      return NextResponse.json({
        success: true,
        pagamentos: [],
        total: 0,
        source: 'fallback'
      })
    }

  } catch (error) {
    console.error("❌ Erro ao buscar pagamentos:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
