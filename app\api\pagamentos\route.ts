import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database-config'

export async function GET(request: NextRequest) {
  try {
    // Por enquanto, vamos buscar todos os pagamentos
    // Em produção, isso deveria ser filtrado por usuário logado
    const pagamentos = await executeQuery(`
      SELECT 
        p.id,
        p.valor,
        p.metodo_pagamento as metodo,
        p.status,
        p.data_pagamento as data,
        p.codigo_transacao,
        a.codigo_bilhete as referencia,
        b.nome as descricao_bolao,
        CASE 
          WHEN pr.id IS NOT NULL THEN 'entrada'
          ELSE 'saida'
        END as tipo,
        CASE 
          WHEN pr.id IS NOT NULL THEN CONCAT('Prêmio - ', b.nome)
          ELSE CONCAT('Aposta - ', b.nome)
        END as descricao
      FROM pagamentos p
      LEFT JOIN apostas a ON p.aposta_id = a.id
      LEFT JOIN boloes b ON a.bolao_id = b.id
      LEFT JOIN premios pr ON a.id = pr.aposta_id
      ORDER BY p.data_pagamento DESC
      LIMIT 50
    `)

    // Formatar dados para o frontend
    const pagamentosFormatted = pagamentos.map((pagamento: any) => ({
      id: pagamento.id,
      tipo: pagamento.tipo,
      descricao: pagamento.descricao,
      valor: parseFloat(pagamento.valor),
      metodo: formatMetodo(pagamento.metodo),
      status: formatStatus(pagamento.status),
      data: new Date(pagamento.data).toISOString().split('T')[0],
      referencia: pagamento.referencia || `TXN${pagamento.id}`
    }))

    return NextResponse.json(pagamentosFormatted)

  } catch (error) {
    console.error('❌ Erro ao buscar pagamentos:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}

function formatMetodo(metodo: string) {
  switch (metodo) {
    case 'pix':
      return 'PIX'
    case 'cartao':
      return 'Cartão de Crédito'
    case 'dinheiro':
      return 'Dinheiro'
    default:
      return metodo
  }
}

function formatStatus(status: string) {
  switch (status) {
    case 'aprovado':
      return 'concluido'
    case 'pendente':
      return 'pendente'
    case 'rejeitado':
      return 'rejeitado'
    case 'cancelado':
      return 'cancelado'
    default:
      return status
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { aposta_id, valor, metodo_pagamento, chave_pix } = body

    if (!aposta_id || !valor || !metodo_pagamento) {
      return NextResponse.json(
        { error: 'Dados obrigatórios não fornecidos' },
        { status: 400 }
      )
    }

    // Buscar dados da aposta
    const aposta = await executeQuery(`
      SELECT usuario_id FROM apostas WHERE id = ?
    `, [aposta_id])

    if (!aposta || aposta.length === 0) {
      return NextResponse.json(
        { error: 'Aposta não encontrada' },
        { status: 404 }
      )
    }

    const usuario_id = aposta[0].usuario_id

    // Inserir pagamento
    const result = await executeQuery(`
      INSERT INTO pagamentos (aposta_id, usuario_id, valor, metodo_pagamento, chave_pix, status)
      VALUES (?, ?, ?, ?, ?, 'pendente')
    `, [aposta_id, usuario_id, valor, metodo_pagamento, chave_pix])

    const pagamento_id = (result as any).insertId

    return NextResponse.json({
      success: true,
      pagamento_id,
      message: 'Pagamento criado com sucesso'
    })

  } catch (error) {
    console.error('❌ Erro ao criar pagamento:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
