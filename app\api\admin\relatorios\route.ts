import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const tipo = searchParams.get("tipo") || "vendas"
    const periodo = searchParams.get("periodo") || "mes"

    console.log("📊 Gerando relatório:", { tipo, periodo })

    let relatorio = {}

    switch (tipo) {
      case "vendas":
        relatorio = await gerarRelatorioVendas(periodo)
        break
      case "usuarios":
        relatorio = await gerarRelatorioUsuarios(periodo)
        break
      case "financeiro":
        relatorio = await gerarRelatorioFinanceiro(periodo)
        break
      case "boloes":
        relatorio = await gerarRelatorioBoloes(periodo)
        break
      case "mensal":
        relatorio = await gerarRelatorioMensal()
        break
      case "cambistas":
        relatorio = await gerarRelatorioCambistas(periodo)
        break
      default:
        relatorio = await gerarRelatorioGeral()
    }

    return NextResponse.json({
      success: true,
      tipo,
      periodo,
      data: relatorio,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro ao gerar relatório:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

async function gerarRelatorioVendas(periodo: string) {
  const whereClause = getWhereClausePeriodo(periodo)
  
  const [vendas, vendasPorDia, topUsuarios] = await Promise.all([
    executeQuery(`
      SELECT 
        COUNT(*) as total_bilhetes,
        SUM(valor_total) as faturamento_total,
        AVG(valor_total) as ticket_medio,
        SUM(CASE WHEN status = 'pago' THEN 1 ELSE 0 END) as bilhetes_pagos,
        SUM(CASE WHEN status = 'pendente' THEN 1 ELSE 0 END) as bilhetes_pendentes
      FROM bilhetes 
      WHERE ${whereClause}
    `),
    executeQuery(`
      SELECT 
        DATE(created_at) as data,
        COUNT(*) as bilhetes,
        SUM(valor_total) as faturamento
      FROM bilhetes 
      WHERE ${whereClause}
      GROUP BY DATE(created_at)
      ORDER BY data DESC
      LIMIT 30
    `),
    executeQuery(`
      SELECT 
        usuario_nome,
        COUNT(*) as total_bilhetes,
        SUM(valor_total) as total_gasto
      FROM bilhetes 
      WHERE ${whereClause}
      GROUP BY usuario_nome
      ORDER BY total_gasto DESC
      LIMIT 10
    `)
  ])

  return {
    resumo: vendas[0] || {},
    vendas_por_dia: vendasPorDia || [],
    top_usuarios: topUsuarios || []
  }
}

async function gerarRelatorioUsuarios(periodo: string) {
  const whereClause = getWhereClausePeriodo(periodo, 'data_cadastro')
  
  const [usuarios, novosPorDia, tiposUsuario] = await Promise.all([
    executeQuery(`
      SELECT 
        COUNT(*) as total_usuarios,
        SUM(CASE WHEN status = 'ativo' THEN 1 ELSE 0 END) as usuarios_ativos,
        SUM(CASE WHEN tipo = 'cambista' THEN 1 ELSE 0 END) as cambistas,
        SUM(CASE WHEN status = 'bloqueado' THEN 1 ELSE 0 END) as usuarios_bloqueados
      FROM usuarios 
      WHERE ${whereClause}
    `),
    executeQuery(`
      SELECT 
        DATE(data_cadastro) as data,
        COUNT(*) as novos_usuarios
      FROM usuarios 
      WHERE ${whereClause}
      GROUP BY DATE(data_cadastro)
      ORDER BY data DESC
      LIMIT 30
    `),
    executeQuery(`
      SELECT 
        tipo,
        COUNT(*) as quantidade,
        status,
        COUNT(*) as total
      FROM usuarios 
      WHERE ${whereClause}
      GROUP BY tipo, status
    `)
  ])

  return {
    resumo: usuarios[0] || {},
    novos_por_dia: novosPorDia || [],
    tipos_usuario: tiposUsuario || []
  }
}

async function gerarRelatorioFinanceiro(periodo: string) {
  const whereClause = getWhereClausePeriodo(periodo)
  
  const [financeiro, receitaPorDia, statusPagamentos] = await Promise.all([
    executeQuery(`
      SELECT 
        SUM(CASE WHEN status = 'pago' THEN valor_total ELSE 0 END) as receita_confirmada,
        SUM(CASE WHEN status = 'pendente' THEN valor_total ELSE 0 END) as receita_pendente,
        COUNT(CASE WHEN status = 'pago' THEN 1 END) as pagamentos_confirmados,
        COUNT(CASE WHEN status = 'pendente' THEN 1 END) as pagamentos_pendentes,
        AVG(CASE WHEN status = 'pago' THEN valor_total END) as ticket_medio_pago
      FROM bilhetes 
      WHERE ${whereClause}
    `),
    executeQuery(`
      SELECT 
        DATE(created_at) as data,
        SUM(CASE WHEN status = 'pago' THEN valor_total ELSE 0 END) as receita_confirmada,
        SUM(CASE WHEN status = 'pendente' THEN valor_total ELSE 0 END) as receita_pendente
      FROM bilhetes 
      WHERE ${whereClause}
      GROUP BY DATE(created_at)
      ORDER BY data DESC
      LIMIT 30
    `),
    executeQuery(`
      SELECT 
        status,
        COUNT(*) as quantidade,
        SUM(valor_total) as valor_total
      FROM bilhetes 
      WHERE ${whereClause}
      GROUP BY status
    `)
  ])

  return {
    resumo: financeiro[0] || {},
    receita_por_dia: receitaPorDia || [],
    status_pagamentos: statusPagamentos || []
  }
}

async function gerarRelatorioBoloes(periodo: string) {
  const whereClause = getWhereClausePeriodo(periodo, 'data_criacao')
  
  const [boloes, topBoloes] = await Promise.all([
    executeQuery(`
      SELECT 
        COUNT(*) as total_boloes,
        SUM(CASE WHEN status = 'ativo' THEN 1 ELSE 0 END) as boloes_ativos,
        AVG(valor_aposta) as valor_medio_aposta,
        SUM(total_arrecadado) as total_arrecadado
      FROM boloes 
      WHERE ${whereClause}
    `),
    executeQuery(`
      SELECT 
        nome,
        valor_aposta,
        total_arrecadado,
        participantes,
        status
      FROM boloes 
      WHERE ${whereClause}
      ORDER BY total_arrecadado DESC
      LIMIT 10
    `)
  ])

  return {
    resumo: boloes[0] || {},
    top_boloes: topBoloes || []
  }
}

async function gerarRelatorioMensal() {
  const [resumoMensal, comparativoMeses] = await Promise.all([
    executeQuery(`
      SELECT 
        COUNT(*) as bilhetes_mes,
        SUM(valor_total) as faturamento_mes,
        COUNT(DISTINCT usuario_id) as usuarios_ativos_mes,
        AVG(valor_total) as ticket_medio_mes
      FROM bilhetes 
      WHERE MONTH(created_at) = MONTH(NOW()) 
      AND YEAR(created_at) = YEAR(NOW())
    `),
    executeQuery(`
      SELECT 
        YEAR(created_at) as ano,
        MONTH(created_at) as mes,
        COUNT(*) as bilhetes,
        SUM(valor_total) as faturamento
      FROM bilhetes 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
      GROUP BY YEAR(created_at), MONTH(created_at)
      ORDER BY ano DESC, mes DESC
    `)
  ])

  return {
    resumo_mensal: resumoMensal[0] || {},
    comparativo_meses: comparativoMeses || []
  }
}

async function gerarRelatorioCambistas(periodo: string) {
  const whereClause = getWhereClausePeriodo(periodo, 'b.created_at')
  
  const [cambistas] = await Promise.all([
    executeQuery(`
      SELECT 
        u.nome,
        u.email,
        COUNT(b.id) as total_vendas,
        SUM(b.valor_total) as faturamento_total,
        u.status
      FROM usuarios u
      LEFT JOIN bilhetes b ON u.id = b.usuario_id AND ${whereClause}
      WHERE u.tipo = 'cambista'
      GROUP BY u.id, u.nome, u.email, u.status
      ORDER BY faturamento_total DESC
    `)
  ])

  return {
    cambistas: cambistas || []
  }
}

async function gerarRelatorioGeral() {
  const [geral] = await Promise.all([
    executeQuery(`
      SELECT 
        (SELECT COUNT(*) FROM usuarios) as total_usuarios,
        (SELECT COUNT(*) FROM bilhetes) as total_bilhetes,
        (SELECT COUNT(*) FROM boloes) as total_boloes,
        (SELECT SUM(valor_total) FROM bilhetes WHERE status = 'pago') as faturamento_total,
        (SELECT COUNT(*) FROM bilhetes WHERE DATE(created_at) = CURDATE()) as bilhetes_hoje
    `)
  ])

  return {
    resumo_geral: geral[0] || {}
  }
}

function getWhereClausePeriodo(periodo: string, campo: string = 'created_at'): string {
  switch (periodo) {
    case 'hoje':
      return `DATE(${campo}) = CURDATE()`
    case 'semana':
      return `${campo} >= DATE_SUB(NOW(), INTERVAL 7 DAY)`
    case 'mes':
      return `${campo} >= DATE_SUB(NOW(), INTERVAL 30 DAY)`
    case 'trimestre':
      return `${campo} >= DATE_SUB(NOW(), INTERVAL 90 DAY)`
    case 'ano':
      return `${campo} >= DATE_SUB(NOW(), INTERVAL 365 DAY)`
    default:
      return `${campo} >= DATE_SUB(NOW(), INTERVAL 30 DAY)`
  }
}
