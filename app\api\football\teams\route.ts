import { NextRequest, NextResponse } from 'next/server'

// Forçar rota dinâmica
export const dynamic = 'force-dynamic'
export const runtime = 'nodejs'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'cbeb9f19b15e4252b3f9d3375fefcfcc'

async function fetchFootballData(endpoint: string) {
  try {
    console.log(`🌐 Buscando dados: ${FOOTBALL_API_URL}${endpoint}`)
    
    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

export async function GET(request: NextRequest) {
  try {
    // Evitar execução durante build
    if (process.env.NEXT_PHASE === 'phase-production-build' ||
        process.env.NODE_ENV === 'production' && !request.headers.get('host') ||
        !request.url) {
      return NextResponse.json({ teams: [], message: "Build mode" })
    }

    console.log("🔍 Buscando times...")

    // Proteção adicional para URL parsing
    let searchParams
    try {
      searchParams = new URL(request.url).searchParams
    } catch (urlError) {
      console.log("⚠️ Erro ao processar URL durante build, retornando dados vazios")
      return NextResponse.json({ teams: [], message: "URL parsing error during build" })
    }
    const competition = searchParams.get('competition') // Ex: PL, PD, SA
    const season = searchParams.get('season') // Ex: 2023
    const teamId = searchParams.get('teamId') // Para buscar time específico

    console.log('👥 Buscando times da Football Data API:', { competition, season, teamId })

    let teams: any[] = []

    if (teamId) {
      // Buscar time específico
      const teamData = await fetchFootballData(`/teams/${teamId}`)
      teams = [teamData]
    } else if (competition) {
      // Buscar times de uma competição
      let endpoint = `/competitions/${competition}/teams`
      if (season) {
        endpoint += `?season=${season}`
      }
      
      const data = await fetchFootballData(endpoint)
      teams = data.teams || []
    } else {
      return NextResponse.json({
        success: false,
        error: 'Parâmetro competition ou teamId é obrigatório'
      }, { status: 400 })
    }

    // Formatar dados dos times
    const formattedTeams = teams.map((team: any) => ({
      id: team.id,
      name: team.name,
      shortName: team.shortName || team.tla || team.name,
      tla: team.tla,
      crest: team.crest || '/images/team-placeholder.png',
      address: team.address,
      website: team.website,
      founded: team.founded,
      clubColors: team.clubColors,
      venue: team.venue,
      area: {
        id: team.area?.id,
        name: team.area?.name,
        code: team.area?.code,
        flag: team.area?.flag
      },
      runningCompetitions: team.runningCompetitions?.map((comp: any) => ({
        id: comp.id,
        name: comp.name,
        code: comp.code,
        type: comp.type,
        emblem: comp.emblem
      })) || [],
      coach: team.coach ? {
        id: team.coach.id,
        firstName: team.coach.firstName,
        lastName: team.coach.lastName,
        name: team.coach.name,
        dateOfBirth: team.coach.dateOfBirth,
        nationality: team.coach.nationality
      } : null,
      squad: team.squad?.map((player: any) => ({
        id: player.id,
        name: player.name,
        position: player.position,
        dateOfBirth: player.dateOfBirth,
        nationality: player.nationality
      })) || [],
      staff: team.staff || [],
      lastUpdated: team.lastUpdated
    }))

    console.log(`✅ ${formattedTeams.length} times encontrados`)

    return NextResponse.json({
      success: true,
      teams: formattedTeams,
      total: formattedTeams.length,
      filters: { competition, season, teamId }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar times:', error)
    
    // Retornar erro em caso de falha


    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar dados da API',
      teams: [],
      total: 0
    })
  }
}
