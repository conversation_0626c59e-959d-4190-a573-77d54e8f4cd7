import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

// Função para atualizar saldo do usuário
async function atualizarSaldo(
  userId: number, 
  tipo: string, 
  valor: number, 
  descricao: string, 
  bilheteId?: number,
  transactionId?: string
) {
  try {
    // Buscar saldo atual
    const usuario = await executeQuery(`
      SELECT saldo FROM usuarios WHERE id = ?
    `, [userId])

    if (!usuario || usuario.length === 0) {
      throw new Error('Usuário não encontrado')
    }

    const saldoAnterior = parseFloat(usuario[0].saldo) || 0
    let saldoPosterior = saldoAnterior

    // Calcular novo saldo baseado no tipo de transação
    switch (tipo) {
      case 'deposito':
      case 'premio':
      case 'estorno':
      case 'bonus':
        saldoPosterior = saldoAnterior + valor
        break
      case 'compra_bilhete':
        saldoPosterior = saldoAnterior - valor
        if (saldoPosterior < 0) {
          throw new Error('Saldo insuficiente')
        }
        break
      default:
        throw new Error('Tipo de transação inválido')
    }

    // Iniciar transação
    await executeQuery('START TRANSACTION')

    try {
      // Atualizar saldo do usuário
      await executeQuery(`
        UPDATE usuarios SET saldo = ? WHERE id = ?
      `, [saldoPosterior, userId])

      // Registrar transação
      const transacaoResult = await executeQuery(`
        INSERT INTO saldo_transacoes (
          usuario_id, tipo, valor, saldo_anterior, saldo_posterior,
          descricao, bilhete_id, transaction_id, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'confirmado')
      `, [
        userId, tipo, valor, saldoAnterior, saldoPosterior,
        descricao, bilheteId || null, transactionId || null
      ])

      // Confirmar transação
      await executeQuery('COMMIT')

      console.log(`✅ Saldo atualizado para usuário ${userId}:`, {
        tipo,
        valor,
        saldoAnterior,
        saldoPosterior,
        transacaoId: (transacaoResult as any).insertId
      })

      return {
        success: true,
        saldoAnterior,
        saldoPosterior,
        transacaoId: (transacaoResult as any).insertId
      }

    } catch (error) {
      await executeQuery('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('❌ Erro ao atualizar saldo:', error)
    throw error
  }
}

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'user_id é obrigatório'
      }, { status: 400 })
    }

    // Buscar saldo atual do usuário
    const usuario = await executeQuery(`
      SELECT id, nome, email, saldo FROM usuarios WHERE id = ?
    `, [userId])

    if (!usuario || usuario.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não encontrado'
      }, { status: 404 })
    }

    // Buscar histórico de transações
    const transacoes = await executeQuery(`
      SELECT 
        id, tipo, valor, saldo_anterior, saldo_posterior,
        descricao, bilhete_id, transaction_id, status, created_at
      FROM saldo_transacoes 
      WHERE usuario_id = ?
      ORDER BY created_at DESC
      LIMIT 50
    `, [userId])

    const saldoAtual = parseFloat(usuario[0].saldo) || 0

    console.log(`💰 Saldo consultado para usuário ${userId}: R$ ${saldoAtual.toFixed(2)}`)

    return NextResponse.json({
      success: true,
      usuario: {
        id: usuario[0].id,
        nome: usuario[0].nome,
        email: usuario[0].email,
        saldo: saldoAtual
      },
      transacoes: (transacoes || []).map((t: any) => ({
        id: t.id,
        tipo: t.tipo,
        valor: parseFloat(t.valor),
        saldoAnterior: parseFloat(t.saldo_anterior),
        saldoPosterior: parseFloat(t.saldo_posterior),
        descricao: t.descricao,
        bilheteId: t.bilhete_id,
        transactionId: t.transaction_id,
        status: t.status,
        data: new Date(t.created_at).toLocaleString('pt-BR')
      }))
    })

  } catch (error) {
    console.error('❌ Erro ao consultar saldo:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const {
      user_id,
      tipo,
      valor,
      descricao,
      bilhete_id,
      transaction_id
    } = body

    console.log('💰 Processando transação de saldo:', {
      user_id, tipo, valor, descricao, bilhete_id, transaction_id
    })

    // Validações
    if (!user_id || !tipo || !valor || !descricao) {
      return NextResponse.json({
        success: false,
        error: 'Dados obrigatórios não fornecidos'
      }, { status: 400 })
    }

    if (!['deposito', 'compra_bilhete', 'premio', 'estorno', 'bonus'].includes(tipo)) {
      return NextResponse.json({
        success: false,
        error: 'Tipo de transação inválido'
      }, { status: 400 })
    }

    if (valor <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Valor deve ser maior que zero'
      }, { status: 400 })
    }

    // Atualizar saldo
    const resultado = await atualizarSaldo(
      user_id, tipo, valor, descricao, bilhete_id, transaction_id
    )

    return NextResponse.json({
      success: true,
      message: 'Transação processada com sucesso',
      ...resultado
    })

  } catch (error) {
    console.error('❌ Erro ao processar transação:', error)
    
    let errorMessage = 'Erro interno do servidor'
    if (error instanceof Error) {
      if (error.message === 'Saldo insuficiente') {
        errorMessage = 'Saldo insuficiente para esta operação'
      } else if (error.message === 'Usuário não encontrado') {
        errorMessage = 'Usuário não encontrado'
      }
    }

    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 })
  }
}

// Exportar função para uso em outras APIs
export { atualizarSaldo }
