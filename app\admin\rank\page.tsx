'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Crown, Trophy, Medal, Star, TrendingUp, Eye } from 'lucide-react'
import { toast } from 'sonner'

interface RankingUser {
  id: number
  nome: string
  email: string
  pontos_totais: number
  apostas_certas: number
  apostas_totais: number
  percentual_acerto: number
  posicao: number
}

interface BilheteModal {
  isOpen: boolean
  userId: number | null
  userName: string
}

export default function RankPage() {
  const [ranking, setRanking] = useState<RankingUser[]>([])
  const [loading, setLoading] = useState(true)
  const [bilheteModal, setBilheteModal] = useState<BilheteModal>({
    isOpen: false,
    userId: null,
    userName: ''
  })

  useEffect(() => {
    fetchRanking()
  }, [])

  const fetchRanking = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/admin/ranking')

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        setRanking(data.ranking || [])
      } else {
        throw new Error(data.error || 'Erro ao buscar ranking')
      }
    } catch (error) {
      console.error('Erro ao buscar ranking:', error)
      toast.error('Erro ao carregar ranking')
      setRanking([])
    } finally {
      setLoading(false)
    }
  }

  const handleVerBilhete = async (userId: number, userName: string) => {
    try {
      const response = await fetch(`/api/user/bilhetes?user_id=${userId}`)

      if (!response.ok) {
        throw new Error('Erro ao buscar bilhetes')
      }

      const data = await response.json()

      if (data.success && data.bilhetes && data.bilhetes.length > 0) {
        // Pegar o primeiro bilhete para mostrar
        const bilhete = data.bilhetes[0]
        mostrarBilheteFormatado(bilhete, userName)
      } else {
        toast.error('Nenhum bilhete encontrado para este usuário')
      }
    } catch (error) {
      console.error('Erro ao buscar bilhete:', error)
      toast.error('Erro ao carregar bilhete')
    }
  }

  const mostrarBilheteFormatado = (bilhete: any, userName: string) => {
    const bilheteFormatado = `
================================
        BOLÃO BRASIL
================================
Bilhete: ${bilhete.codigo || bilhete.id}
Data: ${bilhete.data} ${bilhete.hora || ''}
Usuário: ${userName}
--------------------------------
SUAS APOSTAS:
${bilhete.apostas.map((aposta: any, index: number) => {
  // Extrair nomes dos times do campo jogo (formato: "Time Casa x Time Fora")
  const jogoPartes = aposta.jogo.split(' x ')
  const timeCasa = jogoPartes[0] || 'Time Casa'
  const timeFora = jogoPartes[1] || 'Time Fora'

  // Capitalizar resultado
  const resultadoFormatado = aposta.resultado === 'casa' ? 'Casa' :
                            aposta.resultado === 'fora' ? 'Fora' : 'Empate'

  return `${index + 1}. ${timeCasa} x ${timeFora}
   Resultado: ${resultadoFormatado}
--------------------------------`
}).join('\n')}
Valor: R$ ${bilhete.valor.toFixed(2)}
--------------------------------

--------------------------------
Boa sorte!
================================`

    // Criar uma nova janela para mostrar o bilhete
    const novaJanela = window.open('', '_blank', 'width=600,height=800,scrollbars=yes')
    if (novaJanela) {
      novaJanela.document.write(`
        <html>
          <head>
            <title>Bilhete - ${bilhete.codigo || bilhete.id}</title>
            <style>
              body {
                font-family: 'Courier New', monospace;
                white-space: pre-wrap;
                padding: 20px;
                background: #f5f5f5;
              }
              .bilhete {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                max-width: 500px;
                margin: 0 auto;
              }
              .print-btn {
                margin: 20px auto;
                display: block;
                padding: 10px 20px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
              }
              @media print {
                .print-btn { display: none; }
                body { background: white; }
                .bilhete { box-shadow: none; }
              }
            </style>
          </head>
          <body>
            <div class="bilhete">${bilheteFormatado.replace(/\n/g, '<br>')}</div>
            <button class="print-btn" onclick="window.print()">Imprimir Bilhete</button>
          </body>
        </html>
      `)
      novaJanela.document.close()
    }
  }

  const getRankIcon = (posicao: number) => {
    switch (posicao) {
      case 1:
        return <Crown className="h-6 w-6 text-yellow-500" />
      case 2:
        return <Trophy className="h-6 w-6 text-gray-400" />
      case 3:
        return <Medal className="h-6 w-6 text-amber-600" />
      default:
        return <Star className="h-6 w-6 text-blue-500" />
    }
  }

  const getRankColor = (posicao: number) => {
    switch (posicao) {
      case 1:
        return 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200'
      case 2:
        return 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200'
      case 3:
        return 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200'
      default:
        return 'bg-white border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando ranking...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">🏆 Ranking de Usuários</h1>
          <p className="text-gray-600 mt-2">Classificação dos melhores apostadores</p>
        </div>
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-5 w-5 text-green-600" />
          <span className="text-sm text-gray-600">Atualizado em tempo real</span>
        </div>
      </div>

      <div className="grid gap-4">
        {ranking.map((user) => (
          <Card key={user.id} className={`transition-all hover:shadow-lg ${getRankColor(user.posicao)}`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getRankIcon(user.posicao)}
                    <span className="text-2xl font-bold text-gray-700">#{user.posicao}</span>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{user.nome}</h3>
                    <p className="text-sm text-gray-600">{user.email}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-3xl font-bold text-green-600">{user.apostas_certas}/{user.apostas_totais}</p>
                    <p className="text-xs text-gray-500">Acertos</p>
                  </div>

                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{user.pontos_totais}</p>
                    <p className="text-xs text-gray-500">Pontos (1 por acerto)</p>
                  </div>

                  <div className="text-center">
                    <Badge variant={user.percentual_acerto >= 70 ? 'default' : user.percentual_acerto >= 50 ? 'secondary' : 'destructive'}>
                      {user.percentual_acerto}%
                    </Badge>
                    <p className="text-xs text-gray-500 mt-1">Taxa de Acerto</p>
                  </div>

                  <div className="text-center">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleVerBilhete(user.id, user.nome)}
                      className="flex items-center space-x-2"
                    >
                      <Eye className="h-4 w-4" />
                      <span>Ver Bilhete</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>📊 Estatísticas Gerais</CardTitle>
          <CardDescription>Resumo do desempenho geral dos usuários</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <p className="text-2xl font-bold text-blue-600">{ranking.length}</p>
              <p className="text-sm text-gray-600">Usuários Ativos</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-2xl font-bold text-green-600">
                {Math.round(ranking.reduce((acc, user) => acc + user.percentual_acerto, 0) / ranking.length)}%
              </p>
              <p className="text-sm text-gray-600">Taxa Média de Acerto</p>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <p className="text-2xl font-bold text-yellow-600">
                {ranking.reduce((acc, user) => acc + user.pontos_totais, 0)}
              </p>
              <p className="text-sm text-gray-600">Total de Pontos</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <p className="text-2xl font-bold text-purple-600">
                {ranking.reduce((acc, user) => acc + user.apostas_totais, 0)}
              </p>
              <p className="text-sm text-gray-600">Total de Apostas</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
