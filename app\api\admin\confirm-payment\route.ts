import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: Request) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { bilheteId, transactionId } = body

    console.log("💰 Confirmando pagamento:", { bilheteId, transactionId })

    // Se não foi fornecido ID específico, pegar o bilhete mais recente de R$ 1,00
    let targetBilheteId = bilheteId

    if (!targetBilheteId) {
      console.log("🔍 Buscando bilhete mais recente de R$ 1,00...")
      
      const bilhetesRecentes = await executeQuery(`
        SELECT id, codigo, status, valor_total, created_at 
        FROM bilhetes 
        WHERE valor_total = 1.00 
        AND status = 'pendente'
        AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
        ORDER BY created_at DESC
        LIMIT 1
      `)

      if (bilhetesRecentes.length === 0) {
        return NextResponse.json({
          success: false,
          message: "Nenhum bilhete pendente de R$ 1,00 encontrado nas últimas 2 horas"
        })
      }

      targetBilheteId = bilhetesRecentes[0].id
      console.log(`🎯 Bilhete selecionado: ID ${targetBilheteId} - ${bilhetesRecentes[0].codigo}`)
    }

    // Buscar bilhete antes da atualização
    const bilheteAntes = await executeQuery(`
      SELECT * FROM bilhetes WHERE id = ?
    `, [targetBilheteId])

    if (bilheteAntes.length === 0) {
      return NextResponse.json({
        success: false,
        message: `Bilhete ID ${targetBilheteId} não encontrado`
      })
    }

    console.log(`📋 Bilhete encontrado:`)
    console.log(`   ID: ${bilheteAntes[0].id}`)
    console.log(`   Código: ${bilheteAntes[0].codigo}`)
    console.log(`   Status atual: ${bilheteAntes[0].status}`)
    console.log(`   Valor: R$ ${bilheteAntes[0].valor_total}`)
    console.log(`   Usuário: ${bilheteAntes[0].usuario_nome}`)

    // Atualizar status para "pago"
    const updateResult = await executeQuery(`
      UPDATE bilhetes 
      SET status = 'pago', updated_at = NOW() 
      WHERE id = ?
    `, [targetBilheteId])

    console.log(`✅ Resultado da atualização:`, updateResult)

    // Buscar bilhete após atualização
    const bilheteDepois = await executeQuery(`
      SELECT * FROM bilhetes WHERE id = ?
    `, [targetBilheteId])

    console.log(`🎉 Bilhete atualizado:`)
    console.log(`   Status novo: ${bilheteDepois[0].status}`)
    console.log(`   Atualizado em: ${bilheteDepois[0].updated_at}`)

    // Se foi fornecido transaction_id, também atualizar
    if (transactionId) {
      console.log(`🔗 Associando transaction_id: ${transactionId}`)
      
      // Verificar se a tabela tem coluna transaction_id
      try {
        await executeQuery(`
          UPDATE bilhetes 
          SET transaction_id = ? 
          WHERE id = ?
        `, [transactionId, targetBilheteId])
        console.log(`✅ Transaction ID associado`)
      } catch (error) {
        console.log(`⚠️ Não foi possível associar transaction_id (coluna pode não existir)`)
      }
    }

    return NextResponse.json({
      success: true,
      message: "Pagamento confirmado com sucesso!",
      bilhete: {
        id: bilheteDepois[0].id,
        codigo: bilheteDepois[0].codigo,
        status: bilheteDepois[0].status,
        valor_total: bilheteDepois[0].valor_total,
        usuario_nome: bilheteDepois[0].usuario_nome,
        updated_at: bilheteDepois[0].updated_at
      },
      statusAnterior: bilheteAntes[0].status,
      statusNovo: bilheteDepois[0].status
    })

  } catch (error) {
    console.error("❌ Erro ao confirmar pagamento:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
