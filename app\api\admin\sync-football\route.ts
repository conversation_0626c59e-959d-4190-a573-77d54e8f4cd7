import { NextResponse } from "next/server"
import { syncCompetitions, syncTeamsForCompetition, syncMatches } from "@/scripts/sync-football-data.js"
import { initializeDatabase, executeQuery } from "@/lib/database"

export async function POST(request: Request) {
  try {
    const { type } = await request.json()
    
    await initializeDatabase()
    
    let result = {}
    
    switch (type) {
      case 'competitions':
        console.log('🏆 Sincronizando campeonatos...')
        const competitionsCount = await syncCompetitions()
        result = { 
          success: true, 
          message: `${competitionsCount} campeonatos sincronizados`,
          count: competitionsCount
        }
        break
        
      case 'teams':
        console.log('👥 Sincronizando times...')
        const competitions = await executeQuery(`
          SELECT id, api_id, nome FROM campeonatos 
          WHERE status = 'ativo' AND api_id IS NOT NULL
          LIMIT 10
        `)
        
        let totalTeams = 0
        for (const competition of competitions) {
          const teamsCount = await syncTeamsForCompetition(competition.api_id, competition.id)
          totalTeams += teamsCount
          
          // Delay entre competições
          await new Promise(resolve => setTimeout(resolve, 500))
        }
        
        result = { 
          success: true, 
          message: `${totalTeams} times sincronizados`,
          count: totalTeams
        }
        break
        
      case 'matches':
        console.log('⚽ Sincronizando partidas...')
        const matchesCount = await syncMatches()
        result = { 
          success: true, 
          message: `${matchesCount} partidas sincronizadas`,
          count: matchesCount
        }
        break
        
      case 'all':
        console.log('🚀 Sincronização completa...')
        
        // 1. Campeonatos
        const compCount = await syncCompetitions()
        
        // 2. Times
        const comps = await executeQuery(`
          SELECT id, api_id, nome FROM campeonatos 
          WHERE status = 'ativo' AND api_id IS NOT NULL
          LIMIT 8
        `)
        
        let teamsTotal = 0
        for (const comp of comps) {
          const tCount = await syncTeamsForCompetition(comp.api_id, comp.id)
          teamsTotal += tCount
          await new Promise(resolve => setTimeout(resolve, 500))
        }
        
        // 3. Partidas
        const matchTotal = await syncMatches()
        
        result = { 
          success: true, 
          message: `Sincronização completa: ${compCount} campeonatos, ${teamsTotal} times, ${matchTotal} partidas`,
          competitions: compCount,
          teams: teamsTotal,
          matches: matchTotal
        }
        break
        
      default:
        return NextResponse.json(
          { success: false, error: "Tipo de sincronização inválido" },
          { status: 400 }
        )
    }
    
    return NextResponse.json(result)
    
  } catch (error) {
    console.error("❌ Erro na sincronização:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    await initializeDatabase()
    
    // Buscar estatísticas atuais
    const [competitions, teams, matches] = await Promise.all([
      executeQuery("SELECT COUNT(*) as count FROM campeonatos WHERE status = 'ativo'"),
      executeQuery("SELECT COUNT(*) as count FROM times"),
      executeQuery("SELECT COUNT(*) as count FROM jogos WHERE status = 'agendado' AND data_jogo >= NOW()")
    ])
    
    return NextResponse.json({
      success: true,
      stats: {
        competitions: competitions[0]?.count || 0,
        teams: teams[0]?.count || 0,
        matches: matches[0]?.count || 0
      },
      availableTypes: ['competitions', 'teams', 'matches', 'all']
    })
    
  } catch (error) {
    console.error("❌ Erro ao buscar estatísticas:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor"
      },
      { status: 500 }
    )
  }
}
