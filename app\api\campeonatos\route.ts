import { NextResponse } from "next/server"
import { executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'cbeb9f19b15e4252b3f9d3375fefcfcc'

// Cache simples em memória
const cache = new Map()
const CACHE_DURATION = 15 * 60 * 1000 // 15 minutos para campeonatos
const REQUEST_DELAY = 1500 // 1.5 segundos entre requisições

// Rate limiting
let lastRequestTime = 0
let requestCount = 0
const MAX_REQUESTS_PER_MINUTE = 8

// Competições principais disponíveis
const MAIN_COMPETITIONS = [
  // Ligas Europeias Principais
  { code: 'PL', name: 'Premier League', area: 'England', flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿' },
  { code: 'PD', name: 'La Liga', area: 'Spain', flag: '🇪🇸' },
  { code: 'SA', name: 'Serie A', area: 'Italy', flag: '🇮🇹' },
  { code: 'BL1', name: 'Bundesliga', area: 'Germany', flag: '🇩🇪' },
  { code: 'FL1', name: 'Ligue 1', area: 'France', flag: '🇫🇷' },

  // Competições Europeias
  { code: 'CL', name: 'Champions League', area: 'Europe', flag: '🏆' },
  { code: 'EL', name: 'Europa League', area: 'Europe', flag: '🥈' },
  { code: 'EC', name: 'Conference League', area: 'Europe', flag: '🥉' },

  // Outras Ligas Europeias
  { code: 'DED', name: 'Eredivisie', area: 'Netherlands', flag: '🇳🇱' },
  { code: 'PPL', name: 'Primeira Liga', area: 'Portugal', flag: '🇵🇹' },
  { code: 'BSA', name: 'Jupiler Pro League', area: 'Belgium', flag: '🇧🇪' },
  { code: 'PL', name: 'Ekstraklasa', area: 'Poland', flag: '🇵🇱' },
  { code: 'CLI', name: 'Primera División', area: 'Chile', flag: '🇨🇱' },

  // Ligas Sul-Americanas
  { code: 'BSB', name: 'Brasileirão Série A', area: 'Brazil', flag: '🇧🇷' },
  { code: 'PD', name: 'Primera División', area: 'Argentina', flag: '🇦🇷' },
  { code: 'PPD', name: 'Primera División', area: 'Uruguay', flag: '🇺🇾' },
  { code: 'PPC', name: 'Primera División', area: 'Colombia', flag: '🇨🇴' },

  // Copas Internacionais
  { code: 'CLI', name: 'Copa Libertadores', area: 'South America', flag: '🏆' },
  { code: 'CSA', name: 'Copa Sudamericana', area: 'South America', flag: '🥈' },

  // Outras Ligas
  { code: 'MLS', name: 'Major League Soccer', area: 'USA', flag: '🇺🇸' },
  { code: 'WC', name: 'FIFA World Cup', area: 'World', flag: '🌍' },
  { code: 'EC', name: 'European Championship', area: 'Europe', flag: '🏆' }
]

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function fetchFootballData(endpoint: string) {
  // Verificar cache primeiro
  const cacheKey = endpoint
  const cached = cache.get(cacheKey)
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    console.log(`📦 Cache hit: ${endpoint}`)
    return cached.data
  }

  // Rate limiting
  const now = Date.now()
  if (requestCount >= MAX_REQUESTS_PER_MINUTE) {
    console.log(`⏳ Rate limit preventivo atingido, aguardando...`)
    await delay(60000)
    requestCount = 0
  }

  const timeSinceLastRequest = now - lastRequestTime
  if (timeSinceLastRequest < REQUEST_DELAY) {
    await delay(REQUEST_DELAY - timeSinceLastRequest)
  }

  try {
    console.log(`🌐 Buscando: ${FOOTBALL_API_URL}${endpoint}`)

    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    lastRequestTime = Date.now()
    requestCount++

    if (!response.ok) {
      if (response.status === 429) {
        console.log(`⏳ Rate limit da API atingido, aguardando...`)
        await delay(60000)
        requestCount = 0
        throw new Error(`HTTP 429: Rate limit exceeded`)
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    // Salvar no cache
    cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    })

    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

export async function GET(request: Request) {
  try {
    console.log("🏆 Buscando campeonatos reais da Football Data API...")

    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status") || "ativo"
    const area = searchParams.get("pais") || searchParams.get("area")
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 999 // Aumentado para buscar todos os campeonatos

    // Buscar dados reais da Football Data API
    try {

      // Buscar competições da Football Data API
      const competitionsData = await fetchFootballData('/competitions')

      let competitions = competitionsData.competitions || []

      // Filtrar por área/país se especificado, senão buscar TODOS os campeonatos
      if (area && area !== 'todos') {
        // Filtrar por área/país específico
        competitions = competitions.filter((comp: any) =>
          comp.area?.name?.toLowerCase().includes(area.toLowerCase()) ||
          comp.area?.code?.toLowerCase() === area.toLowerCase()
        )
      }
      // Se area não especificado ou 'todos', buscar TODOS os campeonatos disponíveis
      // Removido o filtro de MAIN_COMPETITIONS para buscar todos os campeonatos

      // Buscar estatísticas para cada competição
      const campeonatosComStats = await Promise.all(
        competitions.slice(0, limit).map(async (comp: any) => {
          try {
            // Buscar partidas para estatísticas
            const matchesData = await fetchFootballData(`/competitions/${comp.code}/matches?status=SCHEDULED,TIMED,IN_PLAY,PAUSED,FINISHED`)
            const matches = matchesData.matches || []

            const hoje = new Date().toISOString().split('T')[0]
            const jogosHoje = matches.filter((m: any) => m.utcDate?.startsWith(hoje)).length

            return {
              id: comp.id,
              nome: comp.name,
              descricao: `${comp.area?.name} - ${comp.type}`,
              pais: comp.area?.name || 'Internacional',
              codigo: comp.code,
              status: comp.currentSeason?.winner ? 'finalizado' : 'ativo',
              logo_url: comp.emblem,
              data_criacao: comp.currentSeason?.startDate || new Date().toISOString(),
              data_inicio: comp.currentSeason?.startDate,
              data_fim: comp.currentSeason?.endDate,
              temporada_atual: comp.currentSeason?.id,
              total_jogos: matches.length,
              jogos_futuros: matches.filter((m: any) => ['SCHEDULED', 'TIMED'].includes(m.status)).length,
              jogos_finalizados: matches.filter((m: any) => m.status === 'FINISHED').length,
              jogos_hoje: jogosHoje,
              total_times: comp.numberOfAvailableSeasons || 0,
              area_flag: MAIN_COMPETITIONS.find(main => main.code === comp.code)?.flag || '🌍'
            }
          } catch (error) {
            console.warn(`⚠️ Erro ao buscar estatísticas para ${comp.name}:`, error.message)
            return {
              id: comp.id,
              nome: comp.name,
              descricao: `${comp.area?.name} - ${comp.type}`,
              pais: comp.area?.name || 'Internacional',
              codigo: comp.code,
              status: 'ativo',
              logo_url: comp.emblem,
              data_criacao: new Date().toISOString(),
              total_jogos: 0,
              jogos_futuros: 0,
              jogos_finalizados: 0,
              jogos_hoje: 0,
              total_times: 0,
              area_flag: MAIN_COMPETITIONS.find(main => main.code === comp.code)?.flag || '🌍'
            }
          }
        })
      )

      // Gerar lista de países/áreas disponíveis
      const paisesSet = new Set<string>()
      campeonatosComStats.forEach(comp => paisesSet.add(comp.pais))
      const paises = Array.from(paisesSet).map(pais => ({
        pais,
        total: campeonatosComStats.filter(comp => comp.pais === pais).length
      })).sort((a, b) => b.total - a.total)

      // Estatísticas gerais
      const stats = {
        total: campeonatosComStats.length,
        ativos: campeonatosComStats.filter(comp => comp.status === 'ativo').length,
        nacionais: campeonatosComStats.filter(comp => comp.pais === 'Brasil').length,
        internacionais: campeonatosComStats.filter(comp => comp.pais !== 'Brasil').length,
        total_jogos: campeonatosComStats.reduce((sum, comp) => sum + comp.total_jogos, 0),
        jogos_hoje: campeonatosComStats.reduce((sum, comp) => sum + comp.jogos_hoje, 0)
      }

      console.log(`✅ ${campeonatosComStats.length} campeonatos reais carregados da Football Data API`)

      return NextResponse.json({
        success: true,
        campeonatos: campeonatosComStats,
        total: campeonatosComStats.length,
        paises: paises,
        stats: stats,
        filters: {
          status,
          area,
          limit
        },
        source: 'football-data-api'
      })

    } catch (apiError) {
      console.error("❌ Erro ao buscar campeonatos da Football Data API:", apiError.message)

      // Fallback: buscar dados do banco local
      try {
        console.log("🔄 Tentando fallback para dados do banco...")

        const localCampeonatos = await executeQuery(`
          SELECT
            c.*,
            COUNT(DISTINCT j.id) as total_jogos,
            COUNT(DISTINCT CASE WHEN j.data_jogo > NOW() THEN j.id END) as jogos_futuros,
            COUNT(DISTINCT CASE WHEN j.status = 'finalizado' THEN j.id END) as jogos_finalizados,
            COUNT(DISTINCT CASE WHEN DATE(j.data_jogo) = CURDATE() THEN j.id END) as jogos_hoje,
            COUNT(DISTINCT t.id) as total_times
          FROM campeonatos c
          LEFT JOIN jogos j ON c.id = j.campeonato_id
          LEFT JOIN times t ON (t.id = j.time_casa_id OR t.id = j.time_fora_id)
          WHERE c.status = 'ativo'
          GROUP BY c.id
          ORDER BY c.nome
          LIMIT ?
        `, [limit])

        const campeonatosFormatados = localCampeonatos.map((camp: any) => ({
          id: camp.id,
          nome: camp.nome,
          descricao: camp.descricao || camp.nome,
          pais: camp.pais || 'Brasil',
          codigo: camp.codigo || camp.nome.substring(0, 3).toUpperCase(),
          status: camp.status === 'ativo' ? 'Em Andamento' : 'Finalizado',
          logo_url: camp.logo_url,
          data_criacao: camp.data_criacao,
          total_jogos: parseInt(camp.total_jogos) || 0,
          jogos_futuros: parseInt(camp.jogos_futuros) || 0,
          jogos_finalizados: parseInt(camp.jogos_finalizados) || 0,
          jogos_hoje: parseInt(camp.jogos_hoje) || 0,
          total_times: parseInt(camp.total_times) || 0,
          area_flag: camp.pais === 'Brazil' ? '🇧🇷' : '🌍'
        }))

        const paisesSet = new Set<string>()
        campeonatosFormatados.forEach(comp => paisesSet.add(comp.pais))
        const paises = Array.from(paisesSet).map(pais => ({
          pais,
          total: campeonatosFormatados.filter(comp => comp.pais === pais).length
        }))

        const stats = {
          total: campeonatosFormatados.length,
          ativos: campeonatosFormatados.filter(comp => comp.status === 'Em Andamento').length,
          nacionais: campeonatosFormatados.filter(comp => comp.pais === 'Brasil').length,
          internacionais: campeonatosFormatados.filter(comp => comp.pais !== 'Brasil').length,
          total_jogos: campeonatosFormatados.reduce((sum, comp) => sum + comp.total_jogos, 0),
          jogos_hoje: campeonatosFormatados.reduce((sum, comp) => sum + comp.jogos_hoje, 0)
        }

        console.log(`✅ ${campeonatosFormatados.length} campeonatos carregados do banco local`)

        return NextResponse.json({
          success: true,
          campeonatos: campeonatosFormatados,
          total: campeonatosFormatados.length,
          paises: paises,
          stats: stats,
          filters: { status, area, limit },
          source: 'local-database'
        })

      } catch (dbError) {
        console.error("❌ Erro no fallback do banco:", dbError)

        return NextResponse.json({
          success: false,
          error: 'Erro ao buscar campeonatos da API e do banco',
          message: apiError.message,
          campeonatos: [],
          total: 0,
          paises: [],
          stats: { total: 0, ativos: 0, nacionais: 0, internacionais: 0 }
        }, { status: 500 })
      }
    }

  } catch (error) {
    console.error("❌ Erro ao buscar campeonatos:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        campeonatos: [],
        total: 0,
        paises: [],
        stats: { total: 0, ativos: 0, nacionais: 0, internacionais: 0 }
      },
      { status: 500 }
    )
  }
}
