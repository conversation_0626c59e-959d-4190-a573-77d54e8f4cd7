#!/bin/bash

# Script para iniciar o sistema na porta 8080
echo "🚀 Iniciando Sistema Bolão na porta 8080..."

# Dar permissões aos binários
echo "🔧 Configurando permissões..."
chmod -R u+x node_modules/.bin/

# Verificar se o build existe
if [ ! -d ".next" ]; then
    echo "🏗️  Build não encontrado. Executando build..."
    npm run build
fi

# Parar processos anteriores na porta 8080
echo "🛑 Verificando porta 8080..."
PORT_PID=$(lsof -ti:8080)
if [ ! -z "$PORT_PID" ]; then
    echo "⚠️  Finalizando processo na porta 8080..."
    kill -9 $PORT_PID
    sleep 2
fi

# Iniciar na porta 8080
echo "🚀 Iniciando na porta 8080..."
PORT=8080 npm start

echo "✅ Sistema iniciado!"
echo "🌐 Acesse: http://localhost:8080"
