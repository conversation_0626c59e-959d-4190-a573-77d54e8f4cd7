import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

// Forçar rota dinâmica
export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    // Evitar execução durante build
    if (process.env.NEXT_PHASE === 'phase-production-build' ||
        process.env.NODE_ENV === 'production' && !request.headers.get('host') ||
        !request.url) {
      return NextResponse.json({ success: false, message: "Build mode" })
    }

    console.log("🔍 Verificando pagamento...")

    // Proteção adicional para URL parsing
    let searchParams
    try {
      searchParams = new URL(request.url).searchParams
    } catch (urlError) {
      console.log("⚠️ Erro ao processar URL durante build, retornando dados vazios")
      return NextResponse.json({ success: false, message: "URL parsing error during build" })
    }
    const bilheteId = searchParams.get('bilhete_id')
    const codigo = searchParams.get('codigo')
    const transactionId = searchParams.get('transaction_id')

    await initializeDatabase()

    console.log("🎉 Verificando pagamento bem-sucedido:", { bilheteId, codigo, transactionId })

    // Buscar bilhete pago
    let bilhete = null

    if (bilheteId) {
      const result = await executeQuery(`
        SELECT * FROM bilhetes WHERE id = ? AND status = 'pago'
      `, [bilheteId])
      bilhete = result[0]
    } else if (codigo) {
      const result = await executeQuery(`
        SELECT * FROM bilhetes WHERE codigo = ? AND status = 'pago'
      `, [codigo])
      bilhete = result[0]
    } else {
      // Buscar bilhete mais recente pago de R$ 1,00
      const result = await executeQuery(`
        SELECT * FROM bilhetes 
        WHERE status = 'pago' 
        AND valor_total = 1.00
        AND updated_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY updated_at DESC
        LIMIT 1
      `)
      bilhete = result[0]
    }

    if (!bilhete) {
      return NextResponse.json({
        success: false,
        message: "Bilhete pago não encontrado"
      })
    }

    // Por enquanto, não buscar apostas (estrutura da tabela pode estar diferente)
    const apostas = []

    return NextResponse.json({
      success: true,
      message: "Pagamento confirmado com sucesso!",
      bilhete: {
        id: bilhete.id,
        codigo: bilhete.codigo,
        valor_total: parseFloat(bilhete.valor_total),
        quantidade_apostas: bilhete.quantidade_apostas,
        status: bilhete.status,
        usuario_nome: bilhete.usuario_nome,
        usuario_email: bilhete.usuario_email,
        created_at: bilhete.created_at,
        updated_at: bilhete.updated_at
      },
      apostas: apostas.map(aposta => ({
        id: aposta.id,
        partida_id: aposta.partida_id,
        palpite_casa: aposta.palpite_casa,
        palpite_fora: aposta.palpite_fora,
        valor_aposta: parseFloat(aposta.valor_aposta),
        status: aposta.status
      })),
      transactionId
    })

  } catch (error) {
    console.error("❌ Erro ao verificar pagamento:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
