#!/usr/bin/env node

/**
 * Teste completo do sistema de bolão
 * Testa todas as funcionalidades principais
 */

const BASE_URL = 'http://localhost:3000'

async function testarSistemaCompleto() {
  console.log('🧪 Iniciando teste completo do sistema...\n')

  try {
    // 1. Testar página principal
    console.log('1️⃣ Testando página principal...')
    const homeResponse = await fetch(`${BASE_URL}`)
    if (homeResponse.ok) {
      console.log('✅ Página principal funcionando')
    } else {
      console.log('❌ Erro na página principal:', homeResponse.status)
    }

    // 2. Testar API de usuários
    console.log('\n2️⃣ Testando API de usuários...')
    try {
      const usersResponse = await fetch(`${BASE_URL}/api/users`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })
      console.log('📊 Status API usuários:', usersResponse.status)
    } catch (error) {
      console.log('⚠️ API usuários não disponível (normal se não implementada)')
    }

    // 3. Testar sistema PIX (já sabemos que funciona)
    console.log('\n3️⃣ Testando sistema PIX...')
    const pixResponse = await fetch(`${BASE_URL}/api/wallet/deposit`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_id: 585,
        valor: 25.00,
        client_name: 'Teste Sistema Completo'
      })
    })
    
    if (pixResponse.ok) {
      const pixData = await pixResponse.json()
      console.log('✅ Sistema PIX funcionando:', pixData.success ? 'OK' : 'ERRO')
    } else {
      console.log('❌ Erro no sistema PIX:', pixResponse.status)
    }

    // 4. Testar API de jogos
    console.log('\n4️⃣ Testando API de jogos...')
    try {
      const gamesResponse = await fetch(`${BASE_URL}/api/games`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })
      console.log('📊 Status API jogos:', gamesResponse.status)
    } catch (error) {
      console.log('⚠️ API jogos:', error.message)
    }

    // 5. Testar API de campeonatos
    console.log('\n5️⃣ Testando API de campeonatos...')
    try {
      const competitionsResponse = await fetch(`${BASE_URL}/api/competitions`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })
      console.log('📊 Status API campeonatos:', competitionsResponse.status)
    } catch (error) {
      console.log('⚠️ API campeonatos:', error.message)
    }

    console.log('\n✅ Teste completo finalizado!')
    console.log('🎯 Sistema está funcionando!')
    
  } catch (error) {
    console.error('❌ Erro no teste completo:', error)
  }
}

// Executar teste
testarSistemaCompleto()
