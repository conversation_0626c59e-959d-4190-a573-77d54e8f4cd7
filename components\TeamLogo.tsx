import React, { useState, useEffect } from 'react'
import Image from 'next/image'

interface TeamLogoProps {
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const TeamLogo: React.FC<TeamLogoProps> = ({
  src,
  alt,
  size = 'md',
  className = ''
}) => {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [currentSrc, setCurrentSrc] = useState(src)
  const [fallbackAttempt, setFallbackAttempt] = useState(0)

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  // URLs de fallback para tentar em caso de erro
  const getFallbackUrls = (teamName: string) => {
    const normalizedName = teamName.toLowerCase()
    const fallbacks = []

    // Mapeamento de times brasileiros conhecidos
    const teamMappings: { [key: string]: string[] } = {
      'athletico': ['1773', '1769'], // Athletico Paranaense
      'atlético': ['1766', '1771'], // Atlético MG, Atlético GO
      'bahia': ['1777'],
      'botafogo': ['1770'],
      'ceará': ['1837'],
      'corinthians': ['1779'],
      'cruzeiro': ['1771'],
      'flamengo': ['1783'],
      'fluminense': ['1765'],
      'fortaleza': ['3984'],
      'grêmio': ['1767'],
      'palmeiras': ['1769'],
      'santos': ['6685'],
      'são paulo': ['1776'],
      'sport': ['1778'],
      'vasco': ['1897'],
      'vitória': ['1782']
    }

    // Tentar encontrar mapeamento
    for (const [key, ids] of Object.entries(teamMappings)) {
      if (normalizedName.includes(key)) {
        ids.forEach(id => {
          fallbacks.push(`https://crests.football-data.org/${id}.png`)
        })
        break
      }
    }

    return fallbacks
  }

  const fallbackUrls = getFallbackUrls(alt)
  const fallbackImage = '/placeholder.svg'

  // Sistema de fallback inteligente
  let imageSrc = currentSrc || src

  if (imageError && fallbackAttempt < fallbackUrls.length) {
    imageSrc = fallbackUrls[fallbackAttempt]
  } else if (imageError || !imageSrc) {
    imageSrc = fallbackImage
  }

  const handleImageError = () => {
    console.log(`⚠️ Erro ao carregar logo: ${imageSrc} para ${alt}`)

    // Tentar próximo fallback se disponível
    if (fallbackAttempt < fallbackUrls.length) {
      setFallbackAttempt(prev => prev + 1)
      setCurrentSrc(fallbackUrls[fallbackAttempt])
      setImageError(false) // Reset para tentar novamente
      return
    }

    // Se não há mais fallbacks, mostrar erro final
    setImageError(true)
    setIsLoading(false)
  }

  const handleImageLoad = () => {
    setIsLoading(false)
    setImageError(false) // Reset error state on successful load
  }

  // Reset states when src changes
  useEffect(() => {
    setCurrentSrc(src)
    setImageError(false)
    setIsLoading(true)
    setFallbackAttempt(0)
  }, [src])

  return (
    <div className={`${sizeClasses[size]} relative flex items-center justify-center ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-full" />
      )}
      
      <Image
        src={imageSrc}
        alt={alt}
        width={size === 'sm' ? 24 : size === 'md' ? 32 : 48}
        height={size === 'sm' ? 24 : size === 'md' ? 32 : 48}
        className={`rounded-full object-contain ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
        unoptimized // Para permitir URLs externas
      />
      
      {imageError && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-md border border-white/20">
          <span className={`font-bold text-white ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'}`}>
            {(() => {
              // Sistema inteligente de iniciais para times brasileiros
              const teamName = alt.trim()

              // Casos especiais para times conhecidos
              if (teamName.toLowerCase().includes('athletico') && teamName.toLowerCase().includes('paranaense')) return 'AP'
              if (teamName.toLowerCase().includes('atlético') && teamName.toLowerCase().includes('goianiense')) return 'AG'
              if (teamName.toLowerCase().includes('atlético') && teamName.toLowerCase().includes('mineiro')) return 'AM'
              if (teamName.toLowerCase().includes('bahia')) return 'BA'
              if (teamName.toLowerCase().includes('botafogo')) return 'BF'
              if (teamName.toLowerCase().includes('ceará')) return 'CE'
              if (teamName.toLowerCase().includes('corinthians')) return 'CO'
              if (teamName.toLowerCase().includes('cruzeiro')) return 'CR'
              if (teamName.toLowerCase().includes('flamengo')) return 'FL'
              if (teamName.toLowerCase().includes('fluminense')) return 'FU'
              if (teamName.toLowerCase().includes('fortaleza')) return 'FO'
              if (teamName.toLowerCase().includes('grêmio')) return 'GR'
              if (teamName.toLowerCase().includes('palmeiras')) return 'PA'
              if (teamName.toLowerCase().includes('santos')) return 'SA'
              if (teamName.toLowerCase().includes('são paulo')) return 'SP'
              if (teamName.toLowerCase().includes('sport')) return 'SP'
              if (teamName.toLowerCase().includes('vasco')) return 'VA'
              if (teamName.toLowerCase().includes('vitória')) return 'VI'

              // Lógica geral para outros times
              const words = teamName.split(' ').filter(word => word.length > 0)
              if (words.length >= 2) {
                return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase()
              } else if (words.length === 1) {
                return words[0].substring(0, 2).toUpperCase()
              }
              return 'TC'
            })()}
          </span>
        </div>
      )}
    </div>
  )
}

export default TeamLogo
