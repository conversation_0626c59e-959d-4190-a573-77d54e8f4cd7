import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const transaction_id = searchParams.get("transaction_id")

    console.log("🔍 Verificando status PIX para transaction_id:", transaction_id)

    if (!transaction_id) {
      return NextResponse.json({
        success: false,
        error: "transaction_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Buscar bilhete no banco de dados
    const bilhetes = await executeQuery(`
      SELECT 
        id, codigo, transaction_id, status, valor_total, 
        created_at, updated_at, pix_order_id
      FROM bilhetes 
      WHERE transaction_id = ? OR codigo = ?
      LIMIT 1
    `, [transaction_id, transaction_id])

    if (bilhetes.length === 0) {
      console.log("❌ Bilhete não encontrado para transaction_id:", transaction_id)
      return NextResponse.json({
        success: false,
        error: "Bilhete não encontrado",
        status: "not_found"
      }, { status: 404 })
    }

    const bilhete = bilhetes[0]
    console.log("📋 Bilhete encontrado:", {
      id: bilhete.id,
      codigo: bilhete.codigo,
      status: bilhete.status,
      valor: bilhete.valor_total
    })

    // Verificação com API real do PIX
    let statusAtualizado = bilhete.status
    let shouldUpdate = false
    let providerResponse = null

    try {
      // Integração com API meiodepagamento.com
      console.log("🔍 Consultando API PIX para transaction_id:", bilhete.transaction_id)

      const pixApiUrl = process.env.PIX_API_URL || 'https://api.meiodepagamento.com/api/V1'
      const pixApiToken = process.env.PIX_API_TOKEN || ''

      if (bilhete.transaction_id && pixApiToken) {
        // Consultar status do pagamento na API
        const statusResponse = await fetch(`${pixApiUrl}/pix/status/${bilhete.transaction_id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${pixApiToken}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        })

        if (statusResponse.ok) {
          providerResponse = await statusResponse.json()
          console.log("💳 Resposta da API PIX:", providerResponse)

          // Verificar se o pagamento foi aprovado
          if (providerResponse.status === 'PAID' ||
              providerResponse.status === 'paid' ||
              providerResponse.status === 'approved' ||
              providerResponse.status === 'APPROVED' ||
              providerResponse.payment_status === 'PAID') {

            console.log("✅ Pagamento aprovado via API PIX!")
            statusAtualizado = 'pago'
            shouldUpdate = true
          } else if (providerResponse.status === 'PENDING' ||
                     providerResponse.status === 'pending') {
            console.log("⏳ Pagamento ainda pendente na API PIX")
          } else if (providerResponse.status === 'CANCELLED' ||
                     providerResponse.status === 'cancelled' ||
                     providerResponse.status === 'EXPIRED' ||
                     providerResponse.status === 'expired') {
            console.log("❌ Pagamento cancelado/expirado na API PIX")
            statusAtualizado = 'cancelado'
            shouldUpdate = true
          }
        } else {
          console.log("⚠️ Erro na resposta da API PIX:", statusResponse.status, statusResponse.statusText)

          // API não respondeu - manter status pendente
          console.log("⚠️ API PIX indisponível - mantendo status pendente")
          console.log("🔒 FALLBACK AUTOMÁTICO DESABILITADO - Aguardando webhook oficial")
        }
      } else {
        console.log("⚠️ Token da API PIX não configurado ou transaction_id inválido")
        console.log("🔒 SIMULAÇÃO AUTOMÁTICA DESABILITADA - Aguardando webhook oficial")
      }

    } catch (providerError) {
      console.log("⚠️ Erro ao consultar API PIX:", providerError)
      console.log("🔒 FALLBACK POR ERRO DESABILITADO - Aguardando webhook oficial")
    }

    // Se o status mudou, atualizar no banco
    if (shouldUpdate) {
      console.log("💾 Atualizando status do bilhete para:", statusAtualizado)
      
      await executeQuery(`
        UPDATE bilhetes 
        SET status = ?, updated_at = NOW() 
        WHERE id = ?
      `, [statusAtualizado, bilhete.id])

      // Disparar evento personalizado para notificar o frontend
      console.log("📡 Status atualizado com sucesso")
    }

    const agora = new Date()
    const criadoEm = new Date(bilhete.created_at)
    const tempoDecorrido = (agora.getTime() - criadoEm.getTime()) / 1000

    return NextResponse.json({
      success: true,
      transaction_id: transaction_id,
      bilhete_id: bilhete.id,
      codigo: bilhete.codigo,
      status: statusAtualizado,
      status_anterior: bilhete.status,
      valor: bilhete.valor_total,
      updated: shouldUpdate,
      tempo_decorrido: Math.round(tempoDecorrido),
      provider_response: providerResponse,
      message: shouldUpdate ? "Status atualizado automaticamente" : "Status inalterado",
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro ao verificar status PIX:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
