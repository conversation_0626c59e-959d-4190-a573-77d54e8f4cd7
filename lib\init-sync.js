// Inicialização da sincronização automática
import { startAutoSync } from './sync-football-data.js'

// Verificar se estamos no servidor (não no build)
if (typeof window === 'undefined' && process.env.NODE_ENV !== 'test') {
  console.log('🚀 Inicializando sincronização automática...')
  
  // Aguardar um pouco antes de iniciar para garantir que o banco esteja pronto
  setTimeout(() => {
    startAutoSync()
  }, 5000) // 5 segundos
}

export default function initSync() {
  // Esta função pode ser chamada manualmente se necessário
  if (typeof window === 'undefined') {
    startAutoSync()
  }
}
