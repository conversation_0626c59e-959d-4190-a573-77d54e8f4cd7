import { initializeDatabase, executeQuery } from '../lib/database.js'

async function createBilhetesTables() {
  console.log("🎫 Criando tabelas de bilhetes e pagamentos...")
  
  try {
    await initializeDatabase()
    
    // Criar tabela de bilhetes
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS bilhetes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo VARCHAR(20) UNIQUE NOT NULL,
        user_id INT NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        status ENUM('pendente', 'pago', 'ganhou', 'perdeu') DEFAULT 'pendente',
        premio DECIMAL(10,2) NULL,
        qr_code_pix TEXT NULL,
        transaction_id VARCHAR(100) NULL,
        client_name VARCHAR(255) NULL,
        client_email VARCHAR(255) NULL,
        client_document VARCHAR(20) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_codigo (codigo),
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log("✅ Tabela bilhetes criada")

    // Criar tabela de apostas dos bilhetes
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS bilhete_apostas (
        id INT AUTO_INCREMENT PRIMARY KEY,
        bilhete_id INT NOT NULL,
        match_id INT NOT NULL,
        resultado ENUM('casa', 'empate', 'fora') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_bilhete_id (bilhete_id),
        INDEX idx_match_id (match_id),
        FOREIGN KEY (bilhete_id) REFERENCES bilhetes(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log("✅ Tabela bilhete_apostas criada")

    // Criar tabela de pagamentos
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS pagamentos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        codigo VARCHAR(20) UNIQUE NOT NULL,
        user_id INT NOT NULL,
        bilhete_id INT NULL,
        valor DECIMAL(10,2) NOT NULL,
        status ENUM('pendente', 'aprovado', 'rejeitado', 'cancelado') DEFAULT 'pendente',
        metodo ENUM('PIX', 'CARTAO', 'BOLETO', 'DINHEIRO') DEFAULT 'PIX',
        transaction_id VARCHAR(100) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_user_id (user_id),
        INDEX idx_bilhete_id (bilhete_id),
        INDEX idx_codigo (codigo),
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_status (status),
        FOREIGN KEY (bilhete_id) REFERENCES bilhetes(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log("✅ Tabela pagamentos criada")

    console.log("\n🎯 Tabelas de bilhetes criadas:")
    console.log("   ✅ bilhetes")
    console.log("   ✅ bilhete_apostas") 
    console.log("   ✅ pagamentos")
    
    console.log("\n🚀 Sistema de bilhetes configurado com sucesso!")
    
  } catch (error) {
    console.error("❌ Erro ao criar tabelas:", error)
    process.exit(1)
  }
}

createBilhetesTables()
