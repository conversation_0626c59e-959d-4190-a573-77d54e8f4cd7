/**
 * Middleware para sincronização que garante logos organizados
 */

import { postSyncHook } from './teams-logo-manager.js'

/**
 * Wrapper para funções de sincronização que aplica automaticamente
 * a organização de logos após qualquer sincronização
 */
export function withLogoSync(syncFunction) {
  return async function(...args) {
    try {
      // Executar função de sincronização original
      const result = await syncFunction(...args)
      
      // Se a sincronização foi bem-sucedida, organizar logos
      if (result && result.success) {
        try {
          console.log('🔧 Aplicando organização automática de logos...')
          
          // Importar executeQuery do contexto
          const { executeQuery } = await import('./database-config.js')
          
          // Executar hook pós-sincronização
          await postSyncHook(executeQuery)
          
          console.log('✅ Logos organizados automaticamente')
        } catch (error) {
          console.warn('⚠️ Erro na organização automática de logos:', error.message)
          // Não falhar a sincronização por causa do erro de logo
        }
      }
      
      return result
    } catch (error) {
      console.error('❌ Erro na sincronização:', error.message)
      throw error
    }
  }
}

/**
 * Middleware para APIs que força organização de logos
 */
export async function ensureLogosOrganized(executeQuery) {
  try {
    console.log('🔍 Verificando organização de logos...')
    
    // Verificar se há times com logos externos
    const externalLogos = await executeQuery(`
      SELECT COUNT(*) as count 
      FROM times 
      WHERE logo_url LIKE 'https://%' 
      AND (nome LIKE '%Cruzeiro%' OR nome LIKE '%Botafogo%' OR nome LIKE '%Grêmio%' 
           OR nome LIKE '%Sport%' OR nome LIKE '%Ceará%' OR nome LIKE '%Flamengo%'
           OR nome LIKE '%Palmeiras%' OR nome LIKE '%São Paulo%' OR nome LIKE '%Corinthians%')
    `)
    
    if (externalLogos[0].count > 0) {
      console.log(`🔄 Encontrados ${externalLogos[0].count} times principais com logos externos, organizando...`)
      await postSyncHook(executeQuery)
    } else {
      console.log('✅ Logos já estão organizados')
    }
    
    return true
  } catch (error) {
    console.error('❌ Erro ao verificar logos:', error.message)
    return false
  }
}

/**
 * Função para ser chamada em todas as APIs de sincronização
 */
export async function autoOrganizeLogos(executeQuery) {
  try {
    const { validateAndFixLogos, forceUpdateTeamLogos } = await import('./teams-logo-manager.js')
    
    console.log('🔧 Auto-organização de logos iniciada...')
    
    // 1. Validar e corrigir logos
    const fixed = await validateAndFixLogos(executeQuery)
    
    // 2. Se encontrou problemas, forçar atualização completa
    if (fixed > 0) {
      console.log('🔄 Problemas encontrados, forçando atualização completa...')
      await forceUpdateTeamLogos(executeQuery)
    }
    
    console.log('✅ Auto-organização concluída')
    return true
  } catch (error) {
    console.error('❌ Erro na auto-organização:', error.message)
    return false
  }
}
