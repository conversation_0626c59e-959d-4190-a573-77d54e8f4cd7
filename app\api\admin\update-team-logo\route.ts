import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: Request) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { teamId, logoUrl, imageId } = body

    console.log(`🔄 Atualizando time ID ${teamId} com logo ${logoUrl}...`)

    // Verificar se o time existe
    const timeExistente = await executeQuery(
      `SELECT id, nome, nome_curto, logo_url FROM times WHERE id = ?`,
      [teamId]
    )

    if (timeExistente.length === 0) {
      return NextResponse.json(
        { success: false, error: `Time com ID ${teamId} não encontrado` },
        { status: 404 }
      )
    }

    const time = timeExistente[0]
    console.log(`📋 Time encontrado: ${time.nome} (${time.nome_curto})`)
    console.log(`   Logo atual: ${time.logo_url}`)

    // Verificar se a URL está funcionando
    if (logoUrl.startsWith('http')) {
      try {
        const response = await fetch(logoUrl, { method: 'HEAD' })
        if (!response.ok) {
          console.log(`⚠️ Aviso: URL pode não estar funcionando: ${response.status}`)
        } else {
          console.log(`✅ URL verificada: ${logoUrl} (${response.headers.get('content-length')} bytes)`)
        }
      } catch (error) {
        console.log(`⚠️ Aviso: Erro ao verificar URL: ${error}`)
      }
    }

    // Atualizar o time
    if (imageId) {
      await executeQuery(
        `UPDATE times SET logo_url = ?, image_id = ? WHERE id = ?`,
        [logoUrl, imageId, teamId]
      )
    } else {
      await executeQuery(
        `UPDATE times SET logo_url = ? WHERE id = ?`,
        [logoUrl, teamId]
      )
    }

    // Verificar resultado
    const timeAtualizado = await executeQuery(
      `SELECT id, nome, nome_curto, logo_url, image_id FROM times WHERE id = ?`,
      [teamId]
    )

    console.log(`✅ Time atualizado com sucesso:`)
    console.log(`   ID: ${timeAtualizado[0].id}`)
    console.log(`   Nome: ${timeAtualizado[0].nome}`)
    console.log(`   Logo: ${timeAtualizado[0].logo_url}`)
    console.log(`   Image ID: ${timeAtualizado[0].image_id}`)

    return NextResponse.json({
      success: true,
      message: `Time ${timeAtualizado[0].nome} atualizado com sucesso`,
      timeAnterior: time,
      timeAtualizado: timeAtualizado[0]
    })

  } catch (error) {
    console.error("❌ Erro ao atualizar time:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido"
      },
      { status: 500 }
    )
  }
}
