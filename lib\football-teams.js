// Mapeamento de times brasileiros com seus IDs e escudos da API Football-Data.org

const brazilianTeams = {
  // Série A
  flamengo: {
    id: 1901,
    name: "<PERSON><PERSON>",
    crest: "https://upload.wikimedia.org/wikipedia/commons/thumb/9/93/Flamengo-RJ_%28BRA%29.png/200px-Flamengo-RJ_%28BRA%29.png",
  },
  fluminense: {
    id: 1902,
    name: "Fluminense",
    crest: "https://upload.wikimedia.org/wikipedia/commons/thumb/a/af/Fluminense_FC_escudo.png/200px-Fluminense_FC_escudo.png",
  },
  botafogo: {
    id: 1903,
    name: "<PERSON><PERSON><PERSON><PERSON>",
    crest: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/52/Botafogo_de_Futebol_e_Regatas_logo.svg/200px-Botafogo_de_Futebol_e_Regatas_logo.svg.png",
  },
  cruzeiro: {
    id: 1904,
    name: "<PERSON><PERSON>",
    crest: "https://crests.football-data.org/1904.png",
  },
  ceara: {
    id: 1905,
    name: "<PERSON><PERSON><PERSON>",
    crest: "https://crests.football-data.org/1905.png",
  },
  bahia: {
    id: 1906,
    name: "Bahia",
    crest: "https://crests.football-data.org/1906.png",
  },
  fortaleza: {
    id: 1907,
    name: "Fortaleza",
    crest: "https://crests.football-data.org/1907.png",
  },
  santos: {
    id: 1908,
    name: "Santos",
    crest: "https://upload.wikimedia.org/wikipedia/commons/thumb/3/35/Santos_logo.svg/200px-Santos_logo.svg.png",
  },
  sport: {
    id: 1909,
    name: "Sport Recife",
    crest: "https://crests.football-data.org/1909.png",
  },
  gremio: {
    id: 1910,
    name: "Grêmio",
    crest: "https://upload.wikimedia.org/wikipedia/commons/thumb/f/f1/Gremio.svg/200px-Gremio.svg.png",
  },
  corinthians: {
    id: 1911,
    name: "Corinthians",
    crest: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5a/Corinthians_oficial.svg/200px-Corinthians_oficial.svg.png",
  },
  palmeiras: {
    id: 1900,
    name: "Palmeiras",
    crest: "https://upload.wikimedia.org/wikipedia/commons/thumb/1/10/Palmeiras_logo.svg/200px-Palmeiras_logo.svg.png",
  },

  // Times adicionais
  rbBragantino: {
    id: 5620,
    name: "RB Bragantino",
    crest: "https://crests.football-data.org/5620.png",
  },
  pontePreta: {
    id: 5621,
    name: "Ponte Preta",
    crest: "https://crests.football-data.org/5621.png",
  },
  abc: {
    id: 5622,
    name: "ABC",
    crest: "https://crests.football-data.org/5622.png",
  },
  vitoria: {
    id: 5623,
    name: "Vitória",
    crest: "https://crests.football-data.org/5623.png",
  },
  juventude: {
    id: 5624,
    name: "Juventude",
    crest: "https://crests.football-data.org/5624.png",
  },
  mirassol: {
    id: 5625,
    name: "Mirassol",
    crest: "https://crests.football-data.org/5625.png",
  },
  ituano: {
    id: 5626,
    name: "Ituano",
    crest: "https://crests.football-data.org/5626.png",
  },
  saoBernardo: {
    id: 5627,
    name: "São Bernardo",
    crest: "https://crests.football-data.org/5627.png",
  },
  botafogoPB: {
    id: 5628,
    name: "Botafogo PB",
    crest: "https://crests.football-data.org/5628.png",
  },
  caxias: {
    id: 5629,
    name: "Caxias",
    crest: "https://crests.football-data.org/5629.png",
  },
}

// Função para buscar escudo por nome do time
function getTeamCrest(teamName) {
  const normalizedName = teamName.toLowerCase().replace(/\s+/g, "")

  for (const [key, team] of Object.entries(brazilianTeams)) {
    if (
      team.name.toLowerCase().includes(normalizedName) ||
      normalizedName.includes(team.name.toLowerCase().replace(/\s+/g, ""))
    ) {
      return team.crest
    }
  }

  // Fallback para placeholder se não encontrar
  return "/placeholder.svg?height=24&width=24"
}

// Função para buscar dados completos do time
function getTeamData(teamName) {
  const normalizedName = teamName.toLowerCase().replace(/\s+/g, "")

  for (const [key, team] of Object.entries(brazilianTeams)) {
    if (
      team.name.toLowerCase().includes(normalizedName) ||
      normalizedName.includes(team.name.toLowerCase().replace(/\s+/g, ""))
    ) {
      return team
    }
  }

  return null
}

// Listar todos os times disponíveis
function getAllTeams() {
  return Object.values(brazilianTeams)
}

console.log("🏆 Biblioteca de times brasileiros carregada!")
console.log(`📊 Total de times: ${Object.keys(brazilianTeams).length}`)

// Exemplos de uso
console.log("\n🔍 Exemplos de busca:")
console.log("Flamengo:", getTeamCrest("Flamengo"))
console.log("Palmeiras:", getTeamCrest("Palmeiras"))
console.log("Botafogo:", getTeamCrest("Botafogo"))

// Exportar para uso em outros módulos
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    brazilianTeams,
    getTeamCrest,
    getTeamData,
    getAllTeams,
  }
}
