-- Configurações MySQL para otimizar conexões e evitar "Too many connections"

-- Aumentar limite de conexões
SET GLOBAL max_connections = 200;

-- Limite de conexões por usuário
SET GLOBAL max_user_connections = 50;

-- Timeout para conexões inativas (5 minutos)
SET GLOBAL wait_timeout = 300;
SET GLOBAL interactive_timeout = 300;

-- Timeout para estabelecer conexão (1 minuto)
SET GLOBAL connect_timeout = 60;

-- Configurações de buffer para melhor performance
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL query_cache_size = 32M;
SET GLOBAL query_cache_type = 1;

-- Configurações de thread
SET GLOBAL thread_cache_size = 16;
SET GLOBAL table_open_cache = 400;

-- Mostrar configurações atuais
SELECT 
    'max_connections' as setting, 
    @@max_connections as value
UNION ALL
SELECT 
    'max_user_connections', 
    @@max_user_connections
UNION ALL
SELECT 
    'wait_timeout', 
    @@wait_timeout
UNION ALL
SELECT 
    'interactive_timeout', 
    @@interactive_timeout
UNION ALL
SELECT 
    'connect_timeout', 
    @@connect_timeout;

-- Mostrar status atual de conexões
SHOW STATUS WHERE Variable_name IN (
    'Connections',
    'Max_used_connections', 
    'Threads_connected',
    'Threads_running',
    'Aborted_connects',
    'Aborted_clients'
);

-- Mostrar processos ativos
SELECT 
    COUNT(*) as total_connections,
    SUM(CASE WHEN COMMAND = 'Sleep' THEN 1 ELSE 0 END) as sleeping_connections,
    SUM(CASE WHEN COMMAND != 'Sleep' THEN 1 ELSE 0 END) as active_connections
FROM INFORMATION_SCHEMA.PROCESSLIST;

-- Limpar conexões inativas há mais de 5 minutos
-- (Descomente as linhas abaixo se necessário)
-- SELECT CONCAT('KILL ', ID, ';') as kill_command
-- FROM INFORMATION_SCHEMA.PROCESSLIST 
-- WHERE COMMAND = 'Sleep' 
-- AND TIME > 300 
-- AND USER != 'root';
