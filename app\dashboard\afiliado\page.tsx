"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  UserPlus, 
  Copy, 
  DollarSign, 
  Users, 
  TrendingUp, 
  Calendar,
  ExternalLink,
  Share2,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

interface AfiliadoData {
  id: number
  nome: string
  email: string
  codigo_afiliado: string
  percentual_comissao: number
  comissao_total: number
  total_indicacoes: number
  status: string
  link_afiliado: string
}

interface Indicacao {
  id: number
  usuario_nome: string
  usuario_email: string
  valor_comissao: number
  status: string
  data_indicacao: string
}

interface Stats {
  totalIndicacoes: number
  comissaoTotal: number
  comissaoMes: number
  indicacoesPendentes: number
}

export default function AfiliadoPage() {
  const [afiliadoData, setAfiliadoData] = useState<AfiliadoData | null>(null)
  const [indicacoes, setIndicacoes] = useState<Indicacao[]>([])
  const [stats, setStats] = useState<Stats>({ totalIndicacoes: 0, comissaoTotal: 0, comissaoMes: 0, indicacoesPendentes: 0 })
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    // Verificar se o usuário está logado
    const userData = localStorage.getItem("user")
    if (userData) {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)
      loadAfiliadoData(parsedUser.id)
    }
  }, [])

  const loadAfiliadoData = async (userId: number) => {
    try {
      setLoading(true)

      console.log("🔄 Carregando dados do afiliado para usuário:", userId)

      // Buscar dados reais da API
      const response = await fetch(`/api/affiliate/dashboard?user_id=${userId}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        console.log("✅ Dados do afiliado carregados:", data)

        const afiliadoData: AfiliadoData = {
          ...data.afiliado,
          nome: user?.nome || data.afiliado.nome,
          email: user?.email || data.afiliado.email,
          link_afiliado: `${window.location.origin}/?ref=${data.afiliado.codigo_afiliado}`
        }

        setAfiliadoData(afiliadoData)
        setIndicacoes(data.indicacoes || [])
        setStats(data.stats || { totalIndicacoes: 0, comissaoTotal: 0, comissaoMes: 0, indicacoesPendentes: 0 })
      } else {
        throw new Error(data.message || "Erro ao carregar dados")
      }
    } catch (error) {
      console.error("Erro ao carregar dados do afiliado:", error)
      toast.error("Erro ao carregar dados do afiliado")
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success("Link copiado para a área de transferência!")
  }

  const shareLink = () => {
    if (navigator.share && afiliadoData) {
      navigator.share({
        title: "Sistema Bolão - Cadastre-se",
        text: "Cadastre-se no Sistema Bolão usando meu link de afiliado!",
        url: afiliadoData.link_afiliado
      })
    } else if (afiliadoData) {
      copyToClipboard(afiliadoData.link_afiliado)
    }
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pago":
        return <Badge className="bg-green-100 text-green-800">Pago</Badge>
      case "pendente":
        return <Badge variant="secondary">Pendente</Badge>
      case "cancelado":
        return <Badge variant="destructive">Cancelado</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Carregando painel de afiliado...</p>
        </div>
      </div>
    )
  }

  if (!afiliadoData) {
    return (
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Meu Afiliado</h1>
          <p className="text-gray-600 mt-2">Você ainda não é um afiliado</p>
        </div>
        
        <Card>
          <CardContent className="p-8 text-center">
            <UserPlus className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Torne-se um Afiliado</h3>
            <p className="text-gray-600 mb-6">
              Ganhe comissões indicando novos usuários para o Sistema Bolão
            </p>
            <Button className="bg-green-600 hover:bg-green-700">
              Solicitar Cadastro como Afiliado
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Meu Painel de Afiliado</h1>
        <p className="text-gray-600 mt-2">Gerencie suas indicações e acompanhe suas comissões</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Indicações</p>
                <p className="text-3xl font-bold text-gray-900">{stats.totalIndicacoes}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Comissão Total</p>
                <p className="text-3xl font-bold text-green-600">{formatCurrency(stats.comissaoTotal)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Comissão Este Mês</p>
                <p className="text-3xl font-bold text-blue-600">{formatCurrency(stats.comissaoMes)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pendentes</p>
                <p className="text-3xl font-bold text-orange-600">{stats.indicacoesPendentes}</p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Link de Afiliado */}
      <Card>
        <CardHeader>
          <CardTitle>Seu Link de Afiliado</CardTitle>
          <CardDescription>
            Compartilhe este link para ganhar comissões de {afiliadoData.percentual_comissao}% sobre cada novo usuário
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Input
                value={afiliadoData.link_afiliado}
                readOnly
                className="flex-1"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(afiliadoData.link_afiliado)}
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={shareLink}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex items-center space-x-4">
              <Badge variant="outline">Código: {afiliadoData.codigo_afiliado}</Badge>
              <Badge className="bg-green-100 text-green-800">
                Comissão: {afiliadoData.percentual_comissao}%
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="indicacoes" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="indicacoes">Minhas Indicações</TabsTrigger>
          <TabsTrigger value="historico">Histórico de Comissões</TabsTrigger>
        </TabsList>

        {/* Indicações Tab */}
        <TabsContent value="indicacoes">
          <Card>
            <CardHeader>
              <CardTitle>Minhas Indicações</CardTitle>
              <CardDescription>Usuários que se cadastraram através do seu link</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Usuário</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Comissão</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Data</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {indicacoes.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                          Nenhuma indicação encontrada
                        </TableCell>
                      </TableRow>
                    ) : (
                      indicacoes.map((indicacao) => (
                        <TableRow key={indicacao.id}>
                          <TableCell className="font-medium">{indicacao.usuario_nome}</TableCell>
                          <TableCell>{indicacao.usuario_email}</TableCell>
                          <TableCell className="font-medium text-green-600">
                            {formatCurrency(indicacao.valor_comissao)}
                          </TableCell>
                          <TableCell>{getStatusBadge(indicacao.status)}</TableCell>
                          <TableCell>{formatDate(indicacao.data_indicacao)}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Histórico Tab */}
        <TabsContent value="historico">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Comissões</CardTitle>
              <CardDescription>Todas as comissões recebidas e pendentes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {indicacoes.map((indicacao) => (
                  <div key={indicacao.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-1">
                        <h4 className="font-medium">Comissão - {indicacao.usuario_nome}</h4>
                        {getStatusBadge(indicacao.status)}
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>Indicação de usuário</span>
                        <span>{formatDate(indicacao.data_indicacao)}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className="font-bold text-lg text-green-600">
                        +{formatCurrency(indicacao.valor_comissao)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Como Funciona */}
      <Card>
        <CardHeader>
          <CardTitle>Como Funciona o Sistema de Afiliados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Share2 className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2">1. Compartilhe seu Link</h3>
              <p className="text-sm text-gray-600">
                Envie seu link de afiliado para amigos e conhecidos
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <UserPlus className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">2. Usuários se Cadastram</h3>
              <p className="text-sm text-gray-600">
                Quando alguém se cadastra pelo seu link, você ganha comissão
              </p>
            </div>

            <div className="text-center">
              <div className="bg-yellow-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <DollarSign className="h-8 w-8 text-yellow-600" />
              </div>
              <h3 className="font-semibold mb-2">3. Receba suas Comissões</h3>
              <p className="text-sm text-gray-600">
                Ganhe {afiliadoData.percentual_comissao}% sobre cada aposta dos seus indicados
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
