/* Estilos específicos para o modal da carteira */

/* Overlay responsivo */
.wallet-modal-overlay {
  position: fixed !important;
  inset: 0 !important;
  z-index: 50 !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(4px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0.5rem !important;
}

/* Container do modal responsivo */
.wallet-modal-container {
  background: white !important;
  border-radius: 0.75rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5) !important;
  width: 100% !important;
  max-width: 20rem !important; /* 320px para mobile */
  max-height: 95vh !important;
  overflow: hidden !important;
  margin: 0 auto !important;
  position: relative !important;
}

/* Responsividade para tablets */
@media (min-width: 640px) {
  .wallet-modal-overlay {
    padding: 1rem !important;
  }
  
  .wallet-modal-container {
    max-width: 28rem !important; /* 448px para tablet */
    max-height: 90vh !important;
  }
}

/* Responsividade para desktop pequeno */
@media (min-width: 768px) {
  .wallet-modal-container {
    max-width: 42rem !important; /* 672px para desktop pequeno */
  }
}

/* Responsividade para desktop grande */
@media (min-width: 1024px) {
  .wallet-modal-container {
    max-width: 56rem !important; /* 896px para desktop grande */
  }
}

/* Header do modal */
.wallet-modal-header {
  padding: 1rem !important;
  border-bottom: 1px solid #e2e8f0 !important;
  background: #1e293b !important;
  color: white !important;
}

@media (min-width: 640px) {
  .wallet-modal-header {
    padding: 1.5rem !important;
  }
}

/* Conteúdo do modal */
.wallet-modal-content {
  padding: 1rem !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
}

@media (min-width: 640px) {
  .wallet-modal-content {
    padding: 1.5rem !important;
  }
}

/* Botões responsivos */
.wallet-modal-button {
  padding: 0.5rem 1rem !important;
  font-size: 0.875rem !important;
}

@media (min-width: 640px) {
  .wallet-modal-button {
    padding: 0.75rem 1.5rem !important;
    font-size: 1rem !important;
  }
}

/* Inputs responsivos */
.wallet-modal-input {
  font-size: 1rem !important;
  padding: 0.75rem !important;
}

@media (max-width: 639px) {
  .wallet-modal-input {
    font-size: 16px !important; /* Evita zoom no iOS */
  }
}

/* Tabs responsivas */
.wallet-modal-tabs {
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
}

@media (min-width: 640px) {
  .wallet-modal-tabs {
    flex-direction: row !important;
    gap: 1rem !important;
  }
}

/* Cards responsivos */
.wallet-modal-card {
  margin-bottom: 1rem !important;
}

.wallet-modal-card .card-content {
  padding: 1rem !important;
}

@media (min-width: 640px) {
  .wallet-modal-card .card-content {
    padding: 1.5rem !important;
  }
}

/* QR Code responsivo */
.wallet-qr-container {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 1rem !important;
  padding: 1rem !important;
}

.wallet-qr-code {
  max-width: 200px !important;
  width: 100% !important;
  height: auto !important;
}

@media (min-width: 640px) {
  .wallet-qr-code {
    max-width: 250px !important;
  }
}

/* Histórico de transações responsivo */
.wallet-transaction-list {
  max-height: 300px !important;
  overflow-y: auto !important;
}

.wallet-transaction-item {
  padding: 0.75rem !important;
  border-bottom: 1px solid #e2e8f0 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.5rem !important;
}

@media (min-width: 640px) {
  .wallet-transaction-item {
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
  }
}

/* Valores monetários */
.wallet-currency {
  font-weight: 600 !important;
  font-size: 1.125rem !important;
}

@media (min-width: 640px) {
  .wallet-currency {
    font-size: 1.25rem !important;
  }
}

/* Loading states */
.wallet-loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 2rem !important;
}

/* Animações suaves */
.wallet-modal-container {
  animation: walletModalSlideIn 0.3s ease-out;
}

@keyframes walletModalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Melhorias para acessibilidade */
.wallet-modal-close-button {
  position: absolute !important;
  top: 1rem !important;
  right: 1rem !important;
  z-index: 10 !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50% !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.wallet-modal-close-button:hover {
  background: white !important;
  transform: scale(1.1) !important;
}

/* Scrollbar personalizada */
.wallet-modal-content::-webkit-scrollbar {
  width: 6px;
}

.wallet-modal-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.wallet-modal-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.wallet-modal-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Estados de foco para acessibilidade */
.wallet-modal-button:focus,
.wallet-modal-input:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* Melhorias para telas muito pequenas */
@media (max-width: 320px) {
  .wallet-modal-container {
    max-width: 95vw !important;
    margin: 0.25rem !important;
  }
  
  .wallet-modal-header,
  .wallet-modal-content {
    padding: 0.75rem !important;
  }
  
  .wallet-currency {
    font-size: 1rem !important;
  }
}

/* Melhorias para landscape em mobile */
@media (max-height: 500px) and (orientation: landscape) {
  .wallet-modal-container {
    max-height: 90vh !important;
  }
  
  .wallet-modal-content {
    max-height: 50vh !important;
  }
}
