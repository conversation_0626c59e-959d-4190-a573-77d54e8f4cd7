import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"
import { QueryBuilder } from "@/lib/database-utils"

// Forçar rota dinâmica
export const dynamic = 'force-dynamic'

export async function GET(request: Request) {
  try {
    // Evitar execução durante build - proteção aprimorada
    if (process.env.NEXT_PHASE === 'phase-production-build' ||
        process.env.NODE_ENV === 'production' && !request.headers.get('host') ||
        !request.url) {
      return NextResponse.json({ times: [], message: "Build mode" })
    }

    // Proteção adicional para URL parsing
    let searchParams
    try {
      searchParams = new URL(request.url).searchParams
    } catch (urlError) {
      console.log("⚠️ Erro ao processar URL durante build, retornando dados vazios")
      return NextResponse.json({ times: [], message: "URL parsing error during build" })
    }

    console.log("🔍 Buscando times...")

    await initializeDatabase()

    // Usar searchParams já validado acima
    const pais = searchParams.get("pais")
    const search = searchParams.get("search")
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 50

    // Query simplificada para MySQL
    let query = `
      SELECT
        t.*,
        COUNT(DISTINCT jc.id) as jogos_casa,
        COUNT(DISTINCT jf.id) as jogos_fora,
        COUNT(DISTINCT jc.id) + COUNT(DISTINCT jf.id) as total_jogos,
        COUNT(DISTINCT CASE WHEN (jc.status = 'agendado' AND jc.data_jogo >= NOW()) OR (jf.status = 'agendado' AND jf.data_jogo >= NOW()) THEN COALESCE(jc.id, jf.id) END) as jogos_futuros
      FROM times t
      LEFT JOIN jogos jc ON t.id = jc.time_casa_id
      LEFT JOIN jogos jf ON t.id = jf.time_fora_id
      WHERE 1=1
    `

    const params = []

    if (pais && pais !== 'todos') {
      query += ' AND t.pais = ?'
      params.push(pais)
    }

    if (search) {
      query += ' AND (t.nome LIKE ? OR t.nome_curto LIKE ?)'
      params.push(`%${search}%`, `%${search}%`)
    }

    query += ' GROUP BY t.id ORDER BY t.nome ASC'

    if (limit && limit > 0) {
      query += ' LIMIT ?'
      params.push(limit)
    }

    const times = await executeQuery(query, params)

    // Buscar países disponíveis
    const paises = await executeQuery(`
      SELECT DISTINCT t.pais, COUNT(t.id) as total
      FROM times t
      WHERE t.pais IS NOT NULL
      GROUP BY t.pais
      ORDER BY 
        CASE WHEN t.pais = 'Brasil' THEN 0 ELSE 1 END,
        total DESC,
        t.pais ASC
    `)

    // Estatísticas gerais
    const stats = await executeQuery(`
      SELECT 
        COUNT(DISTINCT t.id) as total,
        COUNT(DISTINCT CASE WHEN t.pais = 'Brasil' THEN t.id END) as brasileiros,
        COUNT(DISTINCT CASE WHEN t.pais != 'Brasil' THEN t.id END) as internacionais,
        COUNT(DISTINCT CASE WHEN t.logo_url IS NOT NULL THEN t.id END) as com_logo
      FROM times t
    `)

    return NextResponse.json({
      success: true,
      times: times || [],
      total: times?.length || 0,
      paises: paises || [],
      stats: stats[0] || { total: 0, brasileiros: 0, internacionais: 0, com_logo: 0 },
      filters: {
        pais,
        search,
        limit
      }
    })

  } catch (error) {
    console.error("❌ Erro ao buscar times:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        times: [],
        total: 0,
        paises: [],
        stats: { total: 0, brasileiros: 0, internacionais: 0, com_logo: 0 }
      },
      { status: 500 }
    )
  }
}
