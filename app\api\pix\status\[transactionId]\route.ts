import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export async function GET(
  request: NextRequest,
  { params }: { params: { transactionId: string } }
) {
  try {
    const { transactionId } = params

    if (!transactionId) {
      return NextResponse.json(
        { error: 'Transaction ID é obrigatório' },
        { status: 400 }
      )
    }

    await initializeDatabase()

    // Buscar pagamento pelo transaction_id ou order_id
    const payments = await executeQuery(`
      SELECT 
        transaction_id,
        order_id,
        qr_code_payment_id,
        amount,
        status,
        end_to_end_id,
        error_message,
        created_at,
        last_updated_at
      FROM pagamentos 
      WHERE transaction_id = ? OR order_id = ?
      ORDER BY created_at DESC
      LIMIT 1
    `, [transactionId, transactionId])

    if (payments.length === 0) {
      return NextResponse.json(
        { error: 'Pagamento não encontrado' },
        { status: 404 }
      )
    }

    const payment = payments[0]

    return NextResponse.json({
      success: true,
      data: {
        transaction_id: payment.transaction_id,
        order_id: payment.order_id,
        qr_code_payment_id: payment.qr_code_payment_id,
        amount: parseFloat(payment.amount),
        status: payment.status,
        end_to_end_id: payment.end_to_end_id,
        error_message: payment.error_message,
        created_at: payment.created_at,
        last_updated_at: payment.last_updated_at,
        is_paid: payment.status === 'PAID',
        is_pending: payment.status === 'PENDING',
        is_failed: ['FAILED', 'DECLINED'].includes(payment.status)
      }
    })

  } catch (error) {
    console.error('❌ Erro ao verificar status do pagamento:', error)
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    )
  }
}
