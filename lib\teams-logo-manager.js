/**
 * Sistema de Gerenciamento de Logos dos Times
 * Mantém as imagens organizadas e corretas mesmo após atualizações da API
 */

// Mapeamento fixo dos times brasileiros com seus logos corretos
export const TEAMS_LOGO_MAP = {
  // Times principais do Brasileirão
  'cruzeiro': {
    names: ['Cruzeiro', 'Cruzeiro Esporte Clube', 'Cruzeiro EC'],
    logo: '/images/teams/6693.png',
    apiId: 6693,
    priority: 1
  },
  'botafogo': {
    names: ['Botafogo', 'Botafogo de Futebol e Regatas', 'Botafogo FR'],
    logo: '/images/teams/1770.png',
    apiId: 1770,
    priority: 1
  },
  'gremio': {
    names: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Grêmio Foot-Ball Porto Alegrense', 'Grêmio FBPA'],
    logo: '/images/teams/1767.png',
    apiId: 1767,
    priority: 1
  },
  'sport': {
    names: ['Sport Club do Recife', 'Sport', 'SC Recife'],
    logo: '/images/teams/1909.png',
    apiId: 1909,
    priority: 1
  },
  'ceara': {
    names: ['Ceará', 'Ceará Sporting Club', 'Ceará SC'],
    logo: '/images/teams/1905.png',
    apiId: 1905,
    priority: 1
  },
  'flamengo': {
    names: ['Flamengo', 'Clube de Regatas do Flamengo', 'CR Flamengo'],
    logo: '/images/teams/1901.png',
    apiId: 1901,
    priority: 1
  },
  'palmeiras': {
    names: ['Palmeiras', 'Sociedade Esportiva Palmeiras', 'SE Palmeiras'],
    logo: '/images/teams/1900.png',
    apiId: 1900,
    priority: 1
  },
  'sao_paulo': {
    names: ['São Paulo', 'São Paulo Futebol Clube', 'SPFC'],
    logo: '/images/teams/1898.png',
    apiId: 1898,
    priority: 1
  },
  'corinthians': {
    names: ['Corinthians', 'Sport Club Corinthians Paulista', 'SCCP'],
    logo: '/images/teams/1911.png',
    apiId: 1911,
    priority: 1
  },
  'fluminense': {
    names: ['Fluminense', 'Fluminense Football Club', 'Flu'],
    logo: '/images/teams/1902.png',
    apiId: 1902,
    priority: 1
  },
  'atletico_mg': {
    names: ['Atlético-MG', 'Clube Atlético Mineiro', 'Atlético Mineiro', 'Atlético', 'CAM'],
    logo: '/images/teams/1899.png',
    apiId: 1899,
    priority: 1
  },
  'internacional': {
    names: ['Internacional', 'Sport Club Internacional', 'Inter'],
    logo: '/images/teams/1912.png',
    apiId: 1912,
    priority: 1
  },
  'santos': {
    names: ['Santos', 'Santos Futebol Clube', 'Santos FC'],
    logo: '/images/teams/1908.png',
    apiId: 1908,
    priority: 1
  },
  'vasco': {
    names: ['Vasco', 'Club de Regatas Vasco da Gama', 'Vasco da Gama'],
    logo: '/images/teams/1897.png',
    apiId: 1897,
    priority: 1
  }
}

/**
 * Encontra o logo correto para um time baseado no nome
 */
export function findTeamLogo(teamName) {
  if (!teamName) return null
  
  const normalizedName = teamName.toLowerCase().trim()
  
  // Buscar no mapeamento fixo
  for (const [key, team] of Object.entries(TEAMS_LOGO_MAP)) {
    for (const name of team.names) {
      if (normalizedName.includes(name.toLowerCase()) || 
          name.toLowerCase().includes(normalizedName)) {
        return team.logo
      }
    }
  }
  
  return null
}

/**
 * Normaliza o nome do time para busca
 */
export function normalizeTeamName(teamName) {
  if (!teamName) return ''
  
  return teamName
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/[áàâã]/g, 'a')
    .replace(/[éèê]/g, 'e')
    .replace(/[íìî]/g, 'i')
    .replace(/[óòôõ]/g, 'o')
    .replace(/[úùû]/g, 'u')
    .replace(/ç/g, 'c')
}

/**
 * Força a atualização de todos os times no banco com logos corretos
 */
export async function forceUpdateTeamLogos(executeQuery) {
  console.log('🔄 Forçando atualização de logos dos times...')
  
  let totalUpdated = 0
  
  for (const [key, team] of Object.entries(TEAMS_LOGO_MAP)) {
    try {
      // Atualizar todos os registros que correspondem aos nomes do time
      for (const name of team.names) {
        const result = await executeQuery(
          'UPDATE times SET logo_url = ? WHERE nome LIKE ? OR nome_curto LIKE ?',
          [team.logo, `%${name}%`, `%${name}%`]
        )
        
        const affected = result.affectedRows || result.changes || 0
        if (affected > 0) {
          console.log(`✅ ${name}: ${affected} registros atualizados com ${team.logo}`)
          totalUpdated += affected
        }
      }
    } catch (error) {
      console.error(`❌ Erro ao atualizar ${key}:`, error.message)
    }
  }
  
  console.log(`🎉 Total de registros atualizados: ${totalUpdated}`)
  return totalUpdated
}

/**
 * Verifica e corrige logos após sincronização da API
 */
export async function validateAndFixLogos(executeQuery) {
  console.log('🔍 Validando e corrigindo logos após sincronização...')
  
  // Buscar todos os times que não têm logo local
  const timesWithoutLocalLogo = await executeQuery(`
    SELECT id, nome, nome_curto, logo_url 
    FROM times 
    WHERE logo_url NOT LIKE '/images/teams/%' 
    OR logo_url IS NULL
  `)
  
  console.log(`📊 Encontrados ${timesWithoutLocalLogo.length} times sem logo local`)
  
  let fixed = 0
  
  for (const time of timesWithoutLocalLogo) {
    const correctLogo = findTeamLogo(time.nome) || findTeamLogo(time.nome_curto)
    
    if (correctLogo) {
      try {
        await executeQuery(
          'UPDATE times SET logo_url = ? WHERE id = ?',
          [correctLogo, time.id]
        )
        
        console.log(`✅ ${time.nome} (ID: ${time.id}) corrigido: ${correctLogo}`)
        fixed++
      } catch (error) {
        console.error(`❌ Erro ao corrigir ${time.nome}:`, error.message)
      }
    }
  }
  
  console.log(`🎉 ${fixed} logos corrigidos`)
  return fixed
}

/**
 * Hook para ser executado após qualquer sincronização da API
 */
export async function postSyncHook(executeQuery) {
  console.log('🔧 Executando hook pós-sincronização...')
  
  try {
    // 1. Validar e corrigir logos
    await validateAndFixLogos(executeQuery)
    
    // 2. Forçar atualização dos times principais
    await forceUpdateTeamLogos(executeQuery)
    
    // 3. Verificar integridade final
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN logo_url LIKE '/images/teams/%' THEN 1 END) as local_logos,
        COUNT(CASE WHEN logo_url LIKE 'https://%' THEN 1 END) as external_logos,
        COUNT(CASE WHEN logo_url IS NULL THEN 1 END) as no_logos
      FROM times
    `)
    
    console.log('📊 Estatísticas finais:')
    console.log(`  Total de times: ${stats[0].total}`)
    console.log(`  Logos locais: ${stats[0].local_logos}`)
    console.log(`  Logos externos: ${stats[0].external_logos}`)
    console.log(`  Sem logo: ${stats[0].no_logos}`)
    
    return true
  } catch (error) {
    console.error('❌ Erro no hook pós-sincronização:', error.message)
    return false
  }
}
