"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { DollarSign, TrendingUp, Target, LogOut, Plus, Calendar, User } from "lucide-react"
import { toast } from "sonner"

interface Cambista {
  id: number
  nome: string
  email: string
  comissao: number
}

interface Aposta {
  id: number
  cliente: string
  telefone: string
  valor: number
  jogos: string[]
  data: string
  status: "pendente" | "ganha" | "perdida"
}

export default function CambistaDashboard() {
  const [cambista, setCambista] = useState<Cambista | null>(null)
  const [apostas, setApostas] = useState<Aposta[]>([])
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)

  const loadApostasReais = async () => {
    try {
      const response = await fetch('/api/cambista/apostas')
      if (response.ok) {
        const data = await response.json()
        setApostas(data.apostas || [])
      } else {
        console.error('Erro ao carregar apostas')
        setApostas([])
      }
    } catch (error) {
      console.error('Erro ao carregar apostas:', error)
      setApostas([])
    } finally {
      setLoading(false)
    }
  }
  const [formData, setFormData] = useState({
    cliente: "",
    telefone: "",
    valor: "",
    jogos: "",
  })
  const router = useRouter()

  useEffect(() => {
    const cambistaData = localStorage.getItem("cambista")
    if (!cambistaData) {
      router.push("/cambista/login")
      return
    }

    const parsedCambista = JSON.parse(cambistaData)
    setCambista(parsedCambista)

    // Carregar apostas reais do banco de dados
    loadApostasReais()
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem("cambista")
    router.push("/cambista/login")
  }

  const handleCreateAposta = () => {
    if (!formData.cliente || !formData.telefone || !formData.valor || !formData.jogos) {
      toast.error("Preencha todos os campos")
      return
    }

    const novaAposta: Aposta = {
      id: Date.now(),
      cliente: formData.cliente,
      telefone: formData.telefone,
      valor: Number.parseFloat(formData.valor),
      jogos: formData.jogos.split(",").map((jogo) => jogo.trim()),
      data: new Date().toISOString().split("T")[0],
      status: "pendente",
    }

    setApostas([novaAposta, ...apostas])
    setFormData({ cliente: "", telefone: "", valor: "", jogos: "" })
    setIsCreateModalOpen(false)
    toast.success("Aposta registrada com sucesso!")
  }

  if (!cambista) {
    return <div>Carregando...</div>
  }

  const totalVendas = apostas.reduce((acc, aposta) => acc + aposta.valor, 0)
  const vendasHoje = apostas
    .filter((aposta) => aposta.data === new Date().toISOString().split("T")[0])
    .reduce((acc, aposta) => acc + aposta.valor, 0)
  const comissaoGanha = totalVendas * (cambista.comissao / 100)
  const apostasGanhas = apostas.filter((aposta) => aposta.status === "ganha").length

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{cambista.nome}</h1>
                <p className="text-sm text-gray-500">Cambista - {cambista.comissao}% comissão</p>
              </div>
            </div>
            <Button variant="outline" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Sair
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Vendas Hoje</p>
                    <p className="text-3xl font-bold text-gray-900">
                      R$ {vendasHoje.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                  <Calendar className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Vendas</p>
                    <p className="text-3xl font-bold text-gray-900">
                      R$ {totalVendas.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Comissão Ganha</p>
                    <p className="text-3xl font-bold text-gray-900">
                      R$ {comissaoGanha.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Apostas Ganhas</p>
                    <p className="text-3xl font-bold text-gray-900">{apostasGanhas}</p>
                  </div>
                  <Target className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Ações Rápidas */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Minhas Apostas</CardTitle>
                  <CardDescription>Gerencie as apostas dos seus clientes</CardDescription>
                </div>
                <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Nova Aposta
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Registrar Nova Aposta</DialogTitle>
                      <DialogDescription>Preencha os dados da aposta do cliente</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Label htmlFor="cliente">Nome do Cliente</Label>
                        <Input
                          id="cliente"
                          value={formData.cliente}
                          onChange={(e) => setFormData({ ...formData, cliente: e.target.value })}
                          placeholder="Digite o nome do cliente"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="telefone">Telefone</Label>
                        <Input
                          id="telefone"
                          value={formData.telefone}
                          onChange={(e) => setFormData({ ...formData, telefone: e.target.value })}
                          placeholder="(11) 99999-9999"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="valor">Valor da Aposta (R$)</Label>
                        <Input
                          id="valor"
                          type="number"
                          step="0.01"
                          min="1"
                          value={formData.valor}
                          onChange={(e) => setFormData({ ...formData, valor: e.target.value })}
                          placeholder="0.00"
                        />
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="jogos">Jogos (separados por vírgula)</Label>
                        <Input
                          id="jogos"
                          value={formData.jogos}
                          onChange={(e) => setFormData({ ...formData, jogos: e.target.value })}
                          placeholder="Time A x Time B, Time C x Time D"
                        />
                      </div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                        Cancelar
                      </Button>
                      <Button onClick={handleCreateAposta}>Registrar Aposta</Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Cliente</TableHead>
                      <TableHead>Telefone</TableHead>
                      <TableHead>Valor</TableHead>
                      <TableHead>Jogos</TableHead>
                      <TableHead>Data</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Comissão</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {apostas.map((aposta) => (
                      <TableRow key={aposta.id}>
                        <TableCell className="font-medium">{aposta.cliente}</TableCell>
                        <TableCell>{aposta.telefone}</TableCell>
                        <TableCell>R$ {aposta.valor.toLocaleString("pt-BR", { minimumFractionDigits: 2 })}</TableCell>
                        <TableCell>
                          <div className="max-w-xs">{aposta.jogos.join(", ")}</div>
                        </TableCell>
                        <TableCell>{new Date(aposta.data).toLocaleDateString("pt-BR")}</TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              aposta.status === "ganha"
                                ? "default"
                                : aposta.status === "perdida"
                                  ? "destructive"
                                  : "secondary"
                            }
                            className={
                              aposta.status === "ganha"
                                ? "bg-green-100 text-green-800"
                                : aposta.status === "perdida"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {aposta.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          R${" "}
                          {(aposta.valor * (cambista.comissao / 100)).toLocaleString("pt-BR", {
                            minimumFractionDigits: 2,
                          })}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
