/**
 * Remove caracteres não numéricos do CPF
 */
export function cleanCPF(cpf: string): string {
  return cpf.replace(/\D/g, "")
}

/**
 * Formata CPF com pontos e hífen
 */
export function formatCPF(cpf: string): string {
  const cleaned = cleanCPF(cpf)

  if (cleaned.length <= 3) {
    return cleaned
  } else if (cleaned.length <= 6) {
    return `${cleaned.slice(0, 3)}.${cleaned.slice(3)}`
  } else if (cleaned.length <= 9) {
    return `${cleaned.slice(0, 3)}.${cleaned.slice(3, 6)}.${cleaned.slice(6)}`
  } else {
    return `${cleaned.slice(0, 3)}.${cleaned.slice(3, 6)}.${cleaned.slice(6, 9)}-${cleaned.slice(9, 11)}`
  }
}

/**
 * Valida CPF usando o algoritmo oficial
 */
export function validateCPF(cpf: string): boolean {
  const cleaned = cleanCPF(cpf)

  // Verificar se tem 11 dígitos
  if (cleaned.length !== 11) {
    return false
  }

  // Verificar se todos os dígitos são iguais
  if (/^(\d)\1{10}$/.test(cleaned)) {
    return false
  }

  // Calcular primeiro dígito verificador
  let sum = 0
  for (let i = 0; i < 9; i++) {
    sum += Number.parseInt(cleaned.charAt(i)) * (10 - i)
  }
  let remainder = sum % 11
  const firstDigit = remainder < 2 ? 0 : 11 - remainder

  // Verificar primeiro dígito
  if (Number.parseInt(cleaned.charAt(9)) !== firstDigit) {
    return false
  }

  // Calcular segundo dígito verificador
  sum = 0
  for (let i = 0; i < 10; i++) {
    sum += Number.parseInt(cleaned.charAt(i)) * (11 - i)
  }
  remainder = sum % 11
  const secondDigit = remainder < 2 ? 0 : 11 - remainder

  // Verificar segundo dígito
  return Number.parseInt(cleaned.charAt(10)) === secondDigit
}

/**
 * Retorna mensagem de erro específica para CPF
 */
export function getCPFErrorMessage(cpf: string): string | null {
  if (!cpf.trim()) {
    return "CPF é obrigatório"
  }

  const cleaned = cleanCPF(cpf)

  if (cleaned.length < 11) {
    return "CPF deve ter 11 dígitos"
  }

  if (cleaned.length > 11) {
    return "CPF deve ter apenas 11 dígitos"
  }

  if (/^(\d)\1{10}$/.test(cleaned)) {
    return "CPF não pode ter todos os dígitos iguais"
  }

  if (!validateCPF(cpf)) {
    return "CPF inválido"
  }

  return null
}
