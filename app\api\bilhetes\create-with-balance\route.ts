import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const {
      user_id,
      apostas,
      valor,
      client_name,
      client_email,
      client_document,
      cambista_id
    } = body

    console.log("🎫 Iniciando criação de bilhete com saldo:", {
      user_id,
      valor,
      apostas: apostas?.length,
      client_name,
      client_email,
      client_document,
      cambista_id
    })

    // Validações básicas
    if (!user_id) {
      return NextResponse.json({ 
        success: false,
        error: "Usuário deve estar logado para criar bilhetes" 
      }, { status: 400 })
    }

    if (!apostas || !Array.isArray(apostas) || apostas.length === 0) {
      return NextResponse.json({ 
        success: false,
        error: "Apostas são obrigatórias" 
      }, { status: 400 })
    }

    if (!valor || valor <= 0) {
      return NextResponse.json({ 
        success: false,
        error: "Valor deve ser maior que zero" 
      }, { status: 400 })
    }

    if (!client_name || !client_email || !client_document) {
      return NextResponse.json({ 
        success: false,
        error: "Dados do cliente são obrigatórios" 
      }, { status: 400 })
    }

    // Verificar se usuário existe e tem saldo suficiente
    const usuario = await executeQuery(`
      SELECT id, nome, email, saldo FROM usuarios WHERE id = ?
    `, [user_id])

    if (!usuario || usuario.length === 0) {
      return NextResponse.json({ 
        success: false,
        error: "Usuário não encontrado" 
      }, { status: 404 })
    }

    const saldoAtual = parseFloat(usuario[0].saldo) || 0
    const valorBilhete = parseFloat(valor)

    if (saldoAtual < valorBilhete) {
      return NextResponse.json({ 
        success: false,
        error: `Saldo insuficiente. Saldo atual: R$ ${saldoAtual.toFixed(2)}, Valor necessário: R$ ${valorBilhete.toFixed(2)}` 
      }, { status: 400 })
    }

    // Gerar código único do bilhete
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 9)
    const codigoBilhete = `BLT${timestamp}${random}`.toUpperCase()

    console.log("🎯 Código do bilhete gerado:", codigoBilhete)

    // Iniciar transação
    await executeQuery('START TRANSACTION')

    try {
      // 1. Criar o bilhete
      const bilheteResult = await executeQuery(`
        INSERT INTO bilhetes (
          codigo, usuario_id, cambista_id, usuario_nome, usuario_email, usuario_cpf,
          valor_total, quantidade_apostas, status, pago_com_saldo
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pago', TRUE)
      `, [
        codigoBilhete,
        user_id,
        cambista_id || null,
        client_name,
        client_email,
        client_document,
        valorBilhete,
        apostas.length
      ])

      const bilheteId = (bilheteResult as any).insertId
      console.log("✅ Bilhete inserido no banco com ID:", bilheteId)

      // 2. Inserir apostas do bilhete
      if (apostas && apostas.length > 0) {
        for (const aposta of apostas) {
          await executeQuery(`
            INSERT INTO bilhete_apostas (bilhete_id, match_id, resultado)
            VALUES (?, ?, ?)
          `, [bilheteId, aposta.jogo_id || aposta.match_id, aposta.palpite || aposta.resultado])
        }
        console.log(`📊 ${apostas.length} apostas inseridas para o bilhete`)
      }

      // 3. Descontar do saldo do usuário
      const novoSaldo = saldoAtual - valorBilhete

      await executeQuery(`
        UPDATE usuarios SET saldo = ? WHERE id = ?
      `, [novoSaldo, user_id])

      // 4. Registrar transação de saldo
      await executeQuery(`
        INSERT INTO saldo_transacoes (
          usuario_id, tipo, valor, saldo_anterior, saldo_posterior,
          descricao, bilhete_id, status
        ) VALUES (?, 'compra_bilhete', ?, ?, ?, ?, ?, 'confirmado')
      `, [
        user_id,
        valorBilhete,
        saldoAtual,
        novoSaldo,
        `Compra de bilhete ${codigoBilhete}`,
        bilheteId
      ])

      // Confirmar transação
      await executeQuery('COMMIT')

      const bilheteCompleto = {
        id: bilheteId,
        codigo: codigoBilhete,
        valor: valorBilhete,
        status: "pago",
        pago_com_saldo: true,
        apostas_count: apostas.length,
        saldo_anterior: saldoAtual,
        saldo_posterior: novoSaldo,
        created_at: new Date().toISOString()
      }

      console.log("🎉 Bilhete criado com saldo com sucesso:", bilheteCompleto)

      return NextResponse.json({
        success: true,
        message: "Bilhete criado e pago com saldo com sucesso",
        bilhete: bilheteCompleto
      })

    } catch (error) {
      await executeQuery('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error("❌ Erro ao criar bilhete com saldo:", error)
    
    let errorMessage = "Erro interno do servidor"
    if (error instanceof Error) {
      if (error.message.includes('Saldo insuficiente')) {
        errorMessage = error.message
      } else if (error.message.includes('Usuário não encontrado')) {
        errorMessage = "Usuário não encontrado"
      }
    }

    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 })
  }
}

// Endpoint para verificar se usuário tem saldo suficiente
export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const valor = searchParams.get('valor')

    if (!userId || !valor) {
      return NextResponse.json({
        success: false,
        error: 'user_id e valor são obrigatórios'
      }, { status: 400 })
    }

    // Buscar saldo do usuário
    const usuario = await executeQuery(`
      SELECT id, nome, saldo FROM usuarios WHERE id = ?
    `, [userId])

    if (!usuario || usuario.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não encontrado'
      }, { status: 404 })
    }

    const saldoAtual = parseFloat(usuario[0].saldo) || 0
    const valorNecessario = parseFloat(valor)
    const temSaldoSuficiente = saldoAtual >= valorNecessario

    return NextResponse.json({
      success: true,
      usuario: {
        id: usuario[0].id,
        nome: usuario[0].nome,
        saldo: saldoAtual
      },
      valor_necessario: valorNecessario,
      tem_saldo_suficiente: temSaldoSuficiente,
      diferenca: temSaldoSuficiente ? 0 : valorNecessario - saldoAtual
    })

  } catch (error) {
    console.error('❌ Erro ao verificar saldo:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}
