# Correção: "Too many connections" MySQL

## 🚨 Problema
O erro "Too many connections" ocorre quando o MySQL atinge o limite máximo de conexões simultâneas.

## ✅ Soluções Implementadas

### 1. **Configuração de Pool Otimizada**
- Reduzido `connectionLimit` para 5 conexões por pool
- Configurado `maxIdle: 2` para limitar conexões inativas
- Adicionado `waitForConnections: true` para aguardar conexões disponíveis
- Timeout aumentado para 60 segundos

### 2. **Retry Automático com Backoff**
- Implementado retry automático em caso de erro
- Aguarda progressivamente mais tempo entre tentativas
- Retorna dados básicos em vez de falhar completamente

### 3. **Liberação Automática de Conexões**
- Conexões são sempre liberadas no bloco `finally`
- Timeout para aquisição de conexões
- Limpeza automática de conexões inativas

## 🔧 Configurações MySQL Recomendadas

Execute no MySQL:

```sql
-- Aumentar limite de conexões
SET GLOBAL max_connections = 200;

-- Limite por usuário
SET GLOBAL max_user_connections = 50;

-- Timeout para conexões inativas (5 minutos)
SET GLOBAL wait_timeout = 300;
SET GLOBAL interactive_timeout = 300;

-- Timeout para estabelecer conexão
SET GLOBAL connect_timeout = 60;
```

Ou execute o arquivo de otimização:
```bash
mysql -u root -p < config/mysql-optimization.sql
```

## 📊 Monitoramento

### Verificar conexões ativas:
```sql
SHOW PROCESSLIST;
```

### Verificar status:
```sql
SHOW STATUS WHERE Variable_name LIKE '%connect%';
```

### Verificar configurações:
```sql
SHOW VARIABLES WHERE Variable_name IN ('max_connections', 'max_user_connections');
```

## 🛠️ Scripts de Manutenção

### 1. Monitorar conexões:
```bash
node scripts/monitor-mysql-connections.js
```

### 2. Limpar conexões inativas:
```sql
-- Ver conexões inativas
SELECT ID, USER, HOST, TIME, COMMAND 
FROM INFORMATION_SCHEMA.PROCESSLIST 
WHERE COMMAND = 'Sleep' AND TIME > 300;

-- Matar conexões inativas (cuidado!)
-- KILL <ID>;
```

## 🔄 Arquivos Modificados

1. **`lib/database-config.ts`** - Pool otimizado + retry automático
2. **`lib/database-config.js`** - Mesmas otimizações para JS
3. **`lib/database.js`** - Configurações consistentes
4. **`lib/connection-limiter.ts`** - Limitador de conexões por IP
5. **`scripts/monitor-mysql-connections.js`** - Script de monitoramento
6. **`config/mysql-optimization.sql`** - Configurações MySQL

## 📈 Melhorias Implementadas

### Antes:
- ❌ connectionLimit: 10+ (muito alto)
- ❌ Sem retry automático
- ❌ Conexões não liberadas adequadamente
- ❌ Sem tratamento de "Too many connections"

### Depois:
- ✅ connectionLimit: 5 (otimizado)
- ✅ Retry automático com backoff
- ✅ Liberação garantida de conexões
- ✅ Fallback para dados básicos
- ✅ Monitoramento e limpeza automática

## 🚀 Próximos Passos

1. **Execute as configurações MySQL** usando o arquivo SQL
2. **Monitore as conexões** com o script de monitoramento
3. **Verifique os logs** para confirmar que o erro diminuiu
4. **Ajuste os limites** se necessário baseado no uso real

## 📞 Suporte

Se o problema persistir:
1. Verifique se o MySQL tem recursos suficientes
2. Considere aumentar `max_connections` no MySQL
3. Monitore o uso de memória do servidor
4. Verifique se há queries lentas bloqueando conexões

## 🎯 Resultado Esperado

- ✅ Redução drástica de erros "Too many connections"
- ✅ Melhor performance geral da aplicação
- ✅ Conexões mais estáveis e confiáveis
- ✅ Fallback gracioso em caso de problemas
