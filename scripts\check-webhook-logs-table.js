#!/usr/bin/env node

/**
 * Script para verificar a estrutura da tabela webhook_logs
 */

import { executeQuery } from '../lib/database-config.js'

async function checkWebhookLogsTable() {
  try {
    console.log('🔍 Verificando estrutura da tabela webhook_logs...')
    console.log('')

    // Verificar estrutura da tabela
    const structure = await executeQuery('DESCRIBE webhook_logs')
    
    console.log('📋 ESTRUTURA DA TABELA WEBHOOK_LOGS:')
    console.log('===================================')
    structure.forEach(column => {
      console.log(`   ${column.Field.padEnd(20)} | ${column.Type.padEnd(20)} | ${column.Null.padEnd(5)} | ${column.Key.padEnd(5)} | ${column.Default || 'NULL'}`)
    })
    
    console.log('')
    console.log('📊 COLUNAS DISPONÍVEIS:')
    const availableColumns = structure.map(col => col.Field)
    availableColumns.forEach(col => {
      console.log(`   ✅ ${col}`)
    })
    
    console.log('')
    console.log('🔍 VERIFICANDO DADOS EXISTENTES:')
    
    const logs = await executeQuery('SELECT COUNT(*) as total FROM webhook_logs')
    console.log(`   📊 Total de logs: ${logs[0].total}`)
    
    if (logs[0].total > 0) {
      const recentLogs = await executeQuery(`
        SELECT id, transaction_id, status, processed_at 
        FROM webhook_logs 
        ORDER BY processed_at DESC 
        LIMIT 5
      `)
      
      console.log('')
      console.log('📋 ÚLTIMOS 5 LOGS:')
      recentLogs.forEach(log => {
        console.log(`   - ID: ${log.id}, Transaction: ${log.transaction_id}, Status: ${log.status}, Data: ${log.processed_at}`)
      })
    }
    
    console.log('')
    console.log('🔧 QUERY CORRIGIDA PARA INSERIR LOGS:')
    console.log('====================================')
    
    const insertColumns = [
      'transaction_id', 'order_id', 'amount', 'status', 
      'end_to_end_id', 'webhook_data', 'processed_at'
    ].filter(col => availableColumns.includes(col))
    
    const placeholders = insertColumns.map(() => '?').join(', ')
    
    console.log(`INSERT INTO webhook_logs (${insertColumns.join(', ')}) VALUES (${placeholders})`)
    
    console.log('')
    console.log('📋 EXEMPLO DE DADOS:')
    console.log('[')
    console.log('  webhookData.transaction_id,')
    console.log('  webhookData.order_id,')
    console.log('  webhookData.amount,')
    console.log('  webhookData.status,')
    console.log('  webhookData.end_to_end_id,')
    console.log('  JSON.stringify(webhookData),')
    console.log('  new Date()')
    console.log(']')

  } catch (error) {
    console.error('❌ Erro ao verificar tabela:', error)
    throw error
  }
}

// Executar
checkWebhookLogsTable()
  .then(() => {
    console.log('✅ Verificação concluída!')
    process.exit(0)
  })
  .catch(error => {
    console.error('💥 Erro fatal:', error)
    process.exit(1)
  })
