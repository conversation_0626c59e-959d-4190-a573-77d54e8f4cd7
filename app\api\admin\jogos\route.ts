import { type NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const filters = {
      campeonato_id: searchParams.get("campeonato_id") || undefined,
      status: searchParams.get("status") || undefined,
      data_inicio: searchParams.get("data_inicio") || undefined,
      data_fim: searchParams.get("data_fim") || undefined,
      limit: searchParams.get("limit") || undefined,
    }

    // Buscar jogos com filtros
    const jogos = await executeQuery(`
      SELECT
        j.*,
        tc.nome as time_casa_nome,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora_nome,
        tf.logo_url as time_fora_logo,
        c.nome as campeonato_nome
      FROM jogos j
      JOIN times tc ON j.time_casa_id = tc.id
      JOIN times tf ON j.time_fora_id = tf.id
      JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE 1=1
      ${filters.campeonato_id ? 'AND j.campeonato_id = ?' : ''}
      ${filters.status ? 'AND j.status = ?' : ''}
      ${filters.data_inicio ? 'AND j.data_jogo >= ?' : ''}
      ${filters.data_fim ? 'AND j.data_jogo <= ?' : ''}
      ORDER BY j.data_jogo DESC
      ${filters.limit ? 'LIMIT ?' : 'LIMIT 50'}
    `, [
      filters.campeonato_id,
      filters.status,
      filters.data_inicio,
      filters.data_fim,
      filters.limit
    ].filter(Boolean))

    // Buscar estatísticas
    const statsResult = await executeQuery(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN DATE(data_jogo) = CURDATE() THEN 1 END) as hoje,
        COUNT(CASE WHEN data_jogo >= CURDATE() AND data_jogo < DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as semana,
        COUNT(CASE WHEN status = 'em_andamento' THEN 1 END) as aoVivo
      FROM jogos
    `)

    const stats = statsResult[0] || { hoje: 0, semana: 0, total: 0, aoVivo: 0 }

    return NextResponse.json(
      {
        success: true,
        jogos: jogos || [],
        stats: stats || { hoje: 0, semana: 0, total: 0, aoVivo: 0 },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  } catch (error) {
    console.error("Erro ao buscar jogos:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error)?.message || "Erro desconhecido",
        jogos: [],
        stats: { hoje: 0, semana: 0, total: 0, aoVivo: 0 },
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }
}
