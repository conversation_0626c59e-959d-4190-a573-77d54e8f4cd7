import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export async function POST(request: NextRequest) {
  try {
    const webhookData = await request.json()

    console.log('🔔 Webhook PIX recebido:', {
      transaction_id: webhookData.transaction_id,
      order_id: webhookData.order_id,
      amount: webhookData.amount,
      status: webhookData.status,
      end_to_end_id: webhookData.end_to_end_id,
      last_updated_at: webhookData.last_updated_at,
      error: webhookData.error
    })

    const {
      qr_code_payment_id,
      transaction_id,
      order_id,
      amount,
      status,
      end_to_end_id,
      last_updated_at,
      error
    } = webhookData

    // Validar dados obrigatórios conforme documentação
    if (!transaction_id || !status) {
      console.error('❌ Dados obrigatórios ausentes no webhook')
      return NextResponse.json(
        { error: 'transaction_id e status são obrigatórios' },
        { status: 400 }
      )
    }

    try {
      await initializeDatabase()

      // Registrar log do webhook recebido
      await executeQuery(`
        INSERT INTO webhook_logs (
          transaction_id,
          order_id,
          amount,
          status,
          end_to_end_id,
          webhook_data,
          processed_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        transaction_id,
        order_id || null,
        amount || null,
        status,
        end_to_end_id || null,
        JSON.stringify({
          ...webhookData,
          source: 'pix_webhook_generic',
          source_ip: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown'
        })
      ])

      console.log("📝 Log do webhook PIX registrado")

    } catch (dbError) {
      console.error('❌ Erro ao conectar com banco de dados:', dbError)
      return NextResponse.json(
        { error: 'Erro interno no servidor' },
        { status: 500 }
      )
    }

    // Buscar bilhete pelo transaction_id
    let bilhetes = []
    try {
      bilhetes = await executeQuery(`
        SELECT * FROM bilhetes
        WHERE transaction_id = ?
      `, [transaction_id])
    } catch (queryError) {
      console.error('❌ Erro ao consultar bilhetes:', queryError)
      return NextResponse.json(
        { error: 'Erro ao consultar bilhete' },
        { status: 500 }
      )
    }

    if (bilhetes.length === 0) {
      console.log('⚠️ Bilhete não encontrado para transaction_id:', transaction_id)
      return NextResponse.json(
        {
          message: 'Bilhete não encontrado',
          transaction_id: transaction_id
        },
        { status: 404 }
      )
    }

    const bilhete = bilhetes[0]
    console.log('🎫 Bilhete encontrado:', {
      id: bilhete.id,
      codigo: bilhete.codigo,
      status_atual: bilhete.status,
      valor: bilhete.valor_total
    })

    // Mapear status da API para status do sistema conforme documentação
    let novoStatus = bilhete.status
    let statusSistema = ''

    switch (status.toUpperCase()) {
      case 'PAID':
        novoStatus = 'pago'
        statusSistema = 'PAGO'
        console.log('🎉 PAGAMENTO CONFIRMADO!', {
          bilhete: bilhete.codigo,
          valor: amount,
          usuario: bilhete.usuario_nome
        })
        break
      case 'PENDING':
      case 'GENERATED':
      case 'WAITING':
        novoStatus = 'pendente'
        statusSistema = 'PENDENTE'
        console.log('⏳ Pagamento pendente:', transaction_id)
        break
      case 'FAILED':
        novoStatus = 'cancelado'
        statusSistema = 'CANCELADO'
        console.log('❌ Pagamento falhou:', transaction_id, error)
        break
      case 'PROCESSING':
        novoStatus = 'processando'
        statusSistema = 'PROCESSANDO'
        console.log('🔄 Pagamento em processamento:', transaction_id)
        break
      default:
        console.log('⚠️ Status desconhecido:', status)
        novoStatus = 'pendente'
        statusSistema = 'PENDENTE'
    }

    // Atualizar status do bilhete
    try {
      await executeQuery(`
        UPDATE bilhetes SET
        status = ?,
        end_to_end_id = ?,
        updated_at = NOW()
        WHERE transaction_id = ?
      `, [
        novoStatus,
        end_to_end_id || null,
        transaction_id
      ])

      console.log('✅ Status do bilhete atualizado:', {
        transaction_id,
        status_anterior: bilhete.status,
        status_novo: novoStatus
      })
    } catch (updateError) {
      console.error('❌ Erro ao atualizar bilhete:', updateError)
      return NextResponse.json(
        { error: 'Erro ao atualizar bilhete' },
        { status: 500 }
      )
    }

    // Registrar histórico do webhook
    try {
      // Criar tabela se não existir
      await executeQuery(`
        CREATE TABLE IF NOT EXISTS webhook_logs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          transaction_id VARCHAR(255),
          order_id VARCHAR(255),
          amount DECIMAL(10,2),
          status VARCHAR(50),
          end_to_end_id VARCHAR(255),
          webhook_data TEXT,
          processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_transaction_id (transaction_id),
          INDEX idx_order_id (order_id)
        )
      `)

      await executeQuery(`
        INSERT INTO webhook_logs (
          transaction_id,
          order_id,
          amount,
          status,
          end_to_end_id,
          webhook_data,
          processed_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        transaction_id,
        order_id || null,
        amount || null,
        status,
        end_to_end_id || null,
        JSON.stringify(webhookData)
      ])
    } catch (logError) {
      console.error('❌ Erro ao registrar webhook log:', logError)
      // Não falhar por causa do log
    }

    console.log('✅ Webhook processado com sucesso:', {
      transaction_id,
      status_anterior: bilhete.status,
      status_novo: novoStatus,
      bilhete_codigo: bilhete.codigo
    })

    return NextResponse.json({
      message: "Webhook PIX processado com sucesso",
      timestamp: new Date().toISOString(),
      status: statusSistema.toLowerCase(), // "pago", "pendente", "cancelado", etc.
      transaction_id: transaction_id,
      order_id: order_id,
      bilhete_codigo: bilhete.codigo,
      payment_status: statusSistema,
      previous_status: bilhete.status,
      amount: amount
    })

  } catch (error) {
    console.error('❌ Erro ao processar webhook PIX:', error)
    return NextResponse.json(
      {
        message: 'Erro interno no servidor',
        timestamp: new Date().toISOString(),
        status: 'error',
        error: 'Falha ao processar webhook PIX',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

// Endpoint para verificar se o webhook está funcionando
export async function GET() {
  try {
    await initializeDatabase()

    // Buscar alguns bilhetes recentes para mostrar status
    const bilhetesRecentes = await executeQuery(`
      SELECT codigo, status, valor_total, created_at
      FROM bilhetes
      ORDER BY created_at DESC
      LIMIT 3
    `)

    const statusCounts = await executeQuery(`
      SELECT status, COUNT(*) as count
      FROM bilhetes
      GROUP BY status
    `)

    return NextResponse.json({
      message: "Webhook PIX endpoint está funcionando",
      timestamp: new Date().toISOString(),
      status: "active",
      bilhetes_recentes: bilhetesRecentes.map(b => ({
        codigo: b.codigo,
        status: b.status,
        valor: b.valor_total,
        data: b.created_at
      })),
      estatisticas: statusCounts.reduce((acc, item) => {
        acc[item.status] = item.count
        return acc
      }, {}),
      endpoint_info: {
        url: "/api/pix/webhook",
        method: "POST",
        description: "Recebe webhooks de pagamento PIX"
      }
    })
  } catch (error) {
    return NextResponse.json({
      message: "Webhook PIX endpoint está funcionando",
      timestamp: new Date().toISOString(),
      status: "active",
      error: "Erro ao buscar estatísticas do banco"
    })
  }
}
