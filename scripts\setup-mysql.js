import { executeQuery, initializeDatabase } from '../lib/database.js'
import fs from 'fs'
import path from 'path'

async function setupMySQL() {
  try {
    console.log('🚀 Iniciando setup do MySQL...')
    
    // Inicializar cone<PERSON>
    await initializeDatabase()
    
    // Ler o arquivo SQL
    const sqlFile = path.join(process.cwd(), 'scripts', 'mysql-setup.sql')
    const sqlContent = fs.readFileSync(sqlFile, 'utf8')
    
    // Dividir em comandos individuais
    const commands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'))
    
    console.log(`📝 Executando ${commands.length} comandos SQL...`)
    
    for (let i = 0; i < commands.length; i++) {
      const command = commands[i]
      
      if (command.toLowerCase().includes('create database')) {
        console.log(`⏭️ Pulando comando CREATE DATABASE (${i + 1}/${commands.length})`)
        continue
      }
      
      if (command.toLowerCase().includes('use `')) {
        console.log(`⏭️ Pulando comando USE (${i + 1}/${commands.length})`)
        continue
      }
      
      try {
        console.log(`🔄 Executando comando ${i + 1}/${commands.length}...`)
        await executeQuery(command)
        console.log(`✅ Comando ${i + 1} executado com sucesso`)
      } catch (error) {
        if (error.message.includes('already exists') || error.message.includes('Duplicate entry')) {
          console.log(`⚠️ Comando ${i + 1} já existe, pulando...`)
        } else {
          console.error(`❌ Erro no comando ${i + 1}:`, error.message)
          console.error(`Comando: ${command.substring(0, 100)}...`)
        }
      }
    }
    
    console.log('✅ Setup do MySQL concluído!')
    
    // Verificar se as tabelas foram criadas
    console.log('🔍 Verificando tabelas criadas...')
    const tables = await executeQuery(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'sistema-bolao-top'
      ORDER BY TABLE_NAME
    `)
    
    console.log('📊 Tabelas encontradas:')
    tables.forEach(table => {
      console.log(`  ✅ ${table.TABLE_NAME}`)
    })
    
    // Verificar se há dados iniciais
    const usuarios = await executeQuery('SELECT COUNT(*) as count FROM usuarios')
    const campeonatos = await executeQuery('SELECT COUNT(*) as count FROM campeonatos')
    const times = await executeQuery('SELECT COUNT(*) as count FROM times')
    
    console.log('\n📈 Dados iniciais:')
    console.log(`  👥 Usuários: ${usuarios[0].count}`)
    console.log(`  🏆 Campeonatos: ${campeonatos[0].count}`)
    console.log(`  ⚽ Times: ${times[0].count}`)
    
    console.log('\n🎉 Setup completo! O sistema está pronto para usar.')
    
  } catch (error) {
    console.error('❌ Erro no setup do MySQL:', error)
    process.exit(1)
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  setupMySQL()
}

export default setupMySQL
