import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function POST(request: Request) {
  try {
    await initializeDatabase()
    
    // Logos dos times brasileiros mais conhecidos
    const teamLogos = [
      {
        name: '<PERSON><PERSON><PERSON><PERSON>',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/gremio-vector-logo.png'
      },
      {
        name: 'Internacional',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/internacional-vector-logo.png'
      },
      {
        name: 'Botafo<PERSON>',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/botafogo-vector-logo.png'
      },
      {
        name: 'Vasco',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/vasco-da-gama-vector-logo.png'
      },
      {
        name: 'Flame<PERSON>',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/flamengo-vector-logo.png'
      },
      {
        name: 'Fluminen<PERSON>',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/fluminense-vector-logo.png'
      },
      {
        name: 'Corinthians',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/corinthians-vector-logo.png'
      },
      {
        name: 'Palmeiras',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/palmeiras-vector-logo.png'
      },
      {
        name: 'São Paulo',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/sao-paulo-vector-logo.png'
      },
      {
        name: 'Santos',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/santos-vector-logo.png'
      },
      {
        name: 'Atlético Mineiro',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/atletico-mineiro-vector-logo.png'
      },
      {
        name: 'Cruzeiro',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/cruzeiro-vector-logo.png'
      }
    ]
    
    let updatedCount = 0
    
    for (const team of teamLogos) {
      try {
        const result = await executeQuery(`
          UPDATE times 
          SET logo_url = ? 
          WHERE nome LIKE ? OR nome_curto LIKE ?
        `, [team.logo, `%${team.name}%`, `%${team.name}%`])
        
        if (result.affectedRows > 0) {
          console.log(`✅ Logo atualizado para ${team.name}`)
          updatedCount++
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar logo do ${team.name}:`, error)
      }
    }
    
    // Atualizar logos de campeonatos também
    const competitionLogos = [
      {
        name: 'Brasileirão',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/cbf-vector-logo.png'
      },
      {
        name: 'Copa do Brasil',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/cbf-vector-logo.png'
      },
      {
        name: 'Libertadores',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/conmebol-vector-logo.png'
      },
      {
        name: 'Sul-Americana',
        logo: 'https://logoeps.com/wp-content/uploads/2013/03/conmebol-vector-logo.png'
      }
    ]
    
    let competitionsUpdated = 0
    
    for (const comp of competitionLogos) {
      try {
        const result = await executeQuery(`
          UPDATE campeonatos 
          SET logo_url = ? 
          WHERE nome LIKE ?
        `, [comp.logo, `%${comp.name}%`])
        
        if (result.affectedRows > 0) {
          console.log(`✅ Logo do campeonato atualizado para ${comp.name}`)
          competitionsUpdated++
        }
      } catch (error) {
        console.error(`❌ Erro ao atualizar logo do campeonato ${comp.name}:`, error)
      }
    }
    
    return NextResponse.json({
      success: true,
      message: `Logos atualizados com sucesso`,
      teamsUpdated: updatedCount,
      competitionsUpdated: competitionsUpdated
    })
    
  } catch (error) {
    console.error("❌ Erro ao atualizar logos:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error).message
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    await initializeDatabase()
    
    // Buscar times sem logo
    const teamsWithoutLogo = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE logo_url IS NULL OR logo_url = ''
      ORDER BY nome
    `)
    
    // Buscar times com logo
    const teamsWithLogo = await executeQuery(`
      SELECT id, nome, nome_curto, logo_url 
      FROM times 
      WHERE logo_url IS NOT NULL AND logo_url != ''
      ORDER BY nome
    `)
    
    // Buscar campeonatos sem logo
    const competitionsWithoutLogo = await executeQuery(`
      SELECT id, nome, logo_url 
      FROM campeonatos 
      WHERE logo_url IS NULL OR logo_url = ''
      ORDER BY nome
    `)
    
    // Buscar campeonatos com logo
    const competitionsWithLogo = await executeQuery(`
      SELECT id, nome, logo_url 
      FROM campeonatos 
      WHERE logo_url IS NOT NULL AND logo_url != ''
      ORDER BY nome
    `)
    
    return NextResponse.json({
      success: true,
      stats: {
        teamsWithoutLogo: teamsWithoutLogo.length,
        teamsWithLogo: teamsWithLogo.length,
        competitionsWithoutLogo: competitionsWithoutLogo.length,
        competitionsWithLogo: competitionsWithLogo.length
      },
      data: {
        teamsWithoutLogo: teamsWithoutLogo.slice(0, 10), // Primeiros 10
        teamsWithLogo: teamsWithLogo.slice(0, 10), // Primeiros 10
        competitionsWithoutLogo,
        competitionsWithLogo
      }
    })
    
  } catch (error) {
    console.error("❌ Erro ao buscar dados de logos:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        message: (error as Error).message
      },
      { status: 500 }
    )
  }
}
