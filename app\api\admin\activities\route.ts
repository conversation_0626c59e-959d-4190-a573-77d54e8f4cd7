import { NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    await initializeDatabase()

    console.log("📊 Buscando atividades recentes...")

    // Buscar atividades recentes de diferentes tipos
    const [bilhetes, pagamentos, usuarios, apostas] = await Promise.all([
      // Bilhetes recentes
      executeQuery(`
        SELECT 
          'bet' as type,
          usuario_nome as user,
          valor_total as amount,
          CONCAT('Nova aposta de R$ ', FORMAT(valor_total, 2)) as description,
          created_at as time
        FROM bilhetes 
        ORDER BY created_at DESC 
        LIMIT 5
      `).catch(() => []),

      // Pagamentos recentes
      executeQuery(`
        SELECT 
          'payment' as type,
          usuario_nome as user,
          valor_total as amount,
          CONCAT('Pagamento confirmado de R$ ', FORMAT(valor_total, 2)) as description,
          updated_at as time
        FROM bilhetes 
        WHERE status = 'pago'
        ORDER BY updated_at DESC 
        LIMIT 5
      `).catch(() => []),

      // Usuários recentes
      executeQuery(`
        SELECT 
          'user' as type,
          nome as user,
          NULL as amount,
          CONCAT('Novo usuário cadastrado: ', nome) as description,
          data_cadastro as time
        FROM usuarios 
        ORDER BY data_cadastro DESC 
        LIMIT 5
      `).catch(() => []),

      // Apostas ganhadoras (simuladas por enquanto)
      executeQuery(`
        SELECT 
          'win' as type,
          usuario_nome as user,
          valor_total * 2 as amount,
          CONCAT('Aposta ganhadora! Prêmio: R$ ', FORMAT(valor_total * 2, 2)) as description,
          created_at as time
        FROM bilhetes 
        WHERE status = 'pago' 
        ORDER BY created_at DESC 
        LIMIT 3
      `).catch(() => [])
    ])

    // Combinar todas as atividades
    const allActivities = [
      ...bilhetes.map((item: any, index: number) => ({ ...item, id: `bet_${index}` })),
      ...pagamentos.map((item: any, index: number) => ({ ...item, id: `payment_${index}` })),
      ...usuarios.map((item: any, index: number) => ({ ...item, id: `user_${index}` })),
      ...apostas.map((item: any, index: number) => ({ ...item, id: `win_${index}` }))
    ]

    // Ordenar por tempo (mais recente primeiro) e limitar a 20
    const sortedActivities = allActivities
      .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
      .slice(0, 20)
      .map((activity, index) => ({
        id: index + 1,
        type: activity.type,
        user: activity.user || 'Sistema',
        amount: activity.amount ? parseFloat(activity.amount) : undefined,
        description: activity.description,
        time: formatTimeAgo(activity.time)
      }))

    return NextResponse.json({
      success: true,
      activities: sortedActivities,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro ao buscar atividades:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Erro interno do servidor",
        activities: [],
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

function formatTimeAgo(dateString: string): string {
  const now = new Date()
  const date = new Date(dateString)
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return 'Agora mesmo'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes} min atrás`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}h atrás`
  } else {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}d atrás`
  }
}
